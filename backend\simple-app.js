const express = require('express');
const cors = require('cors');
const path = require('path');

console.log('🔍 Starting simple app...');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '.env') });

const app = express();
const PORT = process.env.PORT || 3001;

console.log('✅ Environment loaded, PORT:', PORT);

// 中间件配置
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

console.log('✅ Middleware configured');

// 基础路由
app.get('/', (req, res) => {
    console.log('📍 Root route accessed');
    res.json({
        message: '法务案件管理平台 API',
        version: '1.0.0',
        status: 'running'
    });
});

// 健康检查（不连接数据库）
app.get('/health', (req, res) => {
    console.log('📍 Health check accessed');
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString()
    });
});

// 404 处理
app.use('*', (req, res) => {
    console.log('📍 404 route:', req.originalUrl);
    res.status(404).json({
        error: 'API endpoint not found',
        path: req.originalUrl,
        method: req.method
    });
});

// 全局错误处理
app.use((error, req, res, next) => {
    console.error('❌ Global error handler:', error);
    res.status(error.status || 500).json({
        error: error.message || 'Internal server error'
    });
});

// 启动服务器
console.log('🚀 Starting server...');
const server = app.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 Server is running on port ${PORT}`);
    console.log(`📍 API URL: http://localhost:${PORT}`);
    console.log(`🏥 Health check: http://localhost:${PORT}/health`);
});

server.on('error', (error) => {
    console.error('❌ Server error:', error);
    if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use`);
    }
});

server.on('listening', () => {
    console.log('✅ Server is listening');
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('🔄 SIGTERM received, shutting down gracefully...');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('🔄 SIGINT received, shutting down gracefully...');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});

console.log('✅ Simple app setup complete');
