import api from './api';

/**
 * 负责人管理API服务
 */
export const responsiblesAPI = {
  /**
   * 获取负责人列表
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getResponsibles: (params = {}) => {
    return api.get('/responsibles', { params });
  },

  /**
   * 获取启用的负责人列表（用于下拉选择）
   * @returns {Promise} API响应
   */
  getActiveResponsibles: () => {
    return api.get('/responsibles/active');
  },

  /**
   * 获取负责人详情
   * @param {number} id - 负责人ID
   * @returns {Promise} API响应
   */
  getResponsible: (id) => {
    return api.get(`/responsibles/${id}`);
  },

  /**
   * 创建负责人
   * @param {Object} data - 负责人数据
   * @returns {Promise} API响应
   */
  createResponsible: (data) => {
    return api.post('/responsibles', data);
  },

  /**
   * 更新负责人信息
   * @param {number} id - 负责人ID
   * @param {Object} data - 更新数据
   * @returns {Promise} API响应
   */
  updateResponsible: (id, data) => {
    return api.put(`/responsibles/${id}`, data);
  },

  /**
   * 删除负责人
   * @param {number} id - 负责人ID
   * @returns {Promise} API响应
   */
  deleteResponsible: (id) => {
    return api.delete(`/responsibles/${id}`);
  },

  /**
   * 批量更新负责人排序
   * @param {Array} responsibles - 负责人排序数据
   * @returns {Promise} API响应
   */
  updateSort: (responsibles) => {
    return api.put('/responsibles/batch/sort', { responsibles });
  }
};

export default responsiblesAPI;
