const http = require('http');

function makeRequest(options, data) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    body: body
                });
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(data);
        }
        req.end();
    });
}

async function login() {
    console.log('🔐 登录获取token...');
    
    const loginData = JSON.stringify({
        username: 'admin',
        password: 'admin123'
    });

    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(loginData)
        }
    };

    try {
        const response = await makeRequest(options, loginData);
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 登录成功');
            return data.token;
        } else {
            console.log('❌ 登录失败');
            return null;
        }
    } catch (error) {
        console.error('❌ 登录错误:', error.message);
        return null;
    }
}

async function testOverviewStats(token) {
    console.log('\n📊 测试统计总览...');
    
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/stats/overview',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options);
        console.log('状态码:', response.statusCode);
        
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 统计总览获取成功');
            console.log('总案件数:', data.overview.totalCases);
            console.log('本月新增:', data.overview.newCasesThisMonth);
            console.log('即将到期:', data.overview.upcomingCases);
            console.log('未读通知:', data.overview.unreadNotifications);
            
            if (data.overview.totalUsers) {
                console.log('总用户数:', data.overview.totalUsers);
                console.log('总文件数:', data.overview.totalFiles);
            }
            
            console.log('按状态统计:', data.casesByStatus);
            console.log('按类型统计:', data.casesByType);
            console.log('按优先级统计:', data.casesByPriority);
        } else {
            console.log('❌ 统计总览获取失败');
            console.log('响应:', response.body);
        }
    } catch (error) {
        console.error('❌ 统计总览错误:', error.message);
    }
}

async function testCaseStats(token) {
    console.log('\n📈 测试案件统计...');
    
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/stats/cases?period=month',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options);
        console.log('状态码:', response.statusCode);
        
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 案件统计获取成功');
            console.log('统计周期:', data.period);
            console.log('案件趋势数据点:', data.caseTrend.length);
            console.log('平均处理天数:', data.processingTime.avgDays);
            console.log('负责人工作量:', data.ownerWorkload.length, '人');
            console.log('金额统计:');
            console.log('  - 总金额:', data.amountStats.total);
            console.log('  - 平均金额:', data.amountStats.average);
            console.log('  - 案件数量:', data.amountStats.count);
        } else {
            console.log('❌ 案件统计获取失败');
            console.log('响应:', response.body);
        }
    } catch (error) {
        console.error('❌ 案件统计错误:', error.message);
    }
}

async function testActivityStats(token) {
    console.log('\n🏃 测试用户活动统计...');
    
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/stats/activity?period=week',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options);
        console.log('状态码:', response.statusCode);
        
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 用户活动统计获取成功');
            console.log('统计周期:', data.period);
            console.log('用户ID:', data.user_id);
            console.log('活动日志数据点:', data.activityLogs.length);
            console.log('模块活动统计:', data.moduleActivity);
            console.log('最近案件操作:', data.recentCaseActivity.length, '条');
        } else {
            console.log('❌ 用户活动统计获取失败');
            console.log('响应:', response.body);
        }
    } catch (error) {
        console.error('❌ 用户活动统计错误:', error.message);
    }
}

async function testSystemStats(token) {
    console.log('\n🖥️ 测试系统统计...');
    
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/stats/system',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options);
        console.log('状态码:', response.statusCode);
        
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 系统统计获取成功');
            console.log('数据库表统计:', data.tableStats);
            console.log('最近活动数据点:', data.recentActivity.length);
            console.log('错误统计:', data.errorStats);
            console.log('文件统计:');
            console.log('  - 总文件数:', data.fileStats.totalFiles);
            console.log('  - 总大小:', data.fileStats.totalSize, '字节');
            console.log('  - 平均大小:', data.fileStats.avgSize, '字节');
        } else {
            console.log('❌ 系统统计获取失败');
            console.log('响应:', response.body);
        }
    } catch (error) {
        console.error('❌ 系统统计错误:', error.message);
    }
}

async function main() {
    console.log('🧪 开始测试统计功能...\n');
    
    const token = await login();
    if (!token) {
        console.log('❌ 无法获取token，测试终止');
        return;
    }
    
    await testOverviewStats(token);
    await testCaseStats(token);
    await testActivityStats(token);
    await testSystemStats(token);
    
    console.log('\n✅ 统计功能测试完成');
}

main();
