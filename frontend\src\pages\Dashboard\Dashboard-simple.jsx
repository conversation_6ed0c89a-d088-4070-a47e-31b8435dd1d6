import React from 'react';
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, But<PERSON> } from 'antd';
import { PlusOutlined, FileTextOutlined, CheckCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const Dashboard = () => {
  const navigate = useNavigate();

  // 模拟统计数据
  const stats = {
    totalCases: 0,
    activeCases: 0,
    closedCases: 0,
    pendingCases: 0
  };

  return (
    <div>
      <h1 style={{ marginBottom: '24px', fontSize: '24px', fontWeight: '600' }}>仪表板</h1>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '32px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card hoverable>
            <Statistic
              title="总案件数"
              value={stats.totalCases}
              prefix={<FileTextOutlined style={{ color: '#1890ff' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card hoverable>
            <Statistic
              title="进行中"
              value={stats.activeCases}
              prefix={<ClockCircleOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card hoverable>
            <Statistic
              title="已完成"
              value={stats.closedCases}
              prefix={<CheckCircleOutlined style={{ color: '#13c2c2' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card hoverable>
            <Statistic
              title="待处理"
              value={stats.pendingCases}
              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card
        title="快速操作"
        style={{ marginBottom: '32px' }}
        bodyStyle={{ padding: '24px' }}
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <Button
              type="primary"
              block
              size="large"
              icon={<PlusOutlined />}
              onClick={() => navigate('/cases/create')}
              style={{ height: '48px' }}
            >
              创建新案件
            </Button>
          </Col>
          <Col xs={24} sm={8}>
            <Button
              block
              size="large"
              onClick={() => navigate('/cases')}
              style={{ height: '48px' }}
            >
              查看所有案件
            </Button>
          </Col>
          <Col xs={24} sm={8}>
            <Button
              block
              size="large"
              onClick={() => navigate('/statistics')}
              style={{ height: '48px' }}
            >
              查看统计报表
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 最近案件 */}
      <Card
        title="最近案件"
        bodyStyle={{ padding: '24px' }}
      >
        <Alert
          message="暂无案件数据"
          description="点击上方按钮开始创建您的第一个案件"
          type="info"
          showIcon
          style={{ borderRadius: '8px' }}
        />
      </Card>
    </div>
  );
};

export default Dashboard;
