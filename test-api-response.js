const http = require('http');

// 测试登录API
function testLogin() {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify({
            username: 'admin',
            password: 'admin123'
        });

        const options = {
            hostname: 'localhost',
            port: 8001,
            path: '/api/auth/login',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    console.log('🔐 登录API响应:', response);
                    resolve(response);
                } catch (error) {
                    console.error('❌ 登录API响应解析失败:', error);
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            console.error('❌ 登录API请求失败:', error);
            reject(error);
        });

        req.write(postData);
        req.end();
    });
}

// 测试案件列表API
function testCasesAPI(token) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 8001,
            path: '/api/cases',
            method: 'GET',
            headers: {
                'Authorization': token ? `Bearer ${token}` : '',
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    console.log('📋 案件列表API响应:', JSON.stringify(response, null, 2));
                    resolve(response);
                } catch (error) {
                    console.error('❌ 案件列表API响应解析失败:', error);
                    console.log('原始响应:', data);
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            console.error('❌ 案件列表API请求失败:', error);
            reject(error);
        });

        req.end();
    });
}

// 测试无认证的案件列表API
function testCasesAPINoAuth() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 8001,
            path: '/api/cases',
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                console.log('📋 无认证案件列表API状态码:', res.statusCode);
                console.log('📋 无认证案件列表API响应:', data);
                resolve({ statusCode: res.statusCode, data });
            });
        });

        req.on('error', (error) => {
            console.error('❌ 无认证案件列表API请求失败:', error);
            reject(error);
        });

        req.end();
    });
}

// 主测试函数
async function runTests() {
    console.log('🧪 开始API测试...\n');

    try {
        // 测试无认证的案件列表API
        console.log('1. 测试无认证案件列表API:');
        await testCasesAPINoAuth();
        console.log('\n');

        // 测试登录API
        console.log('2. 测试登录API:');
        const loginResponse = await testLogin();
        console.log('\n');

        // 如果登录成功，测试带认证的案件列表API
        if (loginResponse.token) {
            console.log('3. 测试带认证的案件列表API:');
            await testCasesAPI(loginResponse.token);
        } else {
            console.log('3. 登录失败，跳过认证测试');
        }

    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error);
    }
}

// 运行测试
runTests();
