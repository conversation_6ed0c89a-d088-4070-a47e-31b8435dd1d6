const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');

console.log('🔍 Starting monitored server...');
console.log('📊 Process ID:', process.pid);
console.log('📊 Node version:', process.version);
console.log('📊 Platform:', process.platform);

// 详细的进程事件监听
process.on('exit', (code) => {
    console.log(`🔄 Process exit with code: ${code}`);
});

process.on('beforeExit', (code) => {
    console.log(`🔄 Before exit with code: ${code}`);
});

process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    console.error('Stack:', error.stack);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise);
    console.error('Reason:', reason);
    process.exit(1);
});

process.on('SIGTERM', () => {
    console.log('🔄 SIGTERM received');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('🔄 SIGINT received');
    process.exit(0);
});

process.on('SIGHUP', () => {
    console.log('🔄 SIGHUP received');
});

process.on('SIGBREAK', () => {
    console.log('🔄 SIGBREAK received');
});

const app = express();
const PORT = 3006; // 使用确认可用的端口

console.log('✅ Express app created');

// 中间件配置
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

console.log('✅ Middleware configured');

// 基础路由
app.get('/', (req, res) => {
    console.log('📍 Root route accessed');
    res.json({
        message: '法务案件管理平台 API - Monitored Mode',
        version: '1.0.0',
        status: 'running',
        pid: process.pid,
        uptime: process.uptime()
    });
});

app.get('/health', (req, res) => {
    console.log('📍 Health check accessed');
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        process: {
            pid: process.pid,
            uptime: process.uptime(),
            memory: process.memoryUsage()
        }
    });
});

// 简化登录API
app.post('/api/auth/login', (req, res) => {
    console.log('📍 Login attempt:', req.body);
    const { username, password } = req.body;

    try {
        // 验证输入
        if (!username || !password) {
            return res.status(400).json({
                success: false,
                error: 'Username and password are required'
            });
        }

        // 简单验证
        if (username === 'admin' && password === 'admin123') {
            const token = jwt.sign(
                { id: 1, username: 'admin', role: 'admin' },
                'test-secret-key',
                { expiresIn: '24h' }
            );

            res.json({
                success: true,
                token: token,
                data: {
                    token,
                    user: {
                        id: 1,
                        username: 'admin',
                        real_name: '系统管理员',
                        email: '<EMAIL>',
                        roles: [{ name: 'admin' }]
                    }
                }
            });
        } else {
            res.status(401).json({
                success: false,
                error: 'Invalid credentials'
            });
        }
    } catch (error) {
        console.error('❌ Login error:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error'
        });
    }
});

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('❌ Express error middleware:', error);
    res.status(500).json({
        error: error.message || 'Internal server error'
    });
});

// 404处理
app.use('*', (req, res) => {
    console.log('📍 404 route:', req.originalUrl);
    res.status(404).json({
        error: 'API endpoint not found',
        path: req.originalUrl,
        method: req.method
    });
});

console.log('🚀 Starting server...');

// 启动服务器
const server = app.listen(PORT, () => {
    console.log(`✅ Monitored server is running on port ${PORT}`);
    console.log(`📍 API URL: http://localhost:${PORT}`);
    console.log(`🏥 Health check: http://localhost:${PORT}/health`);
    console.log(`🔑 Login API: http://localhost:${PORT}/api/auth/login`);
    
    // 定期输出状态
    setInterval(() => {
        console.log(`📊 Server alive - PID: ${process.pid}, Uptime: ${Math.floor(process.uptime())}s`);
    }, 10000); // 每10秒输出一次
});

server.on('error', (error) => {
    console.error('❌ Server error event:', error);
    process.exit(1);
});

server.on('close', () => {
    console.log('🔒 Server closed');
});

console.log('✅ Monitored server setup complete');

module.exports = app;
