import React, { useState, useEffect } from 'react';
import {
  Typography,
  Card,
  Row,
  Col,
  Tag,
  Button,
  Space,
  Descriptions,
  Timeline,
  Table,
  Modal,
  Form,
  Select,
  Input,
  message,
  Spin,
  Tooltip,
  Popconfirm,
  Divider,
  DatePicker,
  InputNumber,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  CalendarOutlined,
  FileTextOutlined,
  HistoryOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';

import { casesAPI, usersAPI, CASE_CONSTANTS } from '../../services/cases';
import { downloadRequest } from '../../services/api';
import { getUser } from '../../utils/auth';
import errorHandler from '../../utils/errorHandler';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const CaseDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();

  // 状态管理
  const [loading, setLoading] = useState(true);
  const [caseData, setCaseData] = useState(null);
  const [users, setUsers] = useState([]);
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [assignModalVisible, setAssignModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [statusLoading, setStatusLoading] = useState(false);
  const [assignLoading, setAssignLoading] = useState(false);
  const [editLoading, setEditLoading] = useState(false);

  const currentUser = getUser();

  // 获取案件详情
  const fetchCaseDetail = async () => {
    setLoading(true);
    try {
      const response = await casesAPI.getCaseDetail(id);
      console.log('案件详情API响应:', response.data); // 调试日志

      // 修复：正确的响应数据结构检查
      if (response.data && response.data.success && response.data.data && response.data.data.case) {
        setCaseData(response.data.data.case);
        console.log('案件数据设置成功:', response.data.data.case); // 调试日志
      } else {
        console.error('响应数据格式不正确:', response.data);
        throw new Error('响应数据格式不正确');
      }
    } catch (error) {
      errorHandler.handleApiError(error, {
        showMessage: true,
        // 移除customMessage，让错误处理器使用后端返回的具体错误消息
        retryable: true,
        onRetry: fetchCaseDetail
      });
      navigate('/cases');
    } finally {
      setLoading(false);
    }
  };

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      const response = await usersAPI.getUsers();
      if (response.data && response.data.users) {
        setUsers(response.data.users);
      }
    } catch (error) {
      errorHandler.handleApiError(error, {
        showMessage: true,
        customMessage: '获取用户列表失败，请稍后重试'
      });
    }
  };

  // 初始化
  useEffect(() => {
    if (id) {
      fetchCaseDetail();
      fetchUsers();
    }
  }, [id]);

  // 更新案件状态
  const handleStatusUpdate = async (values) => {
    setStatusLoading(true);
    try {
      await casesAPI.updateCaseStatus(id, values);
      message.success('案件状态更新成功');
      setStatusModalVisible(false);
      form.resetFields();
      fetchCaseDetail(); // 重新获取数据
    } catch (error) {
      errorHandler.handleApiError(error, {
        showMessage: true,
        customMessage: '更新案件状态失败，请稍后重试',
        retryable: true,
        onRetry: () => handleStatusUpdate(values)
      });
    } finally {
      setStatusLoading(false);
    }
  };

  // 分配案件负责人
  const handleAssign = async (values) => {
    setAssignLoading(true);
    try {
      await casesAPI.assignCase(id, values);
      message.success('案件分配成功');
      setAssignModalVisible(false);
      form.resetFields();
      fetchCaseDetail(); // 重新获取数据
    } catch (error) {
      errorHandler.handleApiError(error, {
        showMessage: true,
        customMessage: '分配案件失败，请稍后重试',
        retryable: true,
        onRetry: () => handleAssign(values)
      });
    } finally {
      setAssignLoading(false);
    }
  };

  // 删除案件
  const handleDelete = async () => {
    try {
      await casesAPI.deleteCase(id);
      message.success('案件删除成功');
      navigate('/cases');
    } catch (error) {
      errorHandler.handleApiError(error, {
        showMessage: true,
        customMessage: '删除案件失败，请稍后重试'
      });
    }
  };

  // 下载文件
  const handleDownload = async (file) => {
    try {
      await downloadRequest(`/files/download/${file.id}`, file.original_name);
      message.success('文件下载成功');
    } catch (error) {
      errorHandler.handleApiError(error, {
        showMessage: true,
        customMessage: '文件下载失败，请稍后重试',
        retryable: true,
        onRetry: () => handleDownload(file)
      });
    }
  };

  // 编辑案件
  const handleEdit = async (values) => {
    setEditLoading(true);
    try {
      // 处理日期格式
      const submitData = {
        ...values,
        deadline: values.deadline ? values.deadline.format('YYYY-MM-DD') : null,
      };

      await casesAPI.updateCase(id, submitData);
      message.success('案件信息更新成功');
      setEditModalVisible(false);
      form.resetFields();
      fetchCaseDetail(); // 重新获取数据
    } catch (error) {
      errorHandler.handleApiError(error, {
        showMessage: true,
        customMessage: '更新案件信息失败，请稍后重试',
        retryable: true,
        onRetry: () => handleEdit(values)
      });
    } finally {
      setEditLoading(false);
    }
  };

  // 打开编辑模态框
  const handleOpenEdit = () => {
    if (caseData) {
      form.setFieldsValue({
        title: caseData.title,
        type: caseData.type,
        description: caseData.description,
        priority: caseData.priority,
        deadline: caseData.deadline ? dayjs(caseData.deadline) : null,
        amount: caseData.amount,
        client_name: caseData.client_name,
        client_contact: caseData.client_contact,
      });
    }
    setEditModalVisible(true);
  };

  // 检查权限
  const canEdit = () => {
    if (!currentUser || !caseData) return false;
    const userRoles = currentUser.roles?.map(role => role.name) || [];
    return userRoles.includes('admin') || caseData.owner_id === currentUser.id;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!caseData) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Text type="secondary">案件不存在或已被删除</Text>
      </div>
    );
  }

  // 文件列表表格列定义
  const fileColumns = [
    {
      title: '文件名',
      dataIndex: 'original_name',
      key: 'original_name',
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '文件类型',
      dataIndex: 'file_category',
      key: 'file_category',
      width: 100,
      render: (category) => (
        <Tag color="blue">{category}</Tag>
      ),
    },
    {
      title: '文件大小',
      dataIndex: 'file_size',
      key: 'file_size',
      width: 100,
      render: (size) => {
        if (size < 1024) return `${size} B`;
        if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
        return `${(size / (1024 * 1024)).toFixed(1)} MB`;
      },
    },
    {
      title: '上传人',
      dataIndex: ['uploader', 'real_name'],
      key: 'uploader',
      width: 100,
      render: (text, record) => text || record.uploader?.username || '-',
    },
    {
      title: '上传时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date) => dayjs(date).format('MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, record) => (
        <Button
          type="text"
          size="small"
          icon={<DownloadOutlined />}
          onClick={() => handleDownload(record)}
        >
          下载
        </Button>
      ),
    },
  ];

  return (
    <div>
      {/* 页面标题和操作按钮 */}
      <div style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => navigate('/cases')}
              >
                返回列表
              </Button>
              <Title level={2} style={{ margin: 0 }}>
                案件详情
              </Title>
              <Tag color={CASE_CONSTANTS.STATUS_COLORS[caseData.status] || 'default'}>
                {caseData.status}
              </Tag>
            </Space>
          </Col>
          <Col>
            {canEdit() && (
              <Space>
                <Button
                  icon={<EditOutlined />}
                  onClick={handleOpenEdit}
                >
                  编辑案件
                </Button>
                <Button
                  onClick={() => setStatusModalVisible(true)}
                >
                  更新状态
                </Button>
                <Button
                  onClick={() => setAssignModalVisible(true)}
                >
                  分配负责人
                </Button>
                <Popconfirm
                  title="确定要删除这个案件吗？"
                  description="案件将移入回收站，30天后自动永久删除。"
                  onConfirm={handleDelete}
                  okText="确定"
                  cancelText="取消"
                  icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
                >
                  <Button danger icon={<DeleteOutlined />}>
                    删除案件
                  </Button>
                </Popconfirm>
              </Space>
            )}
          </Col>
        </Row>
      </div>

      {/* 案件基本信息 */}
      <Card title={<><FileTextOutlined /> 案件基本信息</>} style={{ marginBottom: 24 }}>
        <Descriptions column={{ xs: 1, sm: 2, md: 3 }} bordered>
          <Descriptions.Item label="案件编号">
            <Text copyable>{caseData.case_no}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="案件标题">
            {caseData.title}
          </Descriptions.Item>
          <Descriptions.Item label="案件类型">
            <Tag color="blue">{caseData.type}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="案件状态">
            <Tag color={CASE_CONSTANTS.STATUS_COLORS[caseData.status] || 'default'}>
              {caseData.status}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="优先级">
            <Tag color={CASE_CONSTANTS.PRIORITY_COLORS[caseData.priority] || 'default'}>
              {caseData.priority}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="负责人">
            <Space>
              <UserOutlined />
              {caseData.owner?.real_name || caseData.owner?.username || '-'}
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label="客户名称">
            {caseData.client_name || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="客户联系方式">
            {caseData.client_contact || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="涉案金额">
            {caseData.amount ? `¥${Number(caseData.amount).toLocaleString()}` : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="截止日期">
            <Space>
              <CalendarOutlined />
              {caseData.deadline ? (
                <span style={{
                  color: dayjs(caseData.deadline).isBefore(dayjs(), 'day') ? '#f5222d' : undefined
                }}>
                  {dayjs(caseData.deadline).format('YYYY-MM-DD')}
                </span>
              ) : '-'}
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {dayjs(caseData.created_at).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {dayjs(caseData.updated_at).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
          {caseData.description && (
            <Descriptions.Item label="案件描述" span={3}>
              <div style={{ whiteSpace: 'pre-wrap' }}>
                {caseData.description}
              </div>
            </Descriptions.Item>
          )}
        </Descriptions>
      </Card>

      <Row gutter={24}>
        {/* 操作历史 */}
        <Col xs={24} lg={12}>
          <Card
            title={<><HistoryOutlined /> 操作历史</>}
            style={{ marginBottom: 24 }}
            size="small"
          >
            {caseData.flows && caseData.flows.length > 0 ? (
              <Timeline
                items={caseData.flows.map((flow, index) => ({
                  color: index === 0 ? 'blue' : 'gray',
                  children: (
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
                        {flow.action}
                      </div>
                      <div style={{ color: '#666', fontSize: '12px', marginBottom: 4 }}>
                        操作人：{flow.operator?.real_name || flow.operator?.username || '系统'}
                      </div>
                      <div style={{ color: '#666', fontSize: '12px', marginBottom: 4 }}>
                        时间：{dayjs(flow.created_at).format('YYYY-MM-DD HH:mm:ss')}
                      </div>
                      {flow.remark && (
                        <div style={{ color: '#333', fontSize: '13px' }}>
                          备注：{flow.remark}
                        </div>
                      )}
                      {flow.old_value && flow.new_value && (
                        <div style={{ fontSize: '12px', color: '#999', marginTop: 4 }}>
                          <div>变更前：{flow.old_value}</div>
                          <div>变更后：{flow.new_value}</div>
                        </div>
                      )}
                    </div>
                  ),
                }))}
              />
            ) : (
              <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
                暂无操作历史
              </div>
            )}
          </Card>
        </Col>

        {/* 相关文件 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <UploadOutlined />
                相关文件
                {caseData.files && caseData.files.length > 0 && (
                  <Tag color="blue">{caseData.files.length}</Tag>
                )}
              </Space>
            }
            style={{ marginBottom: 24 }}
            size="small"
            extra={
              canEdit() && (
                <Button
                  type="primary"
                  size="small"
                  icon={<UploadOutlined />}
                  onClick={() => navigate(`/files?caseId=${id}`)}
                >
                  上传文件
                </Button>
              )
            }
          >
            {caseData.files && caseData.files.length > 0 ? (
              <Table
                columns={fileColumns}
                dataSource={caseData.files}
                rowKey="id"
                pagination={false}
                size="small"
                scroll={{ x: 400 }}
              />
            ) : (
              <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
                暂无相关文件
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 归档信息 */}
      {caseData.archive && (
        <Card title="归档信息" style={{ marginBottom: 24 }}>
          <Descriptions column={{ xs: 1, sm: 2, md: 3 }} bordered>
            <Descriptions.Item label="归档人">
              {caseData.archive.archivedBy?.real_name || caseData.archive.archivedBy?.username || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="归档时间">
              {dayjs(caseData.archive.created_at).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
            <Descriptions.Item label="归档原因">
              {caseData.archive.reason || '-'}
            </Descriptions.Item>
            {caseData.archive.remark && (
              <Descriptions.Item label="归档备注" span={3}>
                <div style={{ whiteSpace: 'pre-wrap' }}>
                  {caseData.archive.remark}
                </div>
              </Descriptions.Item>
            )}
          </Descriptions>
        </Card>
      )}

      {/* 状态更新模态框 */}
      <Modal
        title="更新案件状态"
        open={statusModalVisible}
        onCancel={() => {
          setStatusModalVisible(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleStatusUpdate}
        >
          <Form.Item
            label="新状态"
            name="status"
            rules={[{ required: true, message: '请选择新状态' }]}
          >
            <Select placeholder="请选择新状态">
              {Object.entries(CASE_CONSTANTS.STATUS).map(([key, value]) => (
                <Option key={key} value={value} disabled={value === caseData.status}>
                  <Tag color={CASE_CONSTANTS.STATUS_COLORS[value] || 'default'}>
                    {value}
                  </Tag>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="备注"
            name="remark"
          >
            <TextArea
              rows={3}
              placeholder="请输入状态变更备注..."
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setStatusModalVisible(false);
                form.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={statusLoading}>
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 分配负责人模态框 */}
      <Modal
        title="分配案件负责人"
        open={assignModalVisible}
        onCancel={() => {
          setAssignModalVisible(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleAssign}
        >
          <Form.Item
            label="新负责人"
            name="owner_id"
            rules={[{ required: true, message: '请选择新负责人' }]}
          >
            <Select
              placeholder="请选择新负责人"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {users.map(user => (
                <Option
                  key={user.id}
                  value={user.id}
                  disabled={user.id === caseData.owner_id}
                >
                  {user.real_name || user.username}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="分配备注"
            name="remark"
          >
            <TextArea
              rows={3}
              placeholder="请输入分配备注..."
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setAssignModalVisible(false);
                form.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={assignLoading}>
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑案件模态框 */}
      <Modal
        title="编辑案件信息"
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleEdit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="案件标题"
                name="title"
                rules={[{ required: true, message: '请输入案件标题' }]}
              >
                <Input placeholder="请输入案件标题" maxLength={200} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="案件类型"
                name="type"
                rules={[{ required: true, message: '请选择案件类型' }]}
              >
                <Select placeholder="请选择案件类型">
                  {Object.entries(CASE_CONSTANTS.TYPES).map(([key, value]) => (
                    <Select.Option key={key} value={value}>
                      {value}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="优先级"
                name="priority"
              >
                <Select placeholder="请选择优先级">
                  {Object.entries(CASE_CONSTANTS.PRIORITIES).map(([key, value]) => (
                    <Select.Option key={key} value={value}>
                      <Tag color={CASE_CONSTANTS.PRIORITY_COLORS[value] || 'default'}>
                        {value}
                      </Tag>
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="截止日期"
                name="deadline"
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="请选择截止日期"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="客户名称"
                name="client_name"
              >
                <Input placeholder="请输入客户名称" maxLength={100} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="客户联系方式"
                name="client_contact"
              >
                <Input placeholder="请输入客户联系方式" maxLength={100} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="涉案金额"
            name="amount"
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入涉案金额"
              min={0}
              precision={2}
              formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/¥\s?|(,*)/g, '')}
            />
          </Form.Item>

          <Form.Item
            label="案件描述"
            name="description"
          >
            <Input.TextArea
              rows={4}
              placeholder="请输入案件描述..."
              maxLength={1000}
              showCount
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setEditModalVisible(false);
                form.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={editLoading}>
                保存
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CaseDetail;
