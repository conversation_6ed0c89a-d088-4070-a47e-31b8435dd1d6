/**
 * 性能优化工具集
 */

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {boolean} immediate - 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait, immediate = false) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func.apply(this, args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func.apply(this, args);
  };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 图片懒加载
 * @param {string} selector - 图片选择器
 * @param {Object} options - 配置选项
 */
export function lazyLoadImages(selector = 'img[data-src]', options = {}) {
  const defaultOptions = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  };

  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.classList.remove('lazy');
          observer.unobserve(img);
        }
      });
    }, defaultOptions);

    document.querySelectorAll(selector).forEach(img => {
      imageObserver.observe(img);
    });
  } else {
    // 降级处理：直接加载所有图片
    document.querySelectorAll(selector).forEach(img => {
      img.src = img.dataset.src;
      img.classList.remove('lazy');
    });
  }
}

/**
 * 虚拟滚动实现
 */
export class VirtualScroll {
  constructor(container, itemHeight, renderItem, data) {
    this.container = container;
    this.itemHeight = itemHeight;
    this.renderItem = renderItem;
    this.data = data;
    this.visibleCount = Math.ceil(container.clientHeight / itemHeight) + 2;
    this.startIndex = 0;
    this.endIndex = this.visibleCount;
    
    this.init();
  }

  init() {
    this.container.style.position = 'relative';
    this.container.style.overflow = 'auto';
    
    // 创建虚拟容器
    this.virtualContainer = document.createElement('div');
    this.virtualContainer.style.height = `${this.data.length * this.itemHeight}px`;
    this.container.appendChild(this.virtualContainer);
    
    // 创建可见项容器
    this.visibleContainer = document.createElement('div');
    this.visibleContainer.style.position = 'absolute';
    this.visibleContainer.style.top = '0';
    this.visibleContainer.style.width = '100%';
    this.virtualContainer.appendChild(this.visibleContainer);
    
    this.render();
    this.bindEvents();
  }

  render() {
    this.visibleContainer.innerHTML = '';
    
    for (let i = this.startIndex; i < this.endIndex && i < this.data.length; i++) {
      const item = this.renderItem(this.data[i], i);
      item.style.position = 'absolute';
      item.style.top = `${i * this.itemHeight}px`;
      item.style.height = `${this.itemHeight}px`;
      this.visibleContainer.appendChild(item);
    }
  }

  bindEvents() {
    this.container.addEventListener('scroll', throttle(() => {
      const scrollTop = this.container.scrollTop;
      this.startIndex = Math.floor(scrollTop / this.itemHeight);
      this.endIndex = this.startIndex + this.visibleCount;
      this.render();
    }, 16)); // 60fps
  }

  updateData(newData) {
    this.data = newData;
    this.virtualContainer.style.height = `${this.data.length * this.itemHeight}px`;
    this.render();
  }
}

/**
 * 内存缓存
 */
export class MemoryCache {
  constructor(maxSize = 100) {
    this.cache = new Map();
    this.maxSize = maxSize;
  }

  get(key) {
    if (this.cache.has(key)) {
      // 更新访问时间
      const value = this.cache.get(key);
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return null;
  }

  set(key, value) {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size >= this.maxSize) {
      // 删除最久未使用的项
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }

  has(key) {
    return this.cache.has(key);
  }

  delete(key) {
    return this.cache.delete(key);
  }

  clear() {
    this.cache.clear();
  }

  size() {
    return this.cache.size;
  }
}

/**
 * 请求缓存装饰器
 * @param {number} ttl - 缓存时间（毫秒）
 * @param {MemoryCache} cache - 缓存实例
 */
export function withCache(ttl = 5 * 60 * 1000, cache = new MemoryCache()) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function(...args) {
      const cacheKey = `${propertyKey}_${JSON.stringify(args)}`;
      const cached = cache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < ttl) {
        return cached.data;
      }
      
      const result = await originalMethod.apply(this, args);
      cache.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      });
      
      return result;
    };
    
    return descriptor;
  };
}

/**
 * 组件懒加载
 * @param {Function} importFunc - 动态导入函数
 * @param {Object} fallback - 加载时的占位组件
 */
export function lazyComponent(importFunc, fallback = null) {
  return React.lazy(() => {
    return new Promise((resolve) => {
      // 最小加载时间，避免闪烁
      const minLoadTime = 200;
      const startTime = Date.now();
      
      importFunc().then((module) => {
        const elapsed = Date.now() - startTime;
        const remainingTime = Math.max(0, minLoadTime - elapsed);
        
        setTimeout(() => {
          resolve(module);
        }, remainingTime);
      });
    });
  });
}

/**
 * 性能监控
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.observers = [];
  }

  // 标记性能点
  mark(name) {
    if ('performance' in window && 'mark' in performance) {
      performance.mark(name);
    }
    this.metrics.set(name, Date.now());
  }

  // 测量性能
  measure(name, startMark, endMark) {
    if ('performance' in window && 'measure' in performance) {
      try {
        performance.measure(name, startMark, endMark);
        const measure = performance.getEntriesByName(name)[0];
        return measure.duration;
      } catch (error) {
        console.warn('Performance measure failed:', error);
      }
    }
    
    // 降级处理
    const start = this.metrics.get(startMark);
    const end = this.metrics.get(endMark);
    return end && start ? end - start : 0;
  }

  // 监控长任务
  observeLongTasks() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          console.warn('Long task detected:', {
            duration: entry.duration,
            startTime: entry.startTime,
            name: entry.name
          });
        });
      });
      
      try {
        observer.observe({ entryTypes: ['longtask'] });
        this.observers.push(observer);
      } catch (error) {
        console.warn('Long task observation not supported');
      }
    }
  }

  // 监控内存使用
  getMemoryUsage() {
    if ('memory' in performance) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      };
    }
    return null;
  }

  // 清理观察器
  disconnect() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

// 自动启动长任务监控
if (process.env.NODE_ENV === 'development') {
  performanceMonitor.observeLongTasks();
}
