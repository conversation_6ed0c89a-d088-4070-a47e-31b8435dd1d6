const mysql = require('mysql2/promise');
require('dotenv').config();

async function testMigration() {
    let connection = null;
    
    try {
        console.log('🔍 测试数据库迁移结果...');
        
        connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASS || '',
            database: process.env.DB_NAME || 'case_manager'
        });
        
        console.log('✅ 数据库连接成功');
        
        // 检查 deleted_at 字段
        const [columns] = await connection.execute(`
            SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                COLUMN_COMMENT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = ? 
              AND TABLE_NAME = 'cases' 
              AND COLUMN_NAME = 'deleted_at'
        `, [process.env.DB_NAME]);
        
        if (columns.length > 0) {
            console.log('✅ deleted_at 字段存在:', columns[0]);
        } else {
            console.log('❌ deleted_at 字段不存在');
            return false;
        }
        
        // 检查案件操作日志表
        const [tables] = await connection.execute(`
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = ? 
              AND TABLE_NAME = 'case_operation_logs'
        `, [process.env.DB_NAME]);
        
        if (tables.length > 0) {
            console.log('✅ case_operation_logs 表存在');
        } else {
            console.log('❌ case_operation_logs 表不存在');
            return false;
        }
        
        // 测试软删除功能
        console.log('🧪 测试软删除功能...');
        
        // 查询现有案件
        const [cases] = await connection.execute(`
            SELECT id, title, deleted_at FROM cases LIMIT 1
        `);
        
        if (cases.length > 0) {
            console.log('✅ 可以查询案件，deleted_at 字段正常');
        } else {
            console.log('ℹ️ 数据库中暂无案件数据');
        }
        
        console.log('🎉 数据库迁移测试通过！');
        return true;
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        return false;
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

testMigration()
    .then(success => {
        if (success) {
            console.log('✅ 数据库已准备就绪，可以启动服务器');
        } else {
            console.log('❌ 数据库未准备就绪，请检查迁移');
        }
        process.exit(success ? 0 : 1);
    })
    .catch(error => {
        console.error('测试脚本执行失败:', error);
        process.exit(1);
    });
