console.log('🔍 Starting server test...');

const express = require('express');
const cors = require('cors');

// 进程事件监听
process.on('exit', (code) => {
    console.log(`🔄 Process exit with code: ${code}`);
});

process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection:', reason);
    process.exit(1);
});

const app = express();
const PORT = 3007;

console.log('✅ Express app created');

// 中间件
app.use(cors());
app.use(express.json());

console.log('✅ Middleware configured');

// 简单路由
app.get('/', (req, res) => {
    console.log('📍 Root route accessed');
    res.json({
        message: 'Server test',
        timestamp: new Date().toISOString(),
        pid: process.pid
    });
});

app.get('/health', (req, res) => {
    console.log('📍 Health check accessed');
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString()
    });
});

console.log('✅ Routes configured');

// 启动服务器
console.log(`🚀 Starting server on port ${PORT}...`);

const server = app.listen(PORT, () => {
    console.log(`✅ Server is running on port ${PORT}`);
    console.log(`📍 URL: http://localhost:${PORT}`);
    console.log(`🏥 Health: http://localhost:${PORT}/health`);
    
    // 定期状态输出
    const statusInterval = setInterval(() => {
        console.log(`📊 Server status - PID: ${process.pid}, Uptime: ${Math.floor(process.uptime())}s`);
    }, 10000);
    
    // 清理定时器
    process.on('exit', () => {
        clearInterval(statusInterval);
    });
});

server.on('error', (error) => {
    console.error('❌ Server error:', error);
    if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use`);
    }
    process.exit(1);
});

server.on('listening', () => {
    console.log('✅ Server listening event fired');
});

server.on('close', () => {
    console.log('🔒 Server close event fired');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('🔄 SIGINT received, shutting down...');
    server.close(() => {
        console.log('✅ Server closed gracefully');
        process.exit(0);
    });
});

console.log('✅ Server test setup complete');
