import React, { useState } from 'react';
import { useN<PERSON><PERSON>, Link } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  Card,
  Typography,
  message,
  Space,
  Select,
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  MailOutlined,
  IdcardOutlined,
  UserAddOutlined,
} from '@ant-design/icons';

import { authAPI } from '../../services/auth';
import './Login.css'; // 复用登录页面样式

const { Title, Text } = Typography;
const { Option } = Select;

const Register = () => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const navigate = useNavigate();

  // 处理注册
  const handleRegister = async (values) => {
    setLoading(true);
    try {
      const response = await authAPI.register(values);
      
      message.success('注册成功！请使用新账户登录');
      navigate('/login');
    } catch (error) {
      console.error('注册失败:', error);
      // 错误信息已在 API 拦截器中处理
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-overlay" />
      </div>
      
      <div className="login-content">
        <Card className="login-card" bordered={false}>
          {/* 标题 */}
          <div className="login-header">
            <Title level={2} style={{ textAlign: 'center', marginBottom: 8 }}>
              用户注册
            </Title>
            <Text type="secondary" style={{ display: 'block', textAlign: 'center' }}>
              创建您的法务管理账户
            </Text>
          </div>

          {/* 注册表单 */}
          <Form
            form={form}
            name="register"
            onFinish={handleRegister}
            autoComplete="off"
            size="large"
            style={{ marginTop: 32 }}
            layout="vertical"
          >
            <Form.Item
              name="username"
              label="用户名"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3个字符' },
                { max: 20, message: '用户名最多20个字符' },
                { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="请输入用户名"
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="real_name"
              label="真实姓名"
              rules={[
                { required: true, message: '请输入真实姓名' },
                { min: 2, message: '姓名至少2个字符' },
                { max: 10, message: '姓名最多10个字符' },
              ]}
            >
              <Input
                prefix={<IdcardOutlined />}
                placeholder="请输入真实姓名"
                autoComplete="name"
              />
            </Form.Item>

            <Form.Item
              name="email"
              label="邮箱地址"
              rules={[
                { required: true, message: '请输入邮箱地址' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="请输入邮箱地址"
                autoComplete="email"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="密码"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' },
                { max: 20, message: '密码最多20个字符' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请输入密码"
                autoComplete="new-password"
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label="确认密码"
              dependencies={['password']}
              rules={[
                { required: true, message: '请确认密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="请再次输入密码"
                autoComplete="new-password"
              />
            </Form.Item>

            <Form.Item style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                icon={<UserAddOutlined />}
              >
                注册账户
              </Button>
            </Form.Item>
          </Form>

          {/* 登录链接 */}
          <div style={{ textAlign: 'center', marginTop: 16 }}>
            <Text type="secondary">
              已有账户？
              <Link to="/login" style={{ marginLeft: 8 }}>
                立即登录
              </Link>
            </Text>
          </div>

          {/* 系统信息 */}
          <div className="login-footer">
            <Text type="secondary" style={{ fontSize: '12px' }}>
              © 2025 Sie Dispute Manager v1.0.0
            </Text>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Register;
