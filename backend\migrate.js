#!/usr/bin/env node

/**
 * 数据库迁移脚本
 * 用法: node migrate.js
 */

const { runMigration } = require('./utils/migrate-database');

console.log('🚀 开始执行数据库迁移...');
console.log('这将为案件管理系统添加软删除功能支持');
console.log('');

runMigration()
    .then(() => {
        console.log('');
        console.log('🎉 数据库迁移成功完成！');
        console.log('现在可以启动服务器: node app.js');
        process.exit(0);
    })
    .catch((error) => {
        console.error('');
        console.error('❌ 数据库迁移失败:', error.message);
        console.error('');
        console.error('请检查以下项目:');
        console.error('1. 数据库连接配置是否正确 (.env 文件)');
        console.error('2. 数据库服务是否正在运行');
        console.error('3. 用户是否有足够的权限修改表结构');
        console.error('');
        process.exit(1);
    });
