const express = require('express');
const cors = require('cors');
const path = require('path');

console.log('🔍 启动案件详情测试服务器...');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '.env') });

const app = express();
const PORT = 8002; // 使用不同端口避免冲突

// 中间件配置
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

console.log('✅ 中间件配置完成');

// 模拟认证中间件
const mockAuth = (req, res, next) => {
    // 模拟用户信息
    req.user = {
        id: 1,
        username: 'testuser',
        roles: [{ name: 'admin' }]
    };
    next();
};

// 基础路由
app.get('/', (req, res) => {
    res.json({
        message: '案件详情测试服务器',
        version: '1.0.0',
        status: 'running',
        port: PORT
    });
});

// 健康检查
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString()
    });
});

// 案件详情API测试 - 模拟案件不存在的情况
app.get('/api/cases/:id', mockAuth, async (req, res) => {
    console.log('📍 获取案件详情请求 - ID:', req.params.id, '用户:', req.user?.id);
    
    try {
        const caseId = req.params.id;
        
        // 模拟案件不存在的情况
        if (caseId === '999' || caseId === 'nonexistent') {
            console.log('❌ 案件不存在:', caseId);
            return res.status(404).json({
                success: false,
                error: '案件不存在或已被删除',
                code: 'CASE_NOT_FOUND'
            });
        }
        
        // 模拟权限不足的情况
        if (caseId === '403') {
            console.log('❌ 权限不足:', req.user.id, '尝试访问案件:', caseId);
            return res.status(403).json({
                success: false,
                error: '权限不足：您只能查看自己的案件',
                code: 'CASE_ACCESS_DENIED'
            });
        }

        // 模拟成功返回案件详情
        const mockCaseData = {
            id: parseInt(caseId),
            case_no: `CASE2025${caseId.padStart(4, '0')}`,
            title: `测试案件 ${caseId}`,
            type: '合同纠纷',
            status: '待处理',
            priority: '中',
            description: '这是一个测试案件的描述',
            client_name: '测试客户',
            client_contact: '13800138000',
            amount: 100000,
            deadline: '2025-12-31',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            owner_id: 1,
            owner: {
                id: 1,
                username: 'testuser',
                real_name: '测试用户'
            },
            flows: [],
            files: [],
            archive: null
        };

        console.log('✅ 案件详情获取成功:', mockCaseData.case_no);
        res.json({
            success: true,
            data: {
                case: mockCaseData
            }
        });

    } catch (error) {
        console.error('❌ 获取案件详情失败:', error);
        res.status(500).json({
            success: false,
            error: '获取案件详情失败',
            code: 'GET_CASE_ERROR',
            message: error.message
        });
    }
});

// 404 处理
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'API endpoint not found',
        path: req.originalUrl,
        method: req.method
    });
});

// 启动服务器
app.listen(PORT, '127.0.0.1', () => {
    console.log(`🚀 测试服务器运行在端口 ${PORT}`);
    console.log(`📍 API URL: http://127.0.0.1:${PORT}`);
    console.log(`🏥 健康检查: http://127.0.0.1:${PORT}/health`);
    console.log(`📋 测试案例:`);
    console.log(`   - 正常案件: http://127.0.0.1:${PORT}/api/cases/1`);
    console.log(`   - 不存在案件: http://127.0.0.1:${PORT}/api/cases/999`);
    console.log(`   - 权限不足: http://127.0.0.1:${PORT}/api/cases/403`);
});

module.exports = app;
