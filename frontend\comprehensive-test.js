// 综合前后端联调测试脚本
import axios from 'axios';

const API_BASE = 'http://localhost:3000/api';

class ComprehensiveTest {
    constructor() {
        this.token = null;
        this.testResults = {
            auth: { passed: 0, failed: 0, tests: [] },
            cases: { passed: 0, failed: 0, tests: [] },
            files: { passed: 0, failed: 0, tests: [] },
            notifications: { passed: 0, failed: 0, tests: [] },
            stats: { passed: 0, failed: 0, tests: [] }
        };
    }

    async runTest(category, testName, testFn) {
        try {
            console.log(`🧪 ${testName}...`);
            await testFn();
            this.testResults[category].passed++;
            this.testResults[category].tests.push({ name: testName, status: '✅ 通过' });
            console.log(`✅ ${testName} - 通过`);
        } catch (error) {
            this.testResults[category].failed++;
            this.testResults[category].tests.push({ 
                name: testName, 
                status: '❌ 失败', 
                error: error.message 
            });
            console.log(`❌ ${testName} - 失败: ${error.message}`);
        }
    }

    async testAuthentication() {
        console.log('\n🔐 === 认证系统测试 ===');

        // 1. 登录测试
        await this.runTest('auth', '用户登录', async () => {
            const response = await axios.post(`${API_BASE}/auth/login`, {
                username: 'admin',
                password: 'admin123'
            });
            
            if (response.status !== 200) throw new Error('登录状态码错误');
            if (!response.data.token) throw new Error('未返回token');
            if (!response.data.user) throw new Error('未返回用户信息');
            
            this.token = response.data.token;
        });

        // 2. 获取用户信息测试
        await this.runTest('auth', '获取用户信息', async () => {
            const response = await axios.get(`${API_BASE}/auth/profile`, {
                headers: { Authorization: `Bearer ${this.token}` }
            });
            
            if (response.status !== 200) throw new Error('获取用户信息状态码错误');
            if (!response.data.user) throw new Error('未返回用户信息');
        });

        // 3. Token刷新测试
        await this.runTest('auth', 'Token刷新', async () => {
            const response = await axios.post(`${API_BASE}/auth/refresh`, {}, {
                headers: { Authorization: `Bearer ${this.token}` }
            });
            
            if (response.status !== 200) throw new Error('刷新token状态码错误');
            if (!response.data.token) throw new Error('未返回新token');
        });
    }

    async testCaseManagement() {
        console.log('\n📋 === 案件管理测试 ===');

        let caseId = null;

        // 1. 获取案件列表
        await this.runTest('cases', '获取案件列表', async () => {
            const response = await axios.get(`${API_BASE}/cases`, {
                headers: { Authorization: `Bearer ${this.token}` }
            });
            
            if (response.status !== 200) throw new Error('获取案件列表状态码错误');
            if (!response.data.cases) throw new Error('未返回案件列表');
        });

        // 2. 创建案件
        await this.runTest('cases', '创建案件', async () => {
            const response = await axios.post(`${API_BASE}/cases`, {
                title: '测试案件-前后端联调',
                type: '合同纠纷',
                description: '这是一个前后端联调测试案件',
                priority: '中',
                client_name: '测试客户',
                client_contact: '13800138000'
            }, {
                headers: { Authorization: `Bearer ${this.token}` }
            });
            
            if (response.status !== 201) throw new Error('创建案件状态码错误');
            if (!response.data.case) throw new Error('未返回案件信息');
            
            caseId = response.data.case.id;
        });

        // 3. 获取案件详情
        if (caseId) {
            await this.runTest('cases', '获取案件详情', async () => {
                const response = await axios.get(`${API_BASE}/cases/${caseId}`, {
                    headers: { Authorization: `Bearer ${this.token}` }
                });
                
                if (response.status !== 200) throw new Error('获取案件详情状态码错误');
                if (!response.data.case) throw new Error('未返回案件详情');
            });

            // 4. 更新案件状态
            await this.runTest('cases', '更新案件状态', async () => {
                const response = await axios.post(`${API_BASE}/cases/${caseId}/status`, {
                    status: '处理中',
                    remark: '开始处理测试案件'
                }, {
                    headers: { Authorization: `Bearer ${this.token}` }
                });
                
                if (response.status !== 200) throw new Error('更新案件状态码错误');
            });
        }
    }

    async testNotifications() {
        console.log('\n🔔 === 通知系统测试 ===');

        // 1. 获取通知列表
        await this.runTest('notifications', '获取通知列表', async () => {
            const response = await axios.get(`${API_BASE}/notifications`, {
                headers: { Authorization: `Bearer ${this.token}` }
            });
            
            if (response.status !== 200) throw new Error('获取通知列表状态码错误');
            if (!response.data.notifications) throw new Error('未返回通知列表');
        });

        // 2. 获取通知统计
        await this.runTest('notifications', '获取通知统计', async () => {
            const response = await axios.get(`${API_BASE}/notifications/stats`, {
                headers: { Authorization: `Bearer ${this.token}` }
            });
            
            if (response.status !== 200) throw new Error('获取通知统计状态码错误');
            if (typeof response.data.total === 'undefined') throw new Error('未返回统计数据');
        });
    }

    async testStatistics() {
        console.log('\n📊 === 统计报表测试 ===');

        // 1. 获取统计总览
        await this.runTest('stats', '获取统计总览', async () => {
            const response = await axios.get(`${API_BASE}/stats/overview`, {
                headers: { Authorization: `Bearer ${this.token}` }
            });
            
            if (response.status !== 200) throw new Error('获取统计总览状态码错误');
            if (!response.data.overview) throw new Error('未返回统计总览');
        });

        // 2. 获取案件统计
        await this.runTest('stats', '获取案件统计', async () => {
            const response = await axios.get(`${API_BASE}/stats/cases`, {
                headers: { Authorization: `Bearer ${this.token}` }
            });
            
            if (response.status !== 200) throw new Error('获取案件统计状态码错误');
            if (!response.data.caseTrend) throw new Error('未返回案件统计');
        });

        // 3. 获取用户活动统计
        await this.runTest('stats', '获取用户活动统计', async () => {
            const response = await axios.get(`${API_BASE}/stats/activity`, {
                headers: { Authorization: `Bearer ${this.token}` }
            });
            
            if (response.status !== 200) throw new Error('获取用户活动统计状态码错误');
            if (!response.data.activityLogs) throw new Error('未返回活动统计');
        });
    }

    printResults() {
        console.log('\n📊 === 测试结果汇总 ===');
        
        let totalPassed = 0;
        let totalFailed = 0;
        
        Object.entries(this.testResults).forEach(([category, results]) => {
            console.log(`\n${category.toUpperCase()}:`);
            console.log(`  通过: ${results.passed}, 失败: ${results.failed}`);
            
            results.tests.forEach(test => {
                console.log(`  ${test.status} ${test.name}`);
                if (test.error) {
                    console.log(`    错误: ${test.error}`);
                }
            });
            
            totalPassed += results.passed;
            totalFailed += results.failed;
        });
        
        console.log(`\n🎯 总计: ${totalPassed + totalFailed} 个测试`);
        console.log(`✅ 通过: ${totalPassed}`);
        console.log(`❌ 失败: ${totalFailed}`);
        console.log(`📈 成功率: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);
        
        if (totalFailed === 0) {
            console.log('\n🎉 所有测试通过！前后端联调成功！');
        } else {
            console.log('\n⚠️ 部分测试失败，需要检查相关功能');
        }
    }

    async run() {
        console.log('🚀 开始综合前后端联调测试...\n');
        
        try {
            await this.testAuthentication();
            await this.testCaseManagement();
            await this.testNotifications();
            await this.testStatistics();
        } catch (error) {
            console.error('测试执行出错:', error);
        }
        
        this.printResults();
    }
}

// 运行测试
const test = new ComprehensiveTest();
test.run();
