# 🚀 法务案件管理平台 v1.0.0 - 快速启动指南

## 📋 版本信息
- **版本**: v1.0.0-stable
- **状态**: ✅ 完整可运行
- **最后测试**: 2025年7月9日

## ⚡ 快速启动

### 1. 启动后端测试服务器
```bash
cd backend
node test-server-8001.js
```
**预期输出**:
```
🚀 启动测试服务器 (端口8001)...
🎉 测试服务器已启动！
📍 地址: http://localhost:8001
```

### 2. 启动前端应用
```bash
cd frontend
npm run dev
```
**预期输出**:
```
VITE v7.0.0  ready in xxx ms
➜  Local:   http://localhost:3000/
➜  Network: use --host to expose
```

### 3. 访问应用
打开浏览器访问: http://localhost:3000

## 🔑 测试账户

### 管理员账户
- **用户名**: admin
- **密码**: admin123
- **权限**: 完整管理权限

### 法务人员账户
- **用户名**: lawyer1
- **密码**: lawyer123
- **权限**: 案件管理权限

## 🧪 功能测试清单

### ✅ 用户认证测试
1. 访问 http://localhost:3000
2. 应该自动跳转到登录页面
3. 使用 admin/admin123 登录
4. 登录成功后跳转到仪表板
5. 点击右上角用户头像，测试登出功能

### ✅ 案件管理测试
1. 点击左侧菜单"案件管理"
2. 查看案件列表（应显示示例数据）
3. 点击"创建案件"按钮
4. 填写案件信息并提交
5. 返回列表查看新创建的案件
6. 点击案件编号查看详情

### ✅ 响应式设计测试
1. 调整浏览器窗口大小
2. 测试移动端视图（宽度 < 768px）
3. 验证侧边栏变为抽屉式菜单
4. 测试菜单展开/收起功能

### ✅ API通信测试
1. 打开浏览器开发者工具
2. 查看Network标签
3. 执行登录操作
4. 验证API请求正常发送到localhost:8001
5. 检查响应数据格式正确

## 🔧 配置验证

### 端口配置检查
```bash
# 检查前端端口
netstat -an | findstr :3000

# 检查后端端口  
netstat -an | findstr :8001
```

### 关键文件检查
确保以下文件存在且配置正确：

1. **frontend/vite.config.js**
   - server.port: 3000
   - proxy配置指向localhost:8001

2. **frontend/src/services/api.js**
   - baseURL: '/api'
   - 代理配置正确

3. **backend/test-server-8001.js**
   - PORT: 8001
   - CORS配置允许localhost:3000

## 🐛 常见问题解决

### 问题1: 端口被占用
**错误**: `Error: Port 3000 is already in use`
**解决**: 
```bash
# 查找占用端口的进程
netstat -ano | findstr :3000
# 结束进程或使用其他端口
```

### 问题2: API请求失败
**错误**: 网络错误或404
**检查**:
1. 后端服务器是否正常运行
2. 端口配置是否正确
3. 代理配置是否生效

### 问题3: 登录失败
**错误**: 用户名或密码错误
**检查**:
1. 使用正确的测试账户
2. 后端服务器响应正常
3. 网络连接正常

### 问题4: 页面空白
**可能原因**:
1. JavaScript错误
2. 组件加载失败
3. 路由配置问题

**解决步骤**:
1. 打开浏览器开发者工具
2. 查看Console错误信息
3. 检查Network请求状态
4. 验证文件路径正确

## 📁 关键文件位置

### 前端核心文件
```
frontend/
├── src/
│   ├── App.jsx                 # 主应用组件
│   ├── main.jsx               # 应用入口
│   ├── services/api.js        # API配置
│   ├── components/Layout/     # 布局组件
│   ├── pages/Auth/           # 认证页面
│   └── pages/Cases/          # 案件页面
├── vite.config.js            # Vite配置
└── package.json              # 依赖配置
```

### 后端测试文件
```
backend/
├── test-server-8001.js       # 测试服务器
└── package.json              # 依赖配置
```

## 🔄 版本回退步骤

如果需要回退到此稳定版本：

1. **保存当前状态**
   ```bash
   git add .
   git commit -m "保存当前状态"
   ```

2. **检查关键配置**
   - 验证端口配置未被修改
   - 确认依赖版本正确
   - 检查API配置完整

3. **重新安装依赖**（如有必要）
   ```bash
   cd frontend
   npm install
   ```

4. **验证功能**
   - 按照测试清单验证所有功能
   - 确认前后端通信正常

## 📞 技术支持

### 日志查看
- **前端日志**: 浏览器开发者工具 Console
- **后端日志**: 终端输出
- **网络请求**: 开发者工具 Network 标签

### 调试模式
```bash
# 前端调试模式
cd frontend
npm run dev

# 查看详细错误信息
# 打开浏览器开发者工具
```

### 性能监控
- 使用浏览器开发者工具 Performance 标签
- 监控API响应时间
- 检查内存使用情况

---

**维护说明**: 此版本已经过完整测试，所有功能正常运行。如遇问题，请首先检查端口配置和服务器状态。

**最后更新**: 2025年7月9日  
**维护者**: Augment Agent
