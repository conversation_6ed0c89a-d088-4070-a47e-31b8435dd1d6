const http = require('http');
const fs = require('fs');
const path = require('path');

function makeRequest(options, data) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    body: body
                });
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(data);
        }
        req.end();
    });
}

async function login() {
    console.log('🔐 登录获取token...');
    
    const loginData = JSON.stringify({
        username: 'admin',
        password: 'admin123'
    });

    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(loginData)
        }
    };

    try {
        const response = await makeRequest(options, loginData);
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 登录成功');
            return data.token;
        } else {
            console.log('❌ 登录失败');
            return null;
        }
    } catch (error) {
        console.error('❌ 登录错误:', error.message);
        return null;
    }
}

async function createTestFile() {
    // 创建一个测试文件
    const testContent = `# 测试文档

这是一个用于测试文件上传功能的文档。

## 内容
- 测试文件上传
- 测试文件下载
- 测试文件删除

创建时间: ${new Date().toISOString()}
`;

    const testFilePath = path.join(__dirname, 'test-document.txt');
    fs.writeFileSync(testFilePath, testContent, 'utf8');
    console.log('📄 测试文件创建成功:', testFilePath);
    return testFilePath;
}

async function testFileUpload(token, caseId, filePath) {
    console.log('\n📤 测试文件上传...');
    
    return new Promise((resolve, reject) => {
        const boundary = '----formdata-boundary-' + Math.random().toString(36);
        const fileName = path.basename(filePath);
        const fileContent = fs.readFileSync(filePath);
        
        // 构建multipart/form-data
        let formData = '';
        formData += `--${boundary}\r\n`;
        formData += `Content-Disposition: form-data; name="description"\r\n\r\n`;
        formData += `测试文档上传\r\n`;
        formData += `--${boundary}\r\n`;
        formData += `Content-Disposition: form-data; name="file_category"\r\n\r\n`;
        formData += `证据材料\r\n`;
        formData += `--${boundary}\r\n`;
        formData += `Content-Disposition: form-data; name="files"; filename="${fileName}"\r\n`;
        formData += `Content-Type: text/plain\r\n\r\n`;
        
        const formDataBuffer = Buffer.concat([
            Buffer.from(formData, 'utf8'),
            fileContent,
            Buffer.from(`\r\n--${boundary}--\r\n`, 'utf8')
        ]);

        const options = {
            hostname: 'localhost',
            port: 3000,
            path: `/api/files/upload/${caseId}`,
            method: 'POST',
            headers: {
                'Content-Type': `multipart/form-data; boundary=${boundary}`,
                'Content-Length': formDataBuffer.length,
                'Authorization': `Bearer ${token}`
            }
        };

        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                console.log('状态码:', res.statusCode);
                if (res.statusCode === 201) {
                    const data = JSON.parse(body);
                    console.log('✅ 文件上传成功');
                    console.log('上传文件数:', data.files.length);
                    if (data.files.length > 0) {
                        console.log('文件ID:', data.files[0].id);
                        resolve(data.files[0].id);
                    } else {
                        resolve(null);
                    }
                } else {
                    console.log('❌ 文件上传失败');
                    console.log('响应:', body);
                    resolve(null);
                }
            });
        });

        req.on('error', (err) => {
            console.error('❌ 上传请求错误:', err.message);
            reject(err);
        });

        req.write(formDataBuffer);
        req.end();
    });
}

async function testGetCaseFiles(token, caseId) {
    console.log('\n📋 测试获取案件文件列表...');
    
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: `/api/files/case/${caseId}`,
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options);
        console.log('状态码:', response.statusCode);
        
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 获取文件列表成功');
            console.log('文件总数:', data.pagination.total);
            console.log('当前页文件数:', data.files.length);
            if (data.files.length > 0) {
                console.log('第一个文件:', data.files[0].original_name);
            }
        } else {
            console.log('❌ 获取文件列表失败');
            console.log('响应:', response.body);
        }
    } catch (error) {
        console.error('❌ 获取文件列表错误:', error.message);
    }
}

async function testFileDownload(token, fileId) {
    if (!fileId) return;
    
    console.log('\n📥 测试文件下载...');
    
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: `/api/files/download/${fileId}`,
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options);
        console.log('状态码:', response.statusCode);
        
        if (response.statusCode === 200) {
            console.log('✅ 文件下载成功');
            console.log('文件大小:', response.body.length, '字节');
            console.log('内容预览:', response.body.substring(0, 100) + '...');
        } else {
            console.log('❌ 文件下载失败');
            console.log('响应:', response.body);
        }
    } catch (error) {
        console.error('❌ 文件下载错误:', error.message);
    }
}

async function testFileDelete(token, fileId) {
    if (!fileId) return;
    
    console.log('\n🗑️ 测试文件删除...');
    
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: `/api/files/${fileId}`,
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options);
        console.log('状态码:', response.statusCode);
        
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 文件删除成功');
            console.log('响应:', data.message);
        } else {
            console.log('❌ 文件删除失败');
            console.log('响应:', response.body);
        }
    } catch (error) {
        console.error('❌ 文件删除错误:', error.message);
    }
}

async function cleanup(filePath) {
    // 清理测试文件
    if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log('🧹 测试文件清理完成');
    }
}

async function main() {
    console.log('🧪 开始测试文件管理功能...\n');
    
    const token = await login();
    if (!token) {
        console.log('❌ 无法获取token，测试终止');
        return;
    }
    
    // 使用已存在的案件ID (从之前的测试中我们知道有案件ID为2)
    const caseId = 2;
    
    let testFilePath;
    let fileId;
    
    try {
        testFilePath = await createTestFile();
        fileId = await testFileUpload(token, caseId, testFilePath);
        await testGetCaseFiles(token, caseId);
        await testFileDownload(token, fileId);
        await testFileDelete(token, fileId);
    } finally {
        if (testFilePath) {
            await cleanup(testFilePath);
        }
    }
    
    console.log('\n✅ 文件管理功能测试完成');
}

main();
