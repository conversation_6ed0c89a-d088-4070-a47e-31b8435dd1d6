const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置存储
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        // 根据文件类型创建子目录
        const fileType = getFileCategory(file.mimetype);
        const subDir = path.join(uploadDir, fileType);
        
        if (!fs.existsSync(subDir)) {
            fs.mkdirSync(subDir, { recursive: true });
        }
        
        cb(null, subDir);
    },
    filename: function (req, file, cb) {
        // 生成唯一文件名：时间戳_随机数_原文件名
        const timestamp = Date.now();
        const random = Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        const basename = path.basename(file.originalname, ext);
        const filename = `${timestamp}_${random}_${basename}${ext}`;
        cb(null, filename);
    }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
    // 允许的文件类型
    const allowedTypes = [
        // 文档类型
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'text/csv',
        // 图片类型
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/bmp',
        'image/webp',
        // 压缩文件
        'application/zip',
        'application/x-rar-compressed',
        'application/x-7z-compressed'
    ];

    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error(`File type ${file.mimetype} is not allowed`), false);
    }
};

// 根据MIME类型确定文件分类
function getFileCategory(mimetype) {
    if (mimetype.startsWith('image/')) {
        return 'images';
    } else if (mimetype.includes('pdf') || mimetype.includes('word') || mimetype.includes('excel') || mimetype.includes('powerpoint') || mimetype.includes('text')) {
        return 'documents';
    } else if (mimetype.includes('zip') || mimetype.includes('rar') || mimetype.includes('7z')) {
        return 'archives';
    } else {
        return 'others';
    }
}

// 创建multer实例
const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 50 * 1024 * 1024, // 50MB限制
        files: 10 // 最多10个文件
    }
});

// 错误处理中间件
const handleUploadError = (error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                error: 'File too large. Maximum size is 50MB',
                code: 'FILE_TOO_LARGE'
            });
        } else if (error.code === 'LIMIT_FILE_COUNT') {
            return res.status(400).json({
                error: 'Too many files. Maximum is 10 files',
                code: 'TOO_MANY_FILES'
            });
        } else if (error.code === 'LIMIT_UNEXPECTED_FILE') {
            return res.status(400).json({
                error: 'Unexpected field name',
                code: 'UNEXPECTED_FIELD'
            });
        }
    } else if (error.message.includes('File type')) {
        return res.status(400).json({
            error: error.message,
            code: 'INVALID_FILE_TYPE'
        });
    }
    
    next(error);
};

// 文件信息提取中间件
const extractFileInfo = (req, res, next) => {
    if (req.files && req.files.length > 0) {
        req.fileInfos = req.files.map(file => ({
            originalName: file.originalname,
            filename: file.filename,
            path: file.path,
            size: file.size,
            mimetype: file.mimetype,
            category: getFileCategory(file.mimetype)
        }));
    } else if (req.file) {
        req.fileInfo = {
            originalName: req.file.originalname,
            filename: req.file.filename,
            path: req.file.path,
            size: req.file.size,
            mimetype: req.file.mimetype,
            category: getFileCategory(req.file.mimetype)
        };
    }
    next();
};

module.exports = {
    upload,
    handleUploadError,
    extractFileInfo,
    getFileCategory
};
