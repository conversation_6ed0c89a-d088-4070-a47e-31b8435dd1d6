import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Select,
  DatePicker,
  Button,
  Space,
  Spin,
  Empty,
  Tag,
  Tooltip,
  message,
  Divider,
} from 'antd';
import {
  Bar<PERSON>hartOutlined,
  PieChartOutlined,
  Line<PERSON><PERSON>Outlined,
  FileTextOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  DownloadOutlined,
  ReloadOutlined,
  TrophyOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { request } from '../../services/api';
import dayjs from 'dayjs';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const Statistics = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [overviewData, setOverviewData] = useState({});
  const [casesByStatus, setCasesByStatus] = useState({});
  const [casesByType, setCasesByType] = useState({});
  const [casesByPriority, setCasesByPriority] = useState({});
  const [userStats, setUserStats] = useState([]);
  const [timeRange, setTimeRange] = useState([
    dayjs().subtract(30, 'day'),
    dayjs()
  ]);
  const [selectedPeriod, setSelectedPeriod] = useState('30days');

  // 状态颜色配置
  const STATUS_COLORS = {
    '待处理': '#faad14',
    '处理中': '#1890ff',
    '已结案': '#52c41a',
    '已归档': '#8c8c8c',
    '已撤销': '#ff4d4f',
  };

  const PRIORITY_COLORS = {
    '低': '#52c41a',
    '中': '#1890ff',
    '高': '#faad14',
    '紧急': '#ff4d4f',
  };

  // 初始化加载
  useEffect(() => {
    fetchStatistics();
  }, [timeRange]);

  // 获取统计数据
  const fetchStatistics = async () => {
    setLoading(true);
    try {
      // 获取总览统计
      const overviewResponse = await request.get('/stats/overview');
      if (overviewResponse.data) {
        setOverviewData(overviewResponse.data.overview || {});
        setCasesByStatus(overviewResponse.data.casesByStatus || {});
        setCasesByType(overviewResponse.data.casesByType || {});
        setCasesByPriority(overviewResponse.data.casesByPriority || {});
      }

      // 获取用户统计
      const userStatsResponse = await request.get('/stats/users');
      if (userStatsResponse.data) {
        setUserStats(userStatsResponse.data.userStats || []);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
      message.error('获取统计数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理时间范围变化
  const handlePeriodChange = (period) => {
    setSelectedPeriod(period);
    let range;
    switch (period) {
      case '7days':
        range = [dayjs().subtract(7, 'day'), dayjs()];
        break;
      case '30days':
        range = [dayjs().subtract(30, 'day'), dayjs()];
        break;
      case '90days':
        range = [dayjs().subtract(90, 'day'), dayjs()];
        break;
      case '1year':
        range = [dayjs().subtract(1, 'year'), dayjs()];
        break;
      default:
        return;
    }
    setTimeRange(range);
  };

  // 导出报表
  const exportReport = () => {
    message.info('报表导出功能开发中...');
  };

  // 计算百分比
  const calculatePercentage = (value, total) => {
    return total > 0 ? Math.round((value / total) * 100) : 0;
  };

  // 用户统计表格列
  const userStatsColumns = [
    {
      title: '排名',
      key: 'rank',
      width: 60,
      render: (_, __, index) => (
        <Space>
          {index < 3 && <TrophyOutlined style={{ color: ['#ffd700', '#c0c0c0', '#cd7f32'][index] }} />}
          {index + 1}
        </Space>
      ),
    },
    {
      title: '用户',
      dataIndex: 'real_name',
      key: 'real_name',
      render: (name, record) => (
        <Space>
          <UserOutlined />
          {name || record.username}
        </Space>
      ),
    },
    {
      title: '负责案件',
      dataIndex: 'totalCases',
      key: 'totalCases',
      render: (count) => <Tag color="blue">{count}</Tag>,
    },
    {
      title: '已结案',
      dataIndex: 'completedCases',
      key: 'completedCases',
      render: (count) => <Tag color="green">{count}</Tag>,
    },
    {
      title: '结案率',
      key: 'completionRate',
      render: (_, record) => {
        const rate = calculatePercentage(record.completedCases, record.totalCases);
        return (
          <Progress
            percent={rate}
            size="small"
            status={rate >= 80 ? 'success' : rate >= 60 ? 'normal' : 'exception'}
          />
        );
      },
    },
  ];

  return (
    <div>
      <Title level={2}>
        <BarChartOutlined /> 统计报表
      </Title>

      {/* 时间范围选择 */}
      <Card style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <span>统计周期：</span>
              <Select
                value={selectedPeriod}
                onChange={handlePeriodChange}
                style={{ width: 120 }}
              >
                <Option value="7days">最近7天</Option>
                <Option value="30days">最近30天</Option>
                <Option value="90days">最近90天</Option>
                <Option value="1year">最近1年</Option>
              </Select>
              <RangePicker
                value={timeRange}
                onChange={setTimeRange}
                format="YYYY-MM-DD"
              />
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchStatistics}
                loading={loading}
              >
                刷新数据
              </Button>
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={exportReport}
              >
                导出报表
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      <Spin spinning={loading}>
        {/* 总览统计 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="总案件数"
                value={overviewData.totalCases || 0}
                prefix={<FileTextOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="本月新增"
                value={overviewData.newCasesThisMonth || 0}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="即将到期"
                value={overviewData.upcomingCases || 0}
                prefix={<ExclamationCircleOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="未读通知"
                value={overviewData.unreadNotifications || 0}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 案件状态分布 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} lg={8}>
            <Card
              title={
                <Space>
                  <PieChartOutlined />
                  案件状态分布
                </Space>
              }
            >
              {Object.keys(casesByStatus).length > 0 ? (
                <div>
                  {Object.entries(casesByStatus).map(([status, count]) => {
                    const total = Object.values(casesByStatus).reduce((sum, val) => sum + val, 0);
                    const percentage = calculatePercentage(count, total);
                    return (
                      <div key={status} style={{ marginBottom: 16 }}>
                        <Row justify="space-between" align="middle">
                          <Col>
                            <Space>
                              <Tag color={STATUS_COLORS[status]}>{status}</Tag>
                              <span>{count}件</span>
                            </Space>
                          </Col>
                          <Col>
                            <span>{percentage}%</span>
                          </Col>
                        </Row>
                        <Progress
                          percent={percentage}
                          strokeColor={STATUS_COLORS[status]}
                          showInfo={false}
                          size="small"
                        />
                      </div>
                    );
                  })}
                </div>
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>

          {/* 案件类型分布 */}
          <Col xs={24} lg={8}>
            <Card
              title={
                <Space>
                  <BarChartOutlined />
                  案件类型分布
                </Space>
              }
            >
              {Object.keys(casesByType).length > 0 ? (
                <div>
                  {Object.entries(casesByType).map(([type, count]) => {
                    const total = Object.values(casesByType).reduce((sum, val) => sum + val, 0);
                    const percentage = calculatePercentage(count, total);
                    return (
                      <div key={type} style={{ marginBottom: 16 }}>
                        <Row justify="space-between" align="middle">
                          <Col>
                            <Space>
                              <Tag color="blue">{type}</Tag>
                              <span>{count}件</span>
                            </Space>
                          </Col>
                          <Col>
                            <span>{percentage}%</span>
                          </Col>
                        </Row>
                        <Progress
                          percent={percentage}
                          strokeColor="#1890ff"
                          showInfo={false}
                          size="small"
                        />
                      </div>
                    );
                  })}
                </div>
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>

          {/* 案件优先级分布 */}
          <Col xs={24} lg={8}>
            <Card
              title={
                <Space>
                  <LineChartOutlined />
                  优先级分布
                </Space>
              }
            >
              {Object.keys(casesByPriority).length > 0 ? (
                <div>
                  {Object.entries(casesByPriority).map(([priority, count]) => {
                    const total = Object.values(casesByPriority).reduce((sum, val) => sum + val, 0);
                    const percentage = calculatePercentage(count, total);
                    return (
                      <div key={priority} style={{ marginBottom: 16 }}>
                        <Row justify="space-between" align="middle">
                          <Col>
                            <Space>
                              <Tag color={PRIORITY_COLORS[priority]}>{priority}</Tag>
                              <span>{count}件</span>
                            </Space>
                          </Col>
                          <Col>
                            <span>{percentage}%</span>
                          </Col>
                        </Row>
                        <Progress
                          percent={percentage}
                          strokeColor={PRIORITY_COLORS[priority]}
                          showInfo={false}
                          size="small"
                        />
                      </div>
                    );
                  })}
                </div>
              ) : (
                <Empty description="暂无数据" />
              )}
            </Card>
          </Col>
        </Row>

        {/* 用户工作统计 */}
        <Row gutter={[16, 16]}>
          <Col xs={24}>
            <Card
              title={
                <Space>
                  <TeamOutlined />
                  用户工作统计
                </Space>
              }
            >
              {userStats.length > 0 ? (
                <Table
                  columns={userStatsColumns}
                  dataSource={userStats}
                  rowKey="id"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                  }}
                  size="small"
                />
              ) : (
                <Empty description="暂无用户统计数据" />
              )}
            </Card>
          </Col>
        </Row>

        {/* 系统信息 */}
        <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
          <Col xs={24}>
            <Card
              title="系统信息"
              size="small"
            >
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={8}>
                  <Statistic
                    title="总用户数"
                    value={overviewData.totalUsers || 0}
                    prefix={<UserOutlined />}
                  />
                </Col>
                <Col xs={24} sm={8}>
                  <Statistic
                    title="总文件数"
                    value={overviewData.totalFiles || 0}
                    prefix={<FileTextOutlined />}
                  />
                </Col>
                <Col xs={24} sm={8}>
                  <Statistic
                    title="数据更新时间"
                    value={dayjs().format('YYYY-MM-DD HH:mm:ss')}
                    prefix={<ClockCircleOutlined />}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default Statistics;
