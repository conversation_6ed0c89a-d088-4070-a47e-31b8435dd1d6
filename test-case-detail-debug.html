<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案件详情调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        button {
            padding: 8px 16px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        button:hover {
            background: #0056b3;
        }

        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }

        input {
            padding: 5px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>

<body>
    <h1>案件详情调试测试</h1>

    <div class="test-section">
        <h3>1. 测试案件详情API</h3>
        <div>
            <label>案件ID: </label>
            <input type="number" id="caseId" value="1" min="1">
            <button onclick="testCaseDetail()">获取案件详情</button>
        </div>
        <div id="caseDetailResult" class="test-result info">等待测试...</div>
    </div>

    <div class="test-section">
        <h3>2. 测试案件列表API</h3>
        <button onclick="testCaseList()">获取案件列表</button>
        <div id="caseListResult" class="test-result info">等待测试...</div>
    </div>

    <div class="test-section">
        <h3>3. 用户登录</h3>
        <div>
            <label>用户名: </label>
            <input type="text" id="username" value="admin" placeholder="用户名">
            <label>密码: </label>
            <input type="password" id="password" value="admin123" placeholder="密码">
            <button onclick="testLogin()">登录</button>
        </div>
        <div id="loginResult" class="test-result info">等待登录...</div>
    </div>

    <div class="test-section">
        <h3>4. 测试认证状态</h3>
        <button onclick="testAuth()">检查认证状态</button>
        <div id="authResult" class="test-result info">等待测试...</div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001/api';

        // 模拟认证token（根据实际情况调整）
        let authToken = localStorage.getItem('token') || 'test-token';

        async function testCaseDetail() {
            const resultDiv = document.getElementById('caseDetailResult');
            const caseId = document.getElementById('caseId').value;

            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = '正在获取案件详情...';

            try {
                const response = await fetch(`${API_BASE}/cases/${caseId}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 案件详情获取成功<br>
                        <strong>案件状态:</strong> ${data.data?.case?.status || '未知'}<br>
                        <strong>案件标题:</strong> ${data.data?.case?.title || '未知'}<br>
                        <strong>案件编号:</strong> ${data.data?.case?.case_no || '未知'}<br>
                        <strong>删除时间:</strong> ${data.data?.case?.deleted_at || '未删除'}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `❌ 获取案件详情失败<br>状态码: ${response.status}<br>错误信息: ${data.error || data.message || '未知错误'}<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
            }
        }

        async function testCaseList() {
            const resultDiv = document.getElementById('caseListResult');

            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = '正在获取案件列表...';

            try {
                const response = await fetch(`${API_BASE}/cases?page=1&limit=5`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    const cases = data.data?.cases || [];
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 案件列表获取成功<br>
                        <strong>案件数量:</strong> ${cases.length}<br>
                        <strong>案件状态分布:</strong><br>
                        ${cases.map(c => `- ${c.case_no}: ${c.status} (删除时间: ${c.deleted_at || '未删除'})`).join('<br>')}
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `❌ 获取案件列表失败<br>状态码: ${response.status}<br>错误信息: ${data.error || data.message || '未知错误'}<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = '正在登录...';

            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    // 保存token
                    localStorage.setItem('token', data.token);
                    authToken = data.token;

                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 登录成功<br>
                        <strong>用户:</strong> ${data.user.real_name || data.user.username}<br>
                        <strong>Token:</strong> ${data.token.substring(0, 20)}...<br>
                        <pre>${JSON.stringify(data.user, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `❌ 登录失败<br>状态码: ${response.status}<br>错误信息: ${data.error || data.message || '未知错误'}<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
            }
        }

        async function testAuth() {
            const resultDiv = document.getElementById('authResult');

            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = '正在检查认证状态...';

            try {
                const response = await fetch(`${API_BASE}/auth/me`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 认证状态正常<br>
                        <strong>用户信息:</strong><br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `❌ 认证失败<br>状态码: ${response.status}<br>错误信息: ${data.error || data.message || '未知错误'}<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
            }
        }

        // 页面加载时显示当前token
        document.addEventListener('DOMContentLoaded', function () {
            console.log('当前使用的认证token:', authToken);
        });
    </script>
</body>

</html>