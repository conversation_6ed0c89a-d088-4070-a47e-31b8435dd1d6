const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const CaseArchive = sequelize.define('CaseArchive', {
    id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
    },
    case_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        unique: true,
        comment: '案件ID',
        references: {
            model: 'cases',
            key: 'id'
        }
    },
    archive_info: {
        type: DataTypes.TEXT,
        allowNull: false,
        comment: '归档信息（JSON格式）',
    },
    archive_reason: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '归档原因',
    },
    result_summary: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '结果总结',
    },
    archived_by: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '归档人ID',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '归档时间',
    },
}, {
    tableName: 'case_archives',
    timestamps: false,
    indexes: [
        {
            unique: true,
            fields: ['case_id']
        },
        {
            fields: ['archived_by']
        },
        {
            fields: ['created_at']
        }
    ]
});

module.exports = CaseArchive;
