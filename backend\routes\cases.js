const express = require('express');
const router = express.Router();
const { Case, User, CaseFlow, CaseFile, CaseArchive, Log } = require('../models');
const CaseOperationLog = require('../models/CaseOperationLog');
const { authenticate, require<PERSON><PERSON>yer, requireCaseOwnerOrAdmin } = require('../middleware/auth');
const { Op } = require('sequelize');
const NotificationService = require('../utils/notification');

/**
 * 获取案件列表
 * GET /api/cases
 */
router.get('/', authenticate, async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            status,
            type,
            priority,
            owner_id,
            search,
            sort_by = 'created_at',
            sort_order = 'DESC'
        } = req.query;

        const offset = (page - 1) * limit;
        const where = {};

        // 默认不显示已删除的案件
        where.deleted_at = null;

        // 权限过滤：非管理员只能看到自己的案件
        const userRoles = req.user.roles.map(role => role.name);
        if (!userRoles.includes('admin')) {
            where.owner_id = req.user.id;
        }

        // 状态过滤
        if (status) {
            where.status = status;
        }

        // 类型过滤
        if (type) {
            where.type = type;
        }

        // 优先级过滤
        if (priority) {
            where.priority = priority;
        }

        // 负责人过滤
        if (owner_id) {
            where.owner_id = owner_id;
        }

        // 搜索过滤
        if (search) {
            where[Op.or] = [
                { title: { [Op.like]: `%${search}%` } },
                { case_no: { [Op.like]: `%${search}%` } },
                { description: { [Op.like]: `%${search}%` } },
                { client_name: { [Op.like]: `%${search}%` } }
            ];
        }

        const { count, rows } = await Case.findAndCountAll({
            where,
            include: [{
                model: User,
                as: 'owner',
                attributes: ['id', 'username', 'real_name']
            }],
            order: [[sort_by, sort_order.toUpperCase()]],
            limit: parseInt(limit),
            offset: parseInt(offset)
        });

        res.json({
            success: true,
            data: rows,
            pagination: {
                total: count,
                page: parseInt(page),
                limit: parseInt(limit),
                pages: Math.ceil(count / limit)
            }
        });

    } catch (error) {
        console.error('Get cases error:', error);
        res.status(500).json({
            error: 'Failed to get cases',
            code: 'GET_CASES_ERROR'
        });
    }
});

/**
 * 获取回收站案件列表
 * GET /api/cases/recycle
 */
router.get('/recycle', authenticate, async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            search,
            sort_by = 'deleted_at',
            sort_order = 'DESC'
        } = req.query;

        const offset = (page - 1) * limit;
        const where = {};

        // 只显示已删除的案件
        where.deleted_at = { [Op.ne]: null };

        // 权限过滤：非管理员只能看到自己的案件
        const userRoles = req.user.roles.map(role => role.name);
        if (!userRoles.includes('admin')) {
            where.owner_id = req.user.id;
        }

        // 搜索过滤
        if (search) {
            where[Op.or] = [
                { title: { [Op.like]: `%${search}%` } },
                { case_no: { [Op.like]: `%${search}%` } },
                { description: { [Op.like]: `%${search}%` } },
                { client_name: { [Op.like]: `%${search}%` } }
            ];
        }

        const { count, rows } = await Case.findAndCountAll({
            where,
            include: [{
                model: User,
                as: 'owner',
                attributes: ['id', 'username', 'real_name']
            }],
            order: [[sort_by, sort_order.toUpperCase()]],
            limit: parseInt(limit),
            offset: parseInt(offset)
        });

        res.json({
            success: true,
            data: rows,
            pagination: {
                total: count,
                page: parseInt(page),
                limit: parseInt(limit),
                pages: Math.ceil(count / limit)
            }
        });

    } catch (error) {
        console.error('Get recycle cases error:', error);
        res.status(500).json({
            error: 'Failed to get recycle cases',
            code: 'GET_RECYCLE_CASES_ERROR'
        });
    }
});

/**
 * 获取案件详情
 * GET /api/cases/:id
 */
router.get('/:id', authenticate, requireCaseOwnerOrAdmin, async (req, res) => {
    try {
        const caseRecord = await Case.findByPk(req.params.id, {
            include: [
                {
                    model: User,
                    as: 'owner',
                    attributes: ['id', 'username', 'real_name']
                },
                {
                    model: CaseFlow,
                    as: 'flows',
                    include: [{
                        model: User,
                        as: 'operator',
                        attributes: ['id', 'username', 'real_name']
                    }],
                    order: [['created_at', 'DESC']]
                },
                {
                    model: CaseFile,
                    as: 'files',
                    include: [{
                        model: User,
                        as: 'uploader',
                        attributes: ['id', 'username', 'real_name']
                    }]
                },
                {
                    model: CaseArchive,
                    as: 'archive',
                    include: [{
                        model: User,
                        as: 'archivedBy',
                        attributes: ['id', 'username', 'real_name']
                    }]
                }
            ]
        });

        if (!caseRecord) {
            return res.status(404).json({
                error: 'Case not found',
                code: 'CASE_NOT_FOUND'
            });
        }

        res.json({
            success: true,
            data: {
                case: caseRecord
            }
        });

    } catch (error) {
        console.error('Get case detail error:', error);
        res.status(500).json({
            error: 'Failed to get case detail',
            code: 'GET_CASE_ERROR'
        });
    }
});

/**
 * 创建新案件
 * POST /api/cases
 */
router.post('/', authenticate, requireLawyer, async (req, res) => {
    try {
        const {
            title,
            type,
            description,
            priority = '中',
            deadline,
            amount,
            client_name,
            client_contact,
            owner_id
        } = req.body;

        // 验证必填字段
        if (!title || !type) {
            return res.status(400).json({
                error: 'Title and type are required',
                code: 'MISSING_FIELDS'
            });
        }

        // 生成案件编号
        const case_no = Case.generateCaseNo();

        // 确定负责人（如果未指定，默认为当前用户）
        const finalOwnerId = owner_id || req.user.id;

        // 验证负责人是否存在
        const owner = await User.findByPk(finalOwnerId);
        if (!owner) {
            return res.status(400).json({
                error: 'Invalid owner_id',
                code: 'INVALID_OWNER'
            });
        }

        // 创建案件
        const caseRecord = await Case.create({
            title,
            case_no,
            type,
            description,
            owner_id: finalOwnerId,
            priority,
            deadline: deadline ? new Date(deadline) : null,
            amount: amount ? parseFloat(amount) : null,
            client_name,
            client_contact,
            status: '待处理'
        });

        // 记录案件流转
        await CaseFlow.create({
            case_id: caseRecord.id,
            action: '创建案件',
            operator_id: req.user.id,
            remark: `案件创建，负责人：${owner.real_name}`,
            new_value: JSON.stringify({
                title,
                type,
                priority,
                owner_id: finalOwnerId
            })
        });

        // 记录操作日志
        await Log.create({
            user_id: req.user.id,
            action: '创建案件',
            module: '案件管理',
            detail: JSON.stringify({
                case_id: caseRecord.id,
                case_no: caseRecord.case_no,
                title: caseRecord.title
            }),
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            status: 'success'
        });

        // 返回创建的案件（包含关联信息）
        const createdCase = await Case.findByPk(caseRecord.id, {
            include: [{
                model: User,
                as: 'owner',
                attributes: ['id', 'username', 'real_name']
            }]
        });

        res.status(201).json({
            success: true,
            message: 'Case created successfully',
            data: {
                case: createdCase
            }
        });

    } catch (error) {
        console.error('Create case error:', error);
        res.status(500).json({
            error: 'Failed to create case',
            code: 'CREATE_CASE_ERROR'
        });
    }
});

/**
 * 更新案件
 * PUT /api/cases/:id
 */
router.put('/:id', authenticate, requireCaseOwnerOrAdmin, async (req, res) => {
    try {
        const caseRecord = await Case.findByPk(req.params.id);
        if (!caseRecord) {
            return res.status(404).json({
                error: 'Case not found',
                code: 'CASE_NOT_FOUND'
            });
        }

        const {
            title,
            type,
            description,
            priority,
            deadline,
            amount,
            client_name,
            client_contact
        } = req.body;

        // 记录变更前的值
        const oldValues = {
            title: caseRecord.title,
            type: caseRecord.type,
            description: caseRecord.description,
            priority: caseRecord.priority,
            deadline: caseRecord.deadline,
            amount: caseRecord.amount,
            client_name: caseRecord.client_name,
            client_contact: caseRecord.client_contact
        };

        // 更新案件
        await caseRecord.update({
            title: title || caseRecord.title,
            type: type || caseRecord.type,
            description: description !== undefined ? description : caseRecord.description,
            priority: priority || caseRecord.priority,
            deadline: deadline ? new Date(deadline) : caseRecord.deadline,
            amount: amount !== undefined ? (amount ? parseFloat(amount) : null) : caseRecord.amount,
            client_name: client_name !== undefined ? client_name : caseRecord.client_name,
            client_contact: client_contact !== undefined ? client_contact : caseRecord.client_contact
        });

        // 记录案件流转
        await CaseFlow.create({
            case_id: caseRecord.id,
            action: '编辑案件',
            operator_id: req.user.id,
            remark: '案件信息更新',
            old_value: JSON.stringify(oldValues),
            new_value: JSON.stringify({
                title: caseRecord.title,
                type: caseRecord.type,
                description: caseRecord.description,
                priority: caseRecord.priority,
                deadline: caseRecord.deadline,
                amount: caseRecord.amount,
                client_name: caseRecord.client_name,
                client_contact: caseRecord.client_contact
            })
        });

        // 记录操作日志
        await Log.create({
            user_id: req.user.id,
            action: '编辑案件',
            module: '案件管理',
            detail: JSON.stringify({
                case_id: caseRecord.id,
                case_no: caseRecord.case_no,
                changes: req.body
            }),
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            status: 'success'
        });

        // 返回更新后的案件
        const updatedCase = await Case.findByPk(caseRecord.id, {
            include: [{
                model: User,
                as: 'owner',
                attributes: ['id', 'username', 'real_name']
            }]
        });

        res.json({
            message: 'Case updated successfully',
            case: updatedCase
        });

    } catch (error) {
        console.error('Update case error:', error);
        res.status(500).json({
            error: 'Failed to update case',
            code: 'UPDATE_CASE_ERROR'
        });
    }
});

/**
 * 案件状态变更
 * PATCH /api/cases/:id/status
 */
router.patch('/:id/status', authenticate, requireCaseOwnerOrAdmin, async (req, res) => {
    try {
        const { status, remark } = req.body;

        if (!status) {
            return res.status(400).json({
                error: 'Status is required',
                code: 'MISSING_STATUS'
            });
        }

        const caseRecord = await Case.findByPk(req.params.id);
        if (!caseRecord) {
            return res.status(404).json({
                error: 'Case not found',
                code: 'CASE_NOT_FOUND'
            });
        }

        const oldStatus = caseRecord.status;

        // 更新状态
        await caseRecord.update({ status });

        // 记录案件流转
        await CaseFlow.create({
            case_id: caseRecord.id,
            action: '状态变更',
            operator_id: req.user.id,
            remark: remark || `状态从"${oldStatus}"变更为"${status}"`,
            old_value: JSON.stringify({ status: oldStatus }),
            new_value: JSON.stringify({ status })
        });

        // 记录操作日志
        await Log.create({
            user_id: req.user.id,
            action: '案件状态变更',
            module: '案件管理',
            detail: JSON.stringify({
                case_id: caseRecord.id,
                case_no: caseRecord.case_no,
                old_status: oldStatus,
                new_status: status
            }),
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            status: 'success'
        });

        // 发送状态变更通知
        try {
            await NotificationService.sendCaseNotification(
                caseRecord.id,
                '状态变更',
                req.user.id,
                remark
            );
        } catch (notifyError) {
            console.error('发送状态变更通知失败:', notifyError);
        }

        res.json({
            message: 'Case status updated successfully',
            case: {
                id: caseRecord.id,
                case_no: caseRecord.case_no,
                status: caseRecord.status
            }
        });

    } catch (error) {
        console.error('Update case status error:', error);
        res.status(500).json({
            error: 'Failed to update case status',
            code: 'UPDATE_STATUS_ERROR'
        });
    }
});

/**
 * 分配案件负责人
 * POST /api/cases/:id/assign
 */
router.post('/:id/assign', authenticate, requireLawyer, async (req, res) => {
    try {
        const { owner_id, remark } = req.body;

        if (!owner_id) {
            return res.status(400).json({
                error: 'Owner ID is required',
                code: 'MISSING_OWNER_ID'
            });
        }

        const caseRecord = await Case.findByPk(req.params.id);
        if (!caseRecord) {
            return res.status(404).json({
                error: 'Case not found',
                code: 'CASE_NOT_FOUND'
            });
        }

        // 验证新负责人是否存在
        const newOwner = await User.findByPk(owner_id);
        if (!newOwner) {
            return res.status(400).json({
                error: 'Invalid owner_id',
                code: 'INVALID_OWNER'
            });
        }

        const oldOwnerId = caseRecord.owner_id;
        const oldOwner = await User.findByPk(oldOwnerId);

        // 更新负责人
        await caseRecord.update({ owner_id });

        // 记录案件流转
        await CaseFlow.create({
            case_id: caseRecord.id,
            action: '分配负责人',
            operator_id: req.user.id,
            remark: remark || `负责人从"${oldOwner?.real_name}"变更为"${newOwner.real_name}"`,
            old_value: JSON.stringify({ owner_id: oldOwnerId, owner_name: oldOwner?.real_name }),
            new_value: JSON.stringify({ owner_id, owner_name: newOwner.real_name })
        });

        // 记录操作日志
        await Log.create({
            user_id: req.user.id,
            action: '分配案件负责人',
            module: '案件管理',
            detail: JSON.stringify({
                case_id: caseRecord.id,
                case_no: caseRecord.case_no,
                old_owner: oldOwner?.real_name,
                new_owner: newOwner.real_name
            }),
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            status: 'success'
        });

        // 发送案件分配通知
        try {
            await NotificationService.sendCaseNotification(
                caseRecord.id,
                '案件分配',
                req.user.id,
                remark
            );
        } catch (notifyError) {
            console.error('发送案件分配通知失败:', notifyError);
        }

        res.json({
            message: 'Case assigned successfully',
            case: {
                id: caseRecord.id,
                case_no: caseRecord.case_no,
                owner_id: caseRecord.owner_id,
                owner_name: newOwner.real_name
            }
        });

    } catch (error) {
        console.error('Assign case error:', error);
        res.status(500).json({
            error: 'Failed to assign case',
            code: 'ASSIGN_CASE_ERROR'
        });
    }
});

/**
 * 删除案件
 * DELETE /api/cases/:id
 */
router.delete('/:id', authenticate, requireCaseOwnerOrAdmin, async (req, res) => {
    try {
        const caseRecord = await Case.findByPk(req.params.id);
        if (!caseRecord) {
            return res.status(404).json({
                error: 'Case not found',
                code: 'CASE_NOT_FOUND'
            });
        }

        // 检查案件状态，已结案或已归档的案件不能删除
        if (['已结案', '已归档'].includes(caseRecord.status)) {
            return res.status(400).json({
                error: 'Cannot delete completed or archived cases',
                code: 'CASE_CANNOT_DELETE'
            });
        }

        // 软删除：设置删除时间
        await caseRecord.update({
            deleted_at: new Date(),
            status: '已删除'
        });

        // 记录删除操作
        await CaseFlow.create({
            case_id: caseRecord.id,
            action: '删除案件',
            operator_id: req.user.id,
            remark: `案件被软删除：${caseRecord.title}`,
            old_value: JSON.stringify({
                title: caseRecord.title,
                status: caseRecord.status,
                owner_id: caseRecord.owner_id,
                deleted_at: null
            }),
            new_value: JSON.stringify({
                deleted_at: new Date(),
                status: '已删除'
            })
        });

        // 记录操作日志
        await CaseOperationLog.create({
            case_id: caseRecord.id,
            operation_type: '删除',
            operator_id: req.user.id,
            operation_detail: `案件被软删除：${caseRecord.title}`,
            old_data: {
                title: caseRecord.title,
                status: caseRecord.status,
                deleted_at: null
            },
            new_data: {
                deleted_at: new Date(),
                status: '已删除'
            },
            ip_address: req.ip,
            user_agent: req.get('User-Agent')
        });

        // 记录系统日志
        await Log.create({
            user_id: req.user.id,
            action: '删除案件',
            module: '案件管理',
            detail: JSON.stringify({
                case_id: caseRecord.id,
                case_no: caseRecord.case_no,
                title: caseRecord.title,
                delete_type: 'soft'
            }),
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            status: 'success'
        });

        res.json({
            message: 'Case deleted successfully',
            case_id: caseRecord.id,
            case_no: caseRecord.case_no
        });

    } catch (error) {
        console.error('Delete case error:', error);
        res.status(500).json({
            error: 'Failed to delete case',
            code: 'DELETE_CASE_ERROR'
        });
    }
});



/**
 * 恢复案件
 * POST /api/cases/:id/restore
 */
router.post('/:id/restore', authenticate, requireCaseOwnerOrAdmin, async (req, res) => {
    try {
        const caseRecord = await Case.findByPk(req.params.id);
        if (!caseRecord) {
            return res.status(404).json({
                error: 'Case not found',
                code: 'CASE_NOT_FOUND'
            });
        }

        // 检查案件是否已删除
        if (!caseRecord.deleted_at) {
            return res.status(400).json({
                error: 'Case is not deleted',
                code: 'CASE_NOT_DELETED'
            });
        }

        // 恢复案件
        await caseRecord.update({
            deleted_at: null,
            status: '待处理' // 恢复为待处理状态
        });

        // 记录恢复操作
        await CaseFlow.create({
            case_id: caseRecord.id,
            action: '恢复案件',
            operator_id: req.user.id,
            remark: `案件从回收站恢复：${caseRecord.title}`,
            old_value: JSON.stringify({
                deleted_at: caseRecord.deleted_at,
                status: '已删除'
            }),
            new_value: JSON.stringify({
                deleted_at: null,
                status: '待处理'
            })
        });

        // 记录操作日志
        await CaseOperationLog.create({
            case_id: caseRecord.id,
            operation_type: '恢复',
            operator_id: req.user.id,
            operation_detail: `案件从回收站恢复：${caseRecord.title}`,
            old_data: {
                deleted_at: caseRecord.deleted_at,
                status: '已删除'
            },
            new_data: {
                deleted_at: null,
                status: '待处理'
            },
            ip_address: req.ip,
            user_agent: req.get('User-Agent')
        });

        res.json({
            message: 'Case restored successfully',
            case: {
                id: caseRecord.id,
                case_no: caseRecord.case_no,
                title: caseRecord.title,
                status: caseRecord.status
            }
        });

    } catch (error) {
        console.error('Restore case error:', error);
        res.status(500).json({
            error: 'Failed to restore case',
            code: 'RESTORE_CASE_ERROR'
        });
    }
});

/**
 * 永久删除案件
 * DELETE /api/cases/:id/permanent
 */
router.delete('/:id/permanent', authenticate, requireCaseOwnerOrAdmin, async (req, res) => {
    try {
        const caseRecord = await Case.findByPk(req.params.id);
        if (!caseRecord) {
            return res.status(404).json({
                error: 'Case not found',
                code: 'CASE_NOT_FOUND'
            });
        }

        // 检查案件是否已删除
        if (!caseRecord.deleted_at) {
            return res.status(400).json({
                error: 'Case must be deleted first',
                code: 'CASE_NOT_DELETED'
            });
        }

        // 记录永久删除操作
        await CaseOperationLog.create({
            case_id: caseRecord.id,
            operation_type: '永久删除',
            operator_id: req.user.id,
            operation_detail: `案件被永久删除：${caseRecord.title}`,
            old_data: {
                title: caseRecord.title,
                case_no: caseRecord.case_no,
                status: caseRecord.status,
                deleted_at: caseRecord.deleted_at
            },
            ip_address: req.ip,
            user_agent: req.get('User-Agent')
        });

        // 永久删除案件
        await caseRecord.destroy();

        res.json({
            message: 'Case permanently deleted',
            case_id: caseRecord.id,
            case_no: caseRecord.case_no
        });

    } catch (error) {
        console.error('Permanent delete case error:', error);
        res.status(500).json({
            error: 'Failed to permanently delete case',
            code: 'PERMANENT_DELETE_ERROR'
        });
    }
});

module.exports = router;
