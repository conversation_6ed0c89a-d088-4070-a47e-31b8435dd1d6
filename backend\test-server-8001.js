const express = require('express');
const cors = require('cors');

console.log('🚀 启动测试服务器 (端口8001)...');

const app = express();
const PORT = 8001;

// 基本中间件
app.use(cors({
  origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`📍 ${req.method} ${req.path}`, req.body || req.query);
  next();
});

// 基础路由
app.get('/', (req, res) => {
  res.json({ 
    message: '法务案件管理平台 MVP测试API',
    version: '1.0.0-test',
    port: PORT,
    status: 'running',
    timestamp: new Date().toISOString()
  });
});

app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy',
    timestamp: new Date().toISOString(),
    port: PORT
  });
});

// 认证API
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;
  
  console.log('🔐 登录尝试:', { username, password });
  
  // 简单验证
  if (username === 'admin' && password === 'admin123') {
    const response = {
      success: true,
      data: {
        token: 'jwt-token-admin-' + Date.now(),
        user: {
          id: 1,
          username: 'admin',
          real_name: '系统管理员',
          email: '<EMAIL>',
          status: 1,
          roles: [{ id: 1, name: 'admin', description: '系统管理员' }]
        }
      }
    };
    console.log('✅ 管理员登录成功');
    res.json(response);
  } else if (username === 'lawyer1' && password === 'lawyer123') {
    const response = {
      success: true,
      data: {
        token: 'jwt-token-lawyer-' + Date.now(),
        user: {
          id: 2,
          username: 'lawyer1',
          real_name: '法务专员',
          email: '<EMAIL>',
          status: 1,
          roles: [{ id: 2, name: 'lawyer', description: '法务人员' }]
        }
      }
    };
    console.log('✅ 法务人员登录成功');
    res.json(response);
  } else {
    console.log('❌ 登录失败: 用户名或密码错误');
    res.status(401).json({
      success: false,
      error: '用户名或密码错误',
      code: 'INVALID_CREDENTIALS'
    });
  }
});

app.post('/api/auth/logout', (req, res) => {
  console.log('🚪 用户登出');
  res.json({
    success: true,
    message: '登出成功'
  });
});

app.get('/api/auth/me', (req, res) => {
  console.log('👤 获取用户信息');
  res.json({
    success: true,
    data: {
      id: 1,
      username: 'admin',
      real_name: '系统管理员',
      email: '<EMAIL>',
      status: 1,
      roles: [{ id: 1, name: 'admin', description: '系统管理员' }]
    }
  });
});

// 案件管理API
app.get('/api/cases', (req, res) => {
  console.log('📋 获取案件列表');
  res.json({
    success: true,
    data: {
      cases: [
        {
          id: 1,
          case_no: 'CASE20250709001',
          title: '示例合同纠纷案件',
          type: '合同纠纷',
          status: '待处理',
          priority: '中',
          owner: { id: 1, real_name: '系统管理员', username: 'admin' },
          client_name: '示例客户',
          client_contact: '13800138000',
          amount: 100000,
          deadline: '2025-01-15',
          description: '这是一个示例案件，用于测试系统功能。',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ],
      total: 1,
      page: 1,
      limit: 10
    }
  });
});

app.post('/api/cases', (req, res) => {
  console.log('➕ 创建案件:', req.body);
  const newCase = {
    id: Date.now(),
    case_no: `CASE${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}${String(Date.now()).slice(-3)}`,
    ...req.body,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  res.json({
    success: true,
    data: {
      case: newCase
    },
    message: '案件创建成功'
  });
});

app.get('/api/cases/:id', (req, res) => {
  const { id } = req.params;
  console.log('📄 获取案件详情:', id);
  
  res.json({
    success: true,
    data: {
      case: {
        id: parseInt(id),
        case_no: 'CASE20250709001',
        title: '示例合同纠纷案件',
        type: '合同纠纷',
        status: '待处理',
        priority: '中',
        owner: { id: 1, real_name: '系统管理员', username: 'admin' },
        client_name: '示例客户',
        client_contact: '13800138000',
        amount: 100000,
        deadline: '2025-01-15',
        description: '这是一个示例案件，用于测试系统功能。',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    }
  });
});

// 用户管理API
app.get('/api/users', (req, res) => {
  console.log('👥 获取用户列表');
  res.json({
    success: true,
    data: {
      users: [
        { id: 1, username: 'admin', real_name: '系统管理员', email: '<EMAIL>' },
        { id: 2, username: 'lawyer1', real_name: '法务专员', email: '<EMAIL>' }
      ]
    }
  });
});

// 负责人管理API
app.get('/api/responsibles/active', (req, res) => {
  console.log('👨‍💼 获取活跃负责人列表');
  res.json({
    success: true,
    data: {
      responsibles: [
        { id: 1, name: '系统管理员', department: '管理部' },
        { id: 2, name: '法务专员', department: '法务部' }
      ]
    }
  });
});

// 统计API
app.get('/api/stats/overview', (req, res) => {
  console.log('📊 获取统计概览');
  res.json({
    success: true,
    data: {
      overview: {
        totalCases: 1,
        newCasesThisMonth: 1,
        upcomingCases: 0,
        unreadNotifications: 0
      },
      casesByStatus: {
        '待处理': 1,
        '处理中': 0,
        '已结案': 0,
        '已归档': 0
      }
    }
  });
});

// 404处理
app.use('*', (req, res) => {
  console.log('❓ 404 - 路径未找到:', req.originalUrl);
  res.status(404).json({
    error: 'API endpoint not found',
    path: req.originalUrl,
    method: req.method
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('💥 服务器错误:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: err.message
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🎉 测试服务器已启动！`);
  console.log(`📍 地址: http://localhost:${PORT}`);
  console.log(`🔗 健康检查: http://localhost:${PORT}/health`);
  console.log(`⏰ 启动时间: ${new Date().toISOString()}`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 收到关闭信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 收到终止信号，正在关闭服务器...');
  process.exit(0);
});
