const https = require('https');
const http = require('http');

// 测试统计API
async function testStatsAPI() {
    console.log('测试统计API...');

    // 首先登录获取token
    const loginData = JSON.stringify({
        username: 'admin',
        password: 'admin123'
    });

    const loginOptions = {
        hostname: '127.0.0.1',
        port: 8001,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(loginData)
        }
    };

    try {
        console.log('1. 登录获取token...');
        const loginResponse = await makeRequest(loginOptions, loginData);

        if (loginResponse.statusCode !== 200) {
            console.error('登录失败:', loginResponse.statusCode, loginResponse.body);
            return;
        }

        const loginResult = JSON.parse(loginResponse.body);
        console.log('登录响应:', loginResult);
        const token = loginResult.data?.token || loginResult.token;
        if (!token) {
            console.error('无法获取token:', loginResult);
            return;
        }
        console.log('✅ 登录成功，获取到token');

        // 测试统计总览API
        console.log('\n2. 测试统计总览API...');
        const statsOptions = {
            hostname: '127.0.0.1',
            port: 8001,
            path: '/api/stats/overview',
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        };

        const statsResponse = await makeRequest(statsOptions);
        console.log('状态码:', statsResponse.statusCode);

        if (statsResponse.statusCode === 200) {
            const statsData = JSON.parse(statsResponse.body);
            console.log('✅ 统计总览获取成功');
            console.log('总案件数:', statsData.overview.totalCases);
            console.log('本月新增:', statsData.overview.newCasesThisMonth);
            console.log('即将到期:', statsData.overview.upcomingCases);
            console.log('未读通知:', statsData.overview.unreadNotifications);
            console.log('按状态统计:', statsData.casesByStatus);
            console.log('按类型统计:', statsData.casesByType);
            console.log('按优先级统计:', statsData.casesByPriority);
        } else {
            console.error('❌ 统计总览获取失败:', statsResponse.statusCode, statsResponse.body);
        }

        // 测试用户统计API
        console.log('\n3. 测试用户统计API...');
        const userStatsOptions = {
            hostname: '127.0.0.1',
            port: 8001,
            path: '/api/stats/users',
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        };

        const userStatsResponse = await makeRequest(userStatsOptions);
        console.log('状态码:', userStatsResponse.statusCode);

        if (userStatsResponse.statusCode === 200) {
            const userStatsData = JSON.parse(userStatsResponse.body);
            console.log('✅ 用户统计获取成功');
            console.log('用户统计数据:', userStatsData.userStats);
        } else {
            console.error('❌ 用户统计获取失败:', userStatsResponse.statusCode, userStatsResponse.body);
        }

        // 测试回收站API
        console.log('\n4. 测试回收站API...');
        const recycleOptions = {
            hostname: '127.0.0.1',
            port: 8001,
            path: '/api/cases/recycle',
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        };

        const recycleResponse = await makeRequest(recycleOptions);
        console.log('状态码:', recycleResponse.statusCode);

        if (recycleResponse.statusCode === 200) {
            const recycleData = JSON.parse(recycleResponse.body);
            console.log('✅ 回收站获取成功');
            console.log('回收站案件数量:', recycleData.data?.length || 0);
            console.log('总数:', recycleData.pagination?.total || 0);
        } else {
            console.error('❌ 回收站获取失败:', recycleResponse.statusCode, recycleResponse.body);
        }

    } catch (error) {
        console.error('测试失败:', error.message);
    }
}

function makeRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    body: body
                });
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (data) {
            req.write(data);
        }
        req.end();
    });
}

testStatsAPI();
