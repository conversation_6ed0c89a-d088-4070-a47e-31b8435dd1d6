# Test case creation and file upload association
$token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwidXNlcm5hbWUiOiJhZG1pbiIsInJlYWxfbmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsImVtYWlsIjoiYWRtaW5AZXhhbXBsZS5jb20iLCJyb2xlcyI6WyJhZG1pbiJdLCJpYXQiOjE3NTIxMTU2MTMsImV4cCI6MTc1MjcyMDQxM30.NpekrrZpiAFhOzwfS55fYWAZREUbpPS8phR48FedU0w"

# Create test case
$caseData = @{
    title = "Test Case - File Association"
    type = "Contract Dispute"
    priority = "High"
    description = "Test file upload association functionality"
    owner_id = 1
} | ConvertTo-Json

$response = Invoke-RestMethod -Uri "http://localhost:8001/api/cases" -Method POST -ContentType "application/json" -Headers @{"Authorization"="Bearer $token"} -Body $caseData

Write-Host "Created case ID: $($response.case.id)"
$caseId = $response.case.id

# Get case details to check file list
$caseDetail = Invoke-RestMethod -Uri "http://localhost:8001/api/cases/$caseId" -Method GET -Headers @{"Authorization"="Bearer $token"}

Write-Host "Case files count: $($caseDetail.case.files.Count)"
if ($caseDetail.case.files.Count -gt 0) {
    Write-Host "Files:"
    $caseDetail.case.files | ForEach-Object {
        Write-Host "  - $($_.original_name) ($($_.file_size) bytes)"
    }
} else {
    Write-Host "No files found for this case"
}
