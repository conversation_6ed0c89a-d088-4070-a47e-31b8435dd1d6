const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const bcrypt = require('bcrypt');

const User = sequelize.define('User', {
    id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
    },
    username: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
        comment: '用户名',
        validate: {
            len: [3, 50],
            notEmpty: true,
        }
    },
    password: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '密码（加密）',
        validate: {
            notEmpty: true,
        }
    },
    real_name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '真实姓名',
        validate: {
            notEmpty: true,
        }
    },
    email: {
        type: DataTypes.STRING(100),
        allowNull: true,
        unique: true,
        comment: '邮箱',
        validate: {
            isEmail: true,
        }
    },
    status: {
        type: DataTypes.TINYINT,
        allowNull: false,
        defaultValue: 1,
        comment: '状态：0-禁用，1-启用',
        validate: {
            isIn: [[0, 1]],
        }
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    tableName: 'users',
    timestamps: false,
    indexes: [
        {
            unique: true,
            fields: ['username']
        },
        {
            unique: true,
            fields: ['email']
        },
        {
            fields: ['status']
        }
    ],
    hooks: {
        beforeCreate: async (user) => {
            if (user.password) {
                user.password = await bcrypt.hash(user.password, 12);
            }
        },
        beforeUpdate: async (user) => {
            if (user.changed('password')) {
                user.password = await bcrypt.hash(user.password, 12);
            }
            user.updated_at = new Date();
        }
    }
});

// 实例方法
User.prototype.validatePassword = async function(password) {
    return await bcrypt.compare(password, this.password);
};

User.prototype.toJSON = function() {
    const values = Object.assign({}, this.get());
    delete values.password; // 不返回密码
    return values;
};

// 类方法
User.findByUsername = function(username) {
    return this.findOne({
        where: { username },
        include: ['roles']
    });
};

User.findByEmail = function(email) {
    return this.findOne({
        where: { email },
        include: ['roles']
    });
};

module.exports = User;
