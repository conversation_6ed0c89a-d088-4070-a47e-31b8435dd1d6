import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Input,
  Space,
  Typography,
  Row,
  Col,
  message,
  Popconfirm,
  Tag,
  Tooltip,
  Card,
  Empty,
  Modal
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  UndoOutlined,
  DeleteOutlined,
  ArrowLeftOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';

import { request } from '../../services/api';
import { CASE_CONSTANTS } from '../../services/cases';

const { Title } = Typography;
const { Search } = Input;

const RecycleBin = () => {
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);
  const [cases, setCases] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchText, setSearchText] = useState('');

  // 获取回收站案件列表
  const fetchRecycleBinCases = async (page = 1, limit = 10, search = '') => {
    setLoading(true);
    try {
      const params = {
        page,
        limit,
        ...(search && { search })
      };

      const response = await request.get('/cases/recycle', { params });

      if (response.data) {
        setCases(response.data.data || []);
        setPagination(prev => ({
          ...prev,
          current: response.data.pagination?.page || page,
          total: response.data.pagination?.total || 0,
          pageSize: response.data.pagination?.limit || limit,
        }));
      }
    } catch (error) {
      console.error('获取回收站案件列表失败:', error);
      message.error('获取回收站案件列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchRecycleBinCases();
  }, []);

  // 搜索处理
  const handleSearch = (value) => {
    setSearchText(value);
    fetchRecycleBinCases(1, pagination.pageSize, value);
  };

  // 恢复案件
  const handleRestore = async (caseId) => {
    try {
      await request.post(`/cases/${caseId}/restore`);
      message.success('案件恢复成功');
      fetchRecycleBinCases(pagination.current, pagination.pageSize, searchText);
    } catch (error) {
      console.error('恢复案件失败:', error);
      message.error('恢复案件失败，请稍后重试');
    }
  };

  // 永久删除案件
  const handlePermanentDelete = async (caseId) => {
    try {
      await request.delete(`/cases/${caseId}/permanent`);
      message.success('案件已永久删除');
      fetchRecycleBinCases(pagination.current, pagination.pageSize, searchText);
    } catch (error) {
      console.error('永久删除案件失败:', error);
      message.error('永久删除案件失败，请稍后重试');
    }
  };

  // 批量恢复确认
  const showBatchRestoreConfirm = (caseIds) => {
    Modal.confirm({
      title: '批量恢复案件',
      icon: <ExclamationCircleOutlined />,
      content: `确定要恢复选中的 ${caseIds.length} 个案件吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await Promise.all(caseIds.map(id => request.post(`/cases/${id}/restore`)));
          message.success(`成功恢复 ${caseIds.length} 个案件`);
          fetchRecycleBinCases(pagination.current, pagination.pageSize, searchText);
        } catch (error) {
          console.error('批量恢复失败:', error);
          message.error('批量恢复失败，请稍后重试');
        }
      }
    });
  };

  // 表格列定义
  const columns = [
    {
      title: '案件编号',
      dataIndex: 'case_no',
      key: 'case_no',
      width: 160,
      ellipsis: {
        showTitle: true,
      },
    },
    {
      title: '案件标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '案件类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type) => (
        <Tag color="blue">{type}</Tag>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority) => (
        <Tag color={CASE_CONSTANTS.PRIORITY_COLORS[priority] || 'default'}>
          {priority}
        </Tag>
      ),
    },
    {
      title: '负责人',
      dataIndex: ['owner', 'real_name'],
      key: 'owner',
      width: 100,
      render: (text, record) => text || record.owner?.username || '-',
    },
    {
      title: '删除时间',
      dataIndex: 'deleted_at',
      key: 'deleted_at',
      width: 120,
      render: (date) => date ? dayjs(date).format('MM-DD HH:mm') : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="恢复案件">
            <Button
              type="text"
              size="small"
              icon={<UndoOutlined />}
              onClick={() => handleRestore(record.id)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要永久删除这个案件吗？"
            description="永久删除后无法恢复，请谨慎操作。"
            onConfirm={() => handlePermanentDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="永久删除">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 页面标题和操作按钮 */}
      <div style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => navigate('/cases')}
              >
                返回案件列表
              </Button>
              <Title level={2} style={{ margin: 0 }}>
                回收站
              </Title>
            </Space>
          </Col>
          <Col>
            <Space>
              <Search
                placeholder="搜索案件编号、标题或客户名称"
                allowClear
                style={{ width: 300 }}
                onSearch={handleSearch}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
              />
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchRecycleBinCases(pagination.current, pagination.pageSize, searchText)}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* 案件表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={cases}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              fetchRecycleBinCases(page, pageSize, searchText);
            },
          }}
          scroll={{ x: 800 }}
          locale={{
            emptyText: (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="回收站为空"
              />
            ),
          }}
        />
      </Card>
    </div>
  );
};

export default RecycleBin;
