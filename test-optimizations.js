/**
 * 系统优化功能测试脚本
 * 测试响应式设计、错误处理、性能优化等功能
 */

const fs = require('fs');
const path = require('path');

// 测试结果记录
const testResults = {
    responsive: {},
    errorHandling: {},
    performance: {},
    summary: {}
};

// 颜色输出函数
const colors = {
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    blue: (text) => `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => `\x1b[36m${text}\x1b[0m`
};

// 记录测试结果
function recordResult(category, test, success, message) {
    if (!testResults[category]) testResults[category] = {};
    testResults[category][test] = { success, message };

    const color = success ? colors.green : colors.red;
    const icon = success ? '✅' : '❌';
    console.log(color(`${icon} ${test}: ${message}`));
}

// 1. 测试响应式设计文件
function testResponsiveDesign() {
    console.log(colors.cyan('\n🔍 1. 测试响应式设计'));

    // 检查响应式CSS文件
    const responsiveCssPath = path.join(__dirname, 'frontend/src/styles/responsive.css');
    if (fs.existsSync(responsiveCssPath)) {
        const content = fs.readFileSync(responsiveCssPath, 'utf8');

        // 检查关键响应式特性
        const features = [
            { name: '移动端断点', pattern: /@media.*max-width.*576px/ },
            { name: '平板端断点', pattern: /@media.*min-width.*577px.*max-width.*768px/ },
            { name: '桌面端断点', pattern: /@media.*min-width.*769px/ },
            { name: '触摸设备优化', pattern: /@media.*hover: none.*pointer: coarse/ },
            { name: '移动端表格优化', pattern: /\.ant-table-thead.*display: none/ },
            { name: '移动端按钮组', pattern: /\.mobile-button-group/ },
            { name: '文件管理响应式', pattern: /\.file-management-responsive/ },
            { name: '通知页面响应式', pattern: /\.notifications-responsive/ },
            { name: '统计页面响应式', pattern: /\.statistics-responsive/ },
            { name: '打印样式', pattern: /@media print/ }
        ];

        features.forEach(feature => {
            if (feature.pattern.test(content)) {
                recordResult('responsive', feature.name, true, '已实现');
            } else {
                recordResult('responsive', feature.name, false, '未找到相关样式');
            }
        });

        // 检查文件大小
        const stats = fs.statSync(responsiveCssPath);
        const fileSizeKB = Math.round(stats.size / 1024);
        recordResult('responsive', '文件大小', fileSizeKB < 50, `${fileSizeKB}KB ${fileSizeKB < 50 ? '(合理)' : '(过大)'}`);

    } else {
        recordResult('responsive', '响应式CSS文件', false, '文件不存在');
    }

    // 检查主应用是否引入响应式样式
    const mainJsxPath = path.join(__dirname, 'frontend/src/main.jsx');
    if (fs.existsSync(mainJsxPath)) {
        const content = fs.readFileSync(mainJsxPath, 'utf8');
        if (content.includes('./styles/responsive.css')) {
            recordResult('responsive', '样式引入', true, '已在main.jsx中引入');
        } else {
            recordResult('responsive', '样式引入', false, '未在main.jsx中引入');
        }
    }
}

// 2. 测试错误处理功能
function testErrorHandling() {
    console.log(colors.cyan('\n🔍 2. 测试错误处理功能'));

    // 检查错误边界组件
    const errorBoundaryPath = path.join(__dirname, 'frontend/src/components/ErrorBoundary/ErrorBoundary.jsx');
    if (fs.existsSync(errorBoundaryPath)) {
        const content = fs.readFileSync(errorBoundaryPath, 'utf8');

        const features = [
            { name: '错误边界类', pattern: /class ErrorBoundary extends React\.Component/ },
            { name: '错误捕获', pattern: /componentDidCatch/ },
            { name: '错误状态管理', pattern: /getDerivedStateFromError/ },
            { name: '错误日志记录', pattern: /logErrorToService/ },
            { name: '用户友好界面', pattern: /Result.*status="error"/ },
            { name: '重试功能', pattern: /handleRetry/ },
            { name: '开发环境调试', pattern: /process\.env\.NODE_ENV === 'development'/ }
        ];

        features.forEach(feature => {
            if (feature.pattern.test(content)) {
                recordResult('errorHandling', feature.name, true, '已实现');
            } else {
                recordResult('errorHandling', feature.name, false, '未找到相关代码');
            }
        });
    } else {
        recordResult('errorHandling', '错误边界组件', false, '文件不存在');
    }

    // 检查错误处理工具
    const errorHandlerPath = path.join(__dirname, 'frontend/src/utils/errorHandler.jsx');
    if (fs.existsSync(errorHandlerPath)) {
        const content = fs.readFileSync(errorHandlerPath, 'utf8');

        const features = [
            { name: '错误解析', pattern: /parseError/ },
            { name: '网络错误处理', pattern: /NETWORK_ERROR/ },
            { name: '认证错误处理', pattern: /handleAuthError/ },
            { name: '重试机制', pattern: /handleRetry/ },
            { name: '错误日志', pattern: /logError/ },
            { name: '网络状态监控', pattern: /setupNetworkMonitoring/ }
        ];

        features.forEach(feature => {
            if (feature.pattern.test(content)) {
                recordResult('errorHandling', feature.name, true, '已实现');
            } else {
                recordResult('errorHandling', feature.name, false, '未找到相关代码');
            }
        });
    } else {
        recordResult('errorHandling', '错误处理工具', false, '文件不存在');
    }

    // 检查API服务是否使用错误处理器
    const apiPath = path.join(__dirname, 'frontend/src/services/api.js');
    if (fs.existsSync(apiPath)) {
        const content = fs.readFileSync(apiPath, 'utf8');
        if (content.includes('errorHandler.handleApiError')) {
            recordResult('errorHandling', 'API错误处理集成', true, '已集成到API服务');
        } else {
            recordResult('errorHandling', 'API错误处理集成', false, '未集成到API服务');
        }
    }
}

// 3. 测试性能优化功能
function testPerformanceOptimizations() {
    console.log(colors.cyan('\n🔍 3. 测试性能优化功能'));

    // 检查性能工具文件
    const performancePath = path.join(__dirname, 'frontend/src/utils/performance.js');
    if (fs.existsSync(performancePath)) {
        const content = fs.readFileSync(performancePath, 'utf8');

        const features = [
            { name: '防抖函数', pattern: /export function debounce/ },
            { name: '节流函数', pattern: /export function throttle/ },
            { name: '图片懒加载', pattern: /lazyLoadImages/ },
            { name: '虚拟滚动', pattern: /class VirtualScroll/ },
            { name: '内存缓存', pattern: /class MemoryCache/ },
            { name: '请求缓存装饰器', pattern: /withCache/ },
            { name: '组件懒加载', pattern: /lazyComponent/ },
            { name: '性能监控', pattern: /class PerformanceMonitor/ },
            { name: '长任务监控', pattern: /observeLongTasks/ },
            { name: '内存使用监控', pattern: /getMemoryUsage/ }
        ];

        features.forEach(feature => {
            if (feature.pattern.test(content)) {
                recordResult('performance', feature.name, true, '已实现');
            } else {
                recordResult('performance', feature.name, false, '未找到相关代码');
            }
        });

        // 检查文件大小
        const stats = fs.statSync(performancePath);
        const fileSizeKB = Math.round(stats.size / 1024);
        recordResult('performance', '工具文件大小', fileSizeKB < 20, `${fileSizeKB}KB ${fileSizeKB < 20 ? '(合理)' : '(过大)'}`);

    } else {
        recordResult('performance', '性能工具文件', false, '文件不存在');
    }
}

// 4. 检查应用集成
function testAppIntegration() {
    console.log(colors.cyan('\n🔍 4. 测试应用集成'));

    // 检查App.jsx是否使用错误边界
    const appPath = path.join(__dirname, 'frontend/src/App.jsx');
    if (fs.existsSync(appPath)) {
        const content = fs.readFileSync(appPath, 'utf8');

        if (content.includes('import ErrorBoundary')) {
            recordResult('performance', '错误边界导入', true, '已导入ErrorBoundary');
        } else {
            recordResult('performance', '错误边界导入', false, '未导入ErrorBoundary');
        }

        if (content.includes('<ErrorBoundary>')) {
            recordResult('performance', '错误边界使用', true, '已使用ErrorBoundary包装应用');
        } else {
            recordResult('performance', '错误边界使用', false, '未使用ErrorBoundary包装应用');
        }
    }

    // 检查页面组件是否应用了响应式类名
    const pageFiles = [
        'frontend/src/pages/Files/FileManagement.jsx',
        'frontend/src/pages/Notifications/Notifications.jsx',
        'frontend/src/pages/Statistics/Statistics.jsx'
    ];

    pageFiles.forEach(filePath => {
        const fullPath = path.join(__dirname, filePath);
        const fileName = path.basename(filePath, '.jsx');

        if (fs.existsSync(fullPath)) {
            const content = fs.readFileSync(fullPath, 'utf8');

            // 检查是否有响应式相关的类名或样式
            const hasResponsiveFeatures =
                content.includes('xs=') ||
                content.includes('sm=') ||
                content.includes('md=') ||
                content.includes('lg=') ||
                content.includes('responsive') ||
                content.includes('mobile') ||
                content.includes('tablet');

            recordResult('performance', `${fileName}响应式特性`, hasResponsiveFeatures,
                hasResponsiveFeatures ? '包含响应式特性' : '缺少响应式特性');
        }
    });
}

// 5. 生成测试报告
function generateReport() {
    console.log(colors.cyan('\n📊 优化功能测试结果汇总'));

    let totalTests = 0;
    let passedTests = 0;

    Object.keys(testResults).forEach(category => {
        if (category === 'summary') return;

        console.log(colors.blue(`\n${category.toUpperCase()}:`));
        Object.keys(testResults[category]).forEach(test => {
            const result = testResults[category][test];
            totalTests++;
            if (result.success) passedTests++;

            const color = result.success ? colors.green : colors.red;
            console.log(`  ${color(result.success ? '✅' : '❌')} ${test}: ${result.message}`);
        });
    });

    const successRate = Math.round((passedTests / totalTests) * 100);
    console.log(colors.cyan(`\n📈 总体测试结果:`));
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过测试: ${colors.green(passedTests)}`);
    console.log(`失败测试: ${colors.red(totalTests - passedTests)}`);
    console.log(`成功率: ${successRate >= 80 ? colors.green(successRate + '%') : colors.yellow(successRate + '%')}`);

    if (successRate >= 80) {
        console.log(colors.green('\n🎉 系统优化功能测试通过！优化效果良好。'));
    } else {
        console.log(colors.yellow('\n⚠️  部分优化功能存在问题，需要进一步完善。'));
    }

    // 提供改进建议
    console.log(colors.cyan('\n💡 改进建议:'));
    if (testResults.responsive && Object.values(testResults.responsive).some(r => !r.success)) {
        console.log('- 完善响应式设计，确保所有页面在移动端正常显示');
    }
    if (testResults.errorHandling && Object.values(testResults.errorHandling).some(r => !r.success)) {
        console.log('- 加强错误处理机制，提升用户体验');
    }
    if (testResults.performance && Object.values(testResults.performance).some(r => !r.success)) {
        console.log('- 实现更多性能优化功能，提升系统响应速度');
    }
}

// 主测试函数
function runOptimizationTests() {
    console.log(colors.cyan('🚀 开始系统优化功能测试\n'));

    try {
        testResponsiveDesign();
        testErrorHandling();
        testPerformanceOptimizations();
        testAppIntegration();
        generateReport();
    } catch (error) {
        console.error(colors.red(`测试过程中发生错误: ${error.message}`));
    }
}

// 运行测试
runOptimizationTests();
