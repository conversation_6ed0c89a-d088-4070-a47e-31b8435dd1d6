import React from 'react';
import { Result, <PERSON><PERSON>, Card, Typography, Space, Alert } from 'antd';
import {
  ExclamationCircleOutlined,
  ReloadOutlined,
  HomeOutlined,
  BugOutlined
} from '@ant-design/icons';

const { Paragraph, Text } = Typography;

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error) {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return {
      hasError: true,
      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
    };
  }

  componentDidCatch(error, errorInfo) {
    // 记录错误信息
    this.setState({
      error,
      errorInfo,
    });

    // 发送错误报告到服务器
    this.logErrorToService(error, errorInfo);
  }

  logErrorToService = (error, errorInfo) => {
    try {
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        errorId: this.state.errorId,
      };

      // 只在开发环境下输出到控制台，不发送到后端
      if (process.env.NODE_ENV === 'development') {
        console.group('🚨 React Error Boundary');
        console.error('Error:', error);
        console.error('Error Info:', errorInfo);
        console.error('Error Report:', errorReport);
        console.groupEnd();
      }

      // 本地存储错误信息
      const localErrors = JSON.parse(localStorage.getItem('frontend_errors') || '[]');
      localErrors.push(errorReport);

      // 只保留最近10个错误
      if (localErrors.length > 10) {
        localErrors.splice(0, localErrors.length - 10);
      }

      localStorage.setItem('frontend_errors', JSON.stringify(localErrors));
    } catch (logError) {
      console.error('Error logging failed:', logError);
    }
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  render() {
    if (this.state.hasError) {
      const { error, errorInfo, errorId } = this.state;
      const isDevelopment = process.env.NODE_ENV === 'development';

      return (
        <div style={{
          padding: '24px',
          minHeight: '100vh',
          backgroundColor: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Card style={{ maxWidth: 800, width: '100%' }}>
            <Result
              status="error"
              icon={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
              title="页面出现错误"
              subTitle="抱歉，页面遇到了一个意外错误。我们已经记录了这个问题，请稍后重试。"
              extra={[
                <Space key="actions" wrap>
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={this.handleRetry}
                  >
                    重新加载
                  </Button>
                  <Button
                    icon={<HomeOutlined />}
                    onClick={this.handleGoHome}
                  >
                    返回首页
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={this.handleReload}
                  >
                    刷新页面
                  </Button>
                </Space>
              ]}
            />

            {/* 错误ID显示 */}
            <Alert
              message="错误追踪信息"
              description={
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Text>错误ID: <Text code>{errorId}</Text></Text>
                  <Text>时间: {new Date().toLocaleString()}</Text>
                  <Text type="secondary">
                    如果问题持续存在，请联系技术支持并提供上述错误ID。
                  </Text>
                </Space>
              }
              type="info"
              showIcon
              style={{ marginTop: 16 }}
            />

            {/* 开发环境下显示详细错误信息 */}
            {isDevelopment && error && (
              <Card
                title={
                  <Space>
                    <BugOutlined />
                    开发调试信息
                  </Space>
                }
                style={{ marginTop: 16 }}
                size="small"
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text strong>错误消息:</Text>
                    <Paragraph code style={{ marginTop: 8 }}>
                      {error.message}
                    </Paragraph>
                  </div>

                  {error.stack && (
                    <div>
                      <Text strong>错误堆栈:</Text>
                      <Paragraph
                        code
                        style={{
                          marginTop: 8,
                          maxHeight: 200,
                          overflow: 'auto',
                          fontSize: '12px'
                        }}
                      >
                        {error.stack}
                      </Paragraph>
                    </div>
                  )}

                  {errorInfo && errorInfo.componentStack && (
                    <div>
                      <Text strong>组件堆栈:</Text>
                      <Paragraph
                        code
                        style={{
                          marginTop: 8,
                          maxHeight: 200,
                          overflow: 'auto',
                          fontSize: '12px'
                        }}
                      >
                        {errorInfo.componentStack}
                      </Paragraph>
                    </div>
                  )}
                </Space>
              </Card>
            )}
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
