const JWTUtils = require('../utils/jwt');
const { User, Role } = require('../models');

/**
 * JWT认证中间件
 */
const authenticate = async (req, res, next) => {
    try {
        // 提取token
        const token = JWTUtils.extractTokenFromHeader(req);
        
        if (!token) {
            return res.status(401).json({
                error: 'Access token is required',
                code: 'TOKEN_MISSING'
            });
        }

        // 验证token
        const decoded = await JWTUtils.verifyToken(token);
        
        // 查询用户信息（包含角色）
        const user = await User.findByPk(decoded.id, {
            include: [{
                model: Role,
                as: 'roles',
                attributes: ['id', 'name', 'description']
            }],
            attributes: { exclude: ['password'] }
        });

        if (!user) {
            return res.status(401).json({
                error: 'User not found',
                code: 'USER_NOT_FOUND'
            });
        }

        if (user.status !== 1) {
            return res.status(401).json({
                error: 'User account is disabled',
                code: 'USER_DISABLED'
            });
        }

        // 将用户信息添加到请求对象
        req.user = user;
        req.token = token;
        
        next();
    } catch (error) {
        console.error('Authentication error:', error);
        return res.status(401).json({
            error: 'Invalid or expired token',
            code: 'TOKEN_INVALID'
        });
    }
};

/**
 * 角色权限检查中间件
 * @param {string|Array} roles - 允许的角色名称
 */
const authorize = (roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                error: 'Authentication required',
                code: 'AUTH_REQUIRED'
            });
        }

        const userRoles = req.user.roles.map(role => role.name);
        const allowedRoles = Array.isArray(roles) ? roles : [roles];
        
        // 检查用户是否具有所需角色
        const hasPermission = allowedRoles.some(role => userRoles.includes(role));
        
        if (!hasPermission) {
            return res.status(403).json({
                error: 'Insufficient permissions',
                code: 'PERMISSION_DENIED',
                required_roles: allowedRoles,
                user_roles: userRoles
            });
        }

        next();
    };
};

/**
 * 可选认证中间件（不强制要求token）
 */
const optionalAuth = async (req, res, next) => {
    try {
        const token = JWTUtils.extractTokenFromHeader(req);
        
        if (token) {
            const decoded = await JWTUtils.verifyToken(token);
            const user = await User.findByPk(decoded.id, {
                include: [{
                    model: Role,
                    as: 'roles',
                    attributes: ['id', 'name', 'description']
                }],
                attributes: { exclude: ['password'] }
            });

            if (user && user.status === 1) {
                req.user = user;
                req.token = token;
            }
        }
        
        next();
    } catch (error) {
        // 可选认证失败时不阻止请求继续
        next();
    }
};

/**
 * 管理员权限检查
 */
const requireAdmin = authorize(['admin']);

/**
 * 法务人员权限检查
 */
const requireLawyer = authorize(['admin', 'lawyer']);

/**
 * 案件所有者或管理员权限检查
 */
const requireCaseOwnerOrAdmin = async (req, res, next) => {
    try {
        if (!req.user) {
            return res.status(401).json({
                error: 'Authentication required',
                code: 'AUTH_REQUIRED'
            });
        }

        const userRoles = req.user.roles.map(role => role.name);
        
        // 管理员可以访问所有案件
        if (userRoles.includes('admin')) {
            return next();
        }

        // 检查是否为案件所有者
        const caseId = req.params.id || req.params.caseId;
        if (caseId) {
            const { Case } = require('../models');
            const caseRecord = await Case.findByPk(caseId);
            
            if (!caseRecord) {
                return res.status(404).json({
                    error: 'Case not found',
                    code: 'CASE_NOT_FOUND'
                });
            }

            if (caseRecord.owner_id === req.user.id) {
                return next();
            }
        }

        return res.status(403).json({
            error: 'Access denied: You can only access your own cases',
            code: 'CASE_ACCESS_DENIED'
        });
        
    } catch (error) {
        console.error('Authorization error:', error);
        return res.status(500).json({
            error: 'Authorization check failed',
            code: 'AUTH_CHECK_ERROR'
        });
    }
};

module.exports = {
    authenticate,
    authorize,
    optionalAuth,
    requireAdmin,
    requireLawyer,
    requireCaseOwnerOrAdmin
};
