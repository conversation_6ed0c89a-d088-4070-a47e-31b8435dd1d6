const express = require('express');
const cors = require('cors');
const path = require('path');

console.log('🔍 启动简化测试服务器...');
console.log('📅 时间:', new Date().toISOString());

const app = express();
const PORT = 8003;

// 详细的错误日志功能
function logError(context, error, additionalInfo = {}) {
    console.error('❌ 错误发生:', {
        context,
        timestamp: new Date().toISOString(),
        error: {
            message: error.message,
            stack: error.stack,
            name: error.name
        },
        additionalInfo
    });
}

// 请求追踪日志
function logRequest(req, step, data = {}) {
    console.log('📍 请求追踪:', {
        step,
        timestamp: new Date().toISOString(),
        method: req.method,
        url: req.originalUrl,
        params: req.params,
        query: req.query,
        user: req.user ? { id: req.user.id, username: req.user.username } : null,
        data
    });
}

// 中间件配置
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

console.log('✅ 中间件配置完成');

// 模拟认证中间件
const mockAuth = (req, res, next) => {
    req.user = {
        id: 1,
        username: 'testuser',
        roles: [{ name: 'admin' }]
    };
    next();
};

// 基础路由
app.get('/', (req, res) => {
    console.log('📍 根路由访问');
    res.json({
        message: '简单测试服务器',
        version: '1.0.0',
        status: 'running',
        port: PORT
    });
});

// 健康检查
app.get('/health', (req, res) => {
    console.log('📍 健康检查请求');
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString()
    });
});

// 案件详情API - 修复后的版本（带详细日志）
app.get('/api/cases/:id', mockAuth, async (req, res) => {
    logRequest(req, '开始处理案件详情请求');

    try {
        const caseId = req.params.id;
        logRequest(req, '解析案件ID', { caseId, type: typeof caseId });

        // 验证案件ID格式
        if (!caseId || caseId.trim() === '') {
            logRequest(req, '案件ID为空');
            return res.status(400).json({
                success: false,
                error: '案件ID不能为空',
                code: 'INVALID_CASE_ID'
            });
        }

        // 模拟案件不存在的情况
        if (caseId === '999' || caseId === 'nonexistent') {
            logRequest(req, '案件不存在', { caseId });
            return res.status(404).json({
                success: false,
                error: '案件不存在或已被删除',
                code: 'CASE_NOT_FOUND'
            });
        }

        // 模拟权限不足的情况
        if (caseId === '403') {
            logRequest(req, '权限不足', { userId: req.user.id, caseId });
            return res.status(403).json({
                success: false,
                error: '权限不足：您只能查看自己的案件',
                code: 'CASE_ACCESS_DENIED'
            });
        }

        // 模拟成功返回案件详情
        const mockCaseData = {
            id: parseInt(caseId),
            case_no: `CASE2025${caseId.padStart(4, '0')}`,
            title: `测试案件 ${caseId}`,
            type: '合同纠纷',
            status: '待处理',
            priority: '中',
            description: '这是一个测试案件的描述',
            client_name: '测试客户',
            client_contact: '13800138000',
            amount: 100000,
            deadline: '2025-12-31',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            owner_id: 1,
            owner: {
                id: 1,
                username: 'testuser',
                real_name: '测试用户'
            },
            flows: [],
            files: [],
            archive: null
        };

        logRequest(req, '案件详情获取成功', { caseNo: mockCaseData.case_no });
        res.json({
            success: true,
            data: {
                case: mockCaseData
            }
        });

    } catch (error) {
        logError('获取案件详情', error, { caseId: req.params.id, userId: req.user?.id });
        res.status(500).json({
            success: false,
            error: '获取案件详情失败',
            code: 'GET_CASE_ERROR',
            message: error.message
        });
    }
});

// 案件列表API
app.get('/api/cases', mockAuth, async (req, res) => {
    console.log('📍 获取案件列表请求');

    res.json({
        success: true,
        data: [
            {
                id: 1,
                case_no: 'CASE20250001',
                title: '测试案件1',
                status: '待处理',
                type: '合同纠纷',
                priority: '高',
                created_at: new Date().toISOString(),
                owner: { id: 1, real_name: '测试用户' }
            }
        ],
        pagination: {
            total: 1,
            page: 1,
            limit: 10,
            pages: 1
        }
    });
});

// 404 处理
app.use('*', (req, res) => {
    console.log('📍 404 - 路径未找到:', req.originalUrl);
    res.status(404).json({
        error: 'API endpoint not found',
        path: req.originalUrl,
        method: req.method
    });
});

// 启动服务器
app.listen(PORT, '127.0.0.1', () => {
    console.log(`🚀 简单测试服务器运行在端口 ${PORT}`);
    console.log(`📍 API URL: http://127.0.0.1:${PORT}`);
    console.log(`🏥 健康检查: http://127.0.0.1:${PORT}/health`);
    console.log(`📋 测试案例:`);
    console.log(`   - 正常案件: http://127.0.0.1:${PORT}/api/cases/1`);
    console.log(`   - 不存在案件: http://127.0.0.1:${PORT}/api/cases/999`);
    console.log(`   - 权限不足: http://127.0.0.1:${PORT}/api/cases/403`);
});

module.exports = app;
