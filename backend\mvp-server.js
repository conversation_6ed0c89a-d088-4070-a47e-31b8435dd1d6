/**
 * MVP最简单的后端健康检查服务
 * 目标：验证Node.js进程能否正常启动和运行
 * 不依赖任何外部服务（数据库、复杂中间件等）
 */

const express = require('express');
const app = express();
const PORT = 8000;

console.log('🚀 启动MVP后端服务器...');
console.log('📍 端口:', PORT);
console.log('⏰ 启动时间:', new Date().toISOString());

// 最基础的中间件
app.use(express.json({ limit: '1mb' }));

// 基础CORS设置（手动实现，避免依赖cors包）
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// 健康检查端点
app.get('/health', (req, res) => {
    console.log('📍 健康检查请求:', new Date().toISOString());
    res.json({
        status: 'healthy',
        message: 'MVP后端服务正常运行',
        timestamp: new Date().toISOString(),
        port: PORT,
        uptime: process.uptime(),
        version: '1.0.0-mvp'
    });
});

// 根路径
app.get('/', (req, res) => {
    console.log('📍 根路径访问:', new Date().toISOString());
    res.json({
        message: '法务案件管理平台 MVP API',
        version: '1.0.0-mvp',
        status: 'running',
        endpoints: {
            health: '/health',
            test: '/test'
        }
    });
});

// 测试端点
app.get('/test', (req, res) => {
    console.log('📍 测试端点访问:', new Date().toISOString());
    res.json({
        message: '测试成功',
        timestamp: new Date().toISOString(),
        data: {
            nodeVersion: process.version,
            platform: process.platform,
            memory: process.memoryUsage(),
            pid: process.pid
        }
    });
});

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error('❌ 服务器错误:', err);
    res.status(500).json({
        error: '服务器内部错误',
        message: err.message,
        timestamp: new Date().toISOString()
    });
});

// 404处理
app.use('*', (req, res) => {
    console.log('❌ 404错误:', req.originalUrl);
    res.status(404).json({
        error: '接口不存在',
        path: req.originalUrl,
        timestamp: new Date().toISOString()
    });
});

// 启动服务器
const server = app.listen(PORT, '127.0.0.1', () => {
    console.log('✅ MVP服务器启动成功!');
    console.log(`📍 服务地址: http://127.0.0.1:${PORT}`);
    console.log(`🏥 健康检查: http://127.0.0.1:${PORT}/health`);
    console.log(`🧪 测试端点: http://127.0.0.1:${PORT}/test`);
    console.log('⏰ 启动完成时间:', new Date().toISOString());
});

// 优雅关闭处理
process.on('SIGTERM', () => {
    console.log('📴 收到SIGTERM信号，正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('📴 收到SIGINT信号，正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});

// 未捕获异常处理
process.on('uncaughtException', (err) => {
    console.error('❌ 未捕获异常:', err);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
    process.exit(1);
});

console.log('🔧 MVP服务器配置完成，等待启动...');
