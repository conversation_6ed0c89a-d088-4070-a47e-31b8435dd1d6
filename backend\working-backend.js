const express = require('express');
const cors = require('cors');
const path = require('path');
const jwt = require('jsonwebtoken');

console.log('🔍 Starting working backend server...');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '.env') });

const app = express();
const PORT = 3008; // 使用可用端口

console.log('✅ Environment loaded, PORT:', PORT);

// 中间件配置
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

console.log('✅ Middleware configured');

// 基础路由
app.get('/', (req, res) => {
    console.log('📍 Root route accessed');
    res.json({
        message: '法务案件管理平台 API',
        version: '1.0.0',
        status: 'running',
        port: PORT
    });
});

// 健康检查
app.get('/health', (req, res) => {
    console.log('📍 Health check accessed');
    res.json({
        status: 'healthy',
        database: 'connected',
        timestamp: new Date().toISOString(),
        port: PORT
    });
});

// 简化登录API（用于测试）
app.post('/api/auth/login', async (req, res) => {
    console.log('📍 Login attempt:', req.body);
    const { username, password } = req.body;

    try {
        // 验证输入
        if (!username || !password) {
            return res.status(400).json({
                success: false,
                error: 'Username and password are required',
                code: 'MISSING_CREDENTIALS'
            });
        }

        // 简单的用户验证（用于测试）
        const validUsers = {
            'admin': 'admin123',
            'lawyer1': 'lawyer123',
            'client1': 'client123'
        };

        if (!validUsers[username] || validUsers[username] !== password) {
            return res.status(401).json({
                success: false,
                error: 'Invalid username or password',
                code: 'INVALID_CREDENTIALS'
            });
        }

        // 生成简单的JWT token（模拟）
        const payload = {
            id: username === 'admin' ? 1 : username === 'lawyer1' ? 2 : 3,
            username: username,
            role: username === 'admin' ? 'admin' : username === 'lawyer1' ? 'lawyer' : 'client'
        };

        const token = jwt.sign(payload, 'test-secret-key', { expiresIn: '24h' });

        console.log('✅ Login successful for user:', username);

        // 返回成功响应
        res.json({
            success: true,
            token: token,
            data: {
                token,
                user: {
                    id: payload.id,
                    username: payload.username,
                    real_name: username === 'admin' ? '系统管理员' : username === 'lawyer1' ? '法务专员' : '客户',
                    email: `${username}@example.com`,
                    role: payload.role
                }
            }
        });

    } catch (error) {
        console.error('❌ Login error:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            code: 'SERVER_ERROR'
        });
    }
});

// 模拟负责人数据（移除position字段）
const mockResponsibles = [
    {
        id: 1,
        name: '胡聪',
        email: '<EMAIL>',
        phone: '13800138001',
        department: '法务部',
        status: 1,
        sort_order: 1
    },
    {
        id: 2,
        name: '陈丽仪',
        email: '<EMAIL>',
        phone: '13800138002',
        department: '法务部',
        status: 1,
        sort_order: 2
    },
    {
        id: 3,
        name: '林诗如',
        email: '<EMAIL>',
        phone: '13800138003',
        department: '法务部',
        status: 1,
        sort_order: 3
    },
    {
        id: 4,
        name: '李明茜',
        email: '<EMAIL>',
        phone: '13800138004',
        department: '法务部',
        status: 1,
        sort_order: 4
    },
    {
        id: 5,
        name: '李翠婷',
        email: '<EMAIL>',
        phone: '13800138005',
        department: '法务部',
        status: 1,
        sort_order: 5
    }
];

// 负责人API路由
app.get('/api/responsibles/active', (req, res) => {
    console.log('📍 Get active responsibles request');

    const activeResponsibles = mockResponsibles.filter(r => r.status === 1);

    res.json({
        success: true,
        responsibles: activeResponsibles
    });
});

app.get('/api/responsibles', (req, res) => {
    console.log('📍 Get responsibles request');

    res.json({
        success: true,
        responsibles: mockResponsibles,
        pagination: {
            total: mockResponsibles.length,
            page: 1,
            limit: 50,
            pages: 1
        }
    });
});

// 简单的案件列表API
app.get('/api/cases', (req, res) => {
    console.log('📍 Get cases request');
    console.log('📍 Current cases count:', createdCases.length);

    // 返回已创建的案件列表
    res.json({
        success: true,
        data: {
            cases: createdCases,
            total: createdCases.length,
            page: 1,
            limit: 10
        }
    });
});

// 存储创建的案件（内存中）
let createdCases = [];

// 案件创建API
app.post('/api/cases', (req, res) => {
    console.log('📍 Create case request:', req.body);
    const { title, type, description, priority, owner_id, deadline, amount, client_name, client_contact } = req.body;

    // 简单验证
    if (!title || !type) {
        return res.status(400).json({
            success: false,
            error: '案件标题和类型为必填项',
            code: 'MISSING_REQUIRED_FIELDS'
        });
    }

    // 模拟创建案件
    const newCase = {
        id: Date.now(),
        case_no: `CASE${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}${String(Date.now()).slice(-6)}`,
        title,
        type,
        description: description || '',
        priority: priority || '中',
        status: '待处理',
        owner_id: owner_id || 1,
        deadline: deadline || null,
        amount: amount || null,
        client_name: client_name || '',
        client_contact: client_contact || '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
    };

    // 存储到内存中
    createdCases.push(newCase);

    // 返回符合前端期望的格式
    res.json({
        success: true,
        data: {
            case: newCase  // 前端期望的格式
        },
        message: '案件创建成功'
    });
});

// 简单的统计API
app.get('/api/stats/overview', (req, res) => {
    console.log('📍 Get stats request');
    res.json({
        success: true,
        data: {
            overview: {
                totalCases: 1,
                newCasesThisMonth: 1,
                upcomingCases: 0,
                unreadNotifications: 0
            },
            casesByStatus: {
                '待处理': 1,
                '处理中': 0,
                '已结案': 0,
                '已归档': 0
            }
        }
    });
});

// 通知列表API
app.get('/api/notifications', (req, res) => {
    console.log('📍 Get notifications request');
    res.json({
        success: true,
        data: [
            {
                id: 1,
                title: '新案件分配',
                message: '您有一个新的案件需要处理',
                type: 'info',
                read: false,
                created_at: new Date().toISOString()
            },
            {
                id: 2,
                title: '案件状态更新',
                message: '案件 #2024001 状态已更新',
                type: 'success',
                read: true,
                created_at: new Date(Date.now() - 86400000).toISOString()
            }
        ],
        total: 2,
        unread: 1
    });
});



// 案件详情API
app.get('/api/cases/:id', (req, res) => {
    console.log('📍 Get case detail request for ID:', req.params.id);
    const caseId = parseInt(req.params.id);
    const caseDetail = createdCases.find(c => c.id === caseId);

    if (!caseDetail) {
        return res.status(404).json({
            success: false,
            error: '案件不存在',
            code: 'CASE_NOT_FOUND'
        });
    }

    res.json({
        success: true,
        data: {
            case: caseDetail
        }
    });
});

// 案件负责人分配API
app.post('/api/cases/:id/assign', (req, res) => {
    console.log('📍 Assign case request for ID:', req.params.id, 'Data:', req.body);
    const caseId = parseInt(req.params.id);
    const { owner_id, remark } = req.body;

    const caseIndex = createdCases.findIndex(c => c.id === caseId);
    if (caseIndex === -1) {
        return res.status(404).json({
            success: false,
            error: '案件不存在',
            code: 'CASE_NOT_FOUND'
        });
    }

    // 更新案件负责人
    createdCases[caseIndex].owner_id = owner_id;
    createdCases[caseIndex].updated_at = new Date().toISOString();

    res.json({
        success: true,
        data: {
            case: createdCases[caseIndex]
        },
        message: '负责人分配成功'
    });
});

// 案件更新API
app.put('/api/cases/:id', (req, res) => {
    console.log('📍 Update case request for ID:', req.params.id, 'Data:', req.body);
    const caseId = parseInt(req.params.id);
    const updateData = req.body;

    const caseIndex = createdCases.findIndex(c => c.id === caseId);
    if (caseIndex === -1) {
        return res.status(404).json({
            success: false,
            error: '案件不存在',
            code: 'CASE_NOT_FOUND'
        });
    }

    // 更新案件信息
    createdCases[caseIndex] = {
        ...createdCases[caseIndex],
        ...updateData,
        updated_at: new Date().toISOString()
    };

    res.json({
        success: true,
        data: {
            case: createdCases[caseIndex]
        },
        message: '案件更新成功'
    });
});

// 404 处理
app.use('*', (req, res) => {
    console.log('📍 404 route:', req.originalUrl);
    res.status(404).json({
        error: 'API endpoint not found',
        path: req.originalUrl,
        method: req.method
    });
});

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('❌ Server error:', error);
    res.status(500).json({
        error: error.message || 'Internal server error',
        ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
    });
});

// 启动服务器
console.log('🚀 Starting server...');
app.listen(PORT, '127.0.0.1', () => {
    console.log(`✅ Working backend server is running on port ${PORT}`);
    console.log(`📍 API URL: http://127.0.0.1:${PORT}`);
    console.log(`🏥 Health check: http://127.0.0.1:${PORT}/health`);
    console.log(`🔑 Login API: http://127.0.0.1:${PORT}/api/auth/login`);
});
