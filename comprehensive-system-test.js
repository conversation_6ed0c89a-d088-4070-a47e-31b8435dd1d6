/**
 * 争议管理系统全面用户体验测试脚本
 * 测试范围：前端UI、后端API、前后端集成、业务流程
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 测试配置
const testConfig = {
    backend: {
        baseURL: 'http://localhost:3001',
        timeout: 5000
    },
    frontend: {
        baseURL: 'http://localhost:3002',
        timeout: 5000
    }
};

// 测试结果记录
const testResults = {
    systemStatus: {},
    backendAPI: {},
    frontendUI: {},
    integration: {},
    issues: [],
    summary: {}
};

// 颜色输出函数
const colors = {
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    blue: (text) => `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => `\x1b[36m${text}\x1b[0m`
};

// 记录问题函数
function recordIssue(category, severity, description, impact, suggestion) {
    testResults.issues.push({
        category,
        severity, // 'high', 'medium', 'low'
        description,
        impact,
        suggestion,
        timestamp: new Date().toISOString()
    });

    const color = severity === 'high' ? colors.red :
        severity === 'medium' ? colors.yellow : colors.blue;
    console.log(color(`❌ [${severity.toUpperCase()}] ${category}: ${description}`));
}

// 记录成功函数
function recordSuccess(category, description) {
    console.log(colors.green(`✅ ${category}: ${description}`));
}

// 1. 系统运行状态验证
async function testSystemStatus() {
    console.log(colors.cyan('\n🔍 1. 系统运行状态验证'));

    try {
        // 测试后端健康检查
        const backendHealth = await axios.get(`${testConfig.backend.baseURL}/health`, {
            timeout: testConfig.backend.timeout
        });

        if (backendHealth.status === 200) {
            testResults.systemStatus.backend = 'running';
            recordSuccess('后端服务器', `运行正常 (端口: ${backendHealth.data.port})`);
        }
    } catch (error) {
        testResults.systemStatus.backend = 'error';
        recordIssue('系统状态', 'high', '后端服务器无法访问', '系统无法正常工作', '检查后端服务器启动状态和端口配置');
    }

    try {
        // 测试前端访问
        const frontendResponse = await axios.get(testConfig.frontend.baseURL, {
            timeout: testConfig.frontend.timeout
        });

        if (frontendResponse.status === 200) {
            testResults.systemStatus.frontend = 'running';
            recordSuccess('前端服务器', '运行正常');
        }
    } catch (error) {
        testResults.systemStatus.frontend = 'error';
        recordIssue('系统状态', 'high', '前端服务器无法访问', '用户无法访问系统界面', '检查前端服务器启动状态');
    }
}

// 2. 后端API接口测试
async function testBackendAPI() {
    console.log(colors.cyan('\n🔍 2. 后端API接口测试'));

    const apiTests = [
        {
            name: '基础信息接口',
            method: 'GET',
            url: '/',
            expectedStatus: 200
        },
        {
            name: '健康检查接口',
            method: 'GET',
            url: '/health',
            expectedStatus: 200
        },
        {
            name: '登录接口',
            method: 'POST',
            url: '/api/auth/login',
            data: { username: 'admin', password: 'admin123' },
            expectedStatus: 200
        },
        {
            name: '案件列表接口',
            method: 'GET',
            url: '/api/cases',
            expectedStatus: 200
        },
        {
            name: '统计数据接口',
            method: 'GET',
            url: '/api/stats/overview',
            expectedStatus: 200
        },
        {
            name: '通知列表接口',
            method: 'GET',
            url: '/api/notifications',
            expectedStatus: 200
        }
    ];

    for (const test of apiTests) {
        try {
            const requestConfig = {
                method: test.method,
                url: `${testConfig.backend.baseURL}${test.url}`,
                timeout: 5000
            };

            if (test.data) {
                requestConfig.data = test.data;
            }

            const response = await axios(requestConfig);

            if (response.status === test.expectedStatus) {
                testResults.backendAPI[test.name] = 'success';
                recordSuccess('API测试', `${test.name} - 响应正常`);

                // 检查响应数据结构
                if (response.data) {
                    if (test.url === '/api/auth/login' && response.data.success) {
                        recordSuccess('认证功能', '登录接口返回成功状态');
                    }
                    if (test.url === '/api/stats/overview' && response.data.data) {
                        recordSuccess('统计功能', '统计接口返回数据结构正确');
                    }
                }
            } else {
                testResults.backendAPI[test.name] = 'failed';
                recordIssue('API接口', 'medium', `${test.name} 返回状态码不正确`, '接口功能可能异常', '检查接口实现和返回状态');
            }
        } catch (error) {
            testResults.backendAPI[test.name] = 'error';
            recordIssue('API接口', 'high', `${test.name} 请求失败: ${error.message}`, '接口无法正常工作', '检查接口实现和网络连接');
        }
    }
}

// 3. 前端功能测试（通过API模拟）
async function testFrontendFunctionality() {
    console.log(colors.cyan('\n🔍 3. 前端功能测试'));

    // 由于无法直接测试前端DOM，我们通过检查前端所需的API是否正常来间接验证
    const frontendRequiredAPIs = [
        '登录接口',
        '案件列表接口',
        '统计数据接口',
        '通知列表接口'
    ];

    let workingAPIs = 0;
    for (const api of frontendRequiredAPIs) {
        if (testResults.backendAPI[api] === 'success') {
            workingAPIs++;
        }
    }

    const apiCoverage = (workingAPIs / frontendRequiredAPIs.length) * 100;

    if (apiCoverage >= 80) {
        testResults.frontendUI.apiSupport = 'good';
        recordSuccess('前端支持', `API支持度: ${apiCoverage.toFixed(1)}%`);
    } else if (apiCoverage >= 50) {
        testResults.frontendUI.apiSupport = 'partial';
        recordIssue('前端功能', 'medium', `API支持度较低: ${apiCoverage.toFixed(1)}%`, '部分前端功能可能无法正常工作', '修复失败的API接口');
    } else {
        testResults.frontendUI.apiSupport = 'poor';
        recordIssue('前端功能', 'high', `API支持度过低: ${apiCoverage.toFixed(1)}%`, '前端功能严重受限', '优先修复核心API接口');
    }
}

// 4. 业务流程测试
async function testBusinessFlow() {
    console.log(colors.cyan('\n🔍 4. 业务流程测试'));

    try {
        // 测试完整的用户登录流程
        console.log('测试用户登录流程...');
        const loginResponse = await axios.post(`${testConfig.backend.baseURL}/api/auth/login`, {
            username: 'admin',
            password: 'admin123'
        });

        if (loginResponse.data.success && loginResponse.data.token) {
            recordSuccess('业务流程', '用户登录流程正常');

            // 使用token测试需要认证的接口
            const authHeaders = {
                'Authorization': `Bearer ${loginResponse.data.token}`
            };

            // 测试案件创建流程
            try {
                const caseData = {
                    title: '测试案件',
                    type: '合同纠纷',
                    description: '这是一个测试案件',
                    priority: 'medium'
                };

                const createCaseResponse = await axios.post(
                    `${testConfig.backend.baseURL}/api/cases`,
                    caseData,
                    { headers: authHeaders }
                );

                if (createCaseResponse.data.success) {
                    recordSuccess('业务流程', '案件创建流程正常');
                    testResults.integration.caseCreation = 'success';
                } else {
                    recordIssue('业务流程', 'medium', '案件创建返回失败状态', '用户无法创建新案件', '检查案件创建逻辑和数据验证');
                }
            } catch (error) {
                recordIssue('业务流程', 'medium', `案件创建失败: ${error.message}`, '案件管理功能受限', '检查案件创建接口实现');
            }

        } else {
            recordIssue('业务流程', 'high', '登录接口未返回有效token', '用户无法正常登录系统', '检查JWT token生成逻辑');
        }
    } catch (error) {
        recordIssue('业务流程', 'high', `登录流程失败: ${error.message}`, '用户无法登录系统', '检查认证系统实现');
    }
}

// 5. 性能和响应时间测试
async function testPerformance() {
    console.log(colors.cyan('\n🔍 5. 性能和响应时间测试'));

    const performanceTests = [
        { name: '健康检查', url: '/health' },
        { name: '登录接口', url: '/api/auth/login', method: 'POST', data: { username: 'admin', password: 'admin123' } },
        { name: '案件列表', url: '/api/cases' },
        { name: '统计数据', url: '/api/stats/overview' }
    ];

    for (const test of performanceTests) {
        try {
            const startTime = Date.now();

            const requestConfig = {
                method: test.method || 'GET',
                url: `${testConfig.backend.baseURL}${test.url}`,
                timeout: 10000
            };

            if (test.data) {
                requestConfig.data = test.data;
            }

            await axios(requestConfig);
            const responseTime = Date.now() - startTime;

            if (responseTime < 1000) {
                recordSuccess('性能测试', `${test.name} 响应时间: ${responseTime}ms (优秀)`);
            } else if (responseTime < 3000) {
                recordIssue('性能', 'low', `${test.name} 响应时间较慢: ${responseTime}ms`, '用户体验可能受影响', '优化接口性能');
            } else {
                recordIssue('性能', 'medium', `${test.name} 响应时间过慢: ${responseTime}ms`, '用户体验明显受影响', '优化接口性能和数据库查询');
            }
        } catch (error) {
            recordIssue('性能', 'medium', `${test.name} 性能测试失败`, '无法评估接口性能', '修复接口错误后重新测试');
        }
    }
}

// 主测试函数
async function runComprehensiveTest() {
    console.log(colors.blue('🚀 开始争议管理系统全面用户体验测试\n'));

    await testSystemStatus();
    await testBackendAPI();
    await testFrontendFunctionality();
    await testBusinessFlow();
    await testPerformance();

    // 生成测试总结
    generateTestSummary();

    // 保存测试结果
    saveTestResults();
}

// 生成测试总结
function generateTestSummary() {
    console.log(colors.cyan('\n📊 测试总结'));

    const totalIssues = testResults.issues.length;
    const highIssues = testResults.issues.filter(i => i.severity === 'high').length;
    const mediumIssues = testResults.issues.filter(i => i.severity === 'medium').length;
    const lowIssues = testResults.issues.filter(i => i.severity === 'low').length;

    testResults.summary = {
        totalIssues,
        highIssues,
        mediumIssues,
        lowIssues,
        systemHealth: totalIssues === 0 ? 'excellent' :
            highIssues === 0 ? 'good' :
                highIssues <= 2 ? 'fair' : 'poor'
    };

    console.log(`总计发现问题: ${totalIssues}`);
    console.log(`高优先级问题: ${colors.red(highIssues)}`);
    console.log(`中优先级问题: ${colors.yellow(mediumIssues)}`);
    console.log(`低优先级问题: ${colors.blue(lowIssues)}`);

    const healthColor = testResults.summary.systemHealth === 'excellent' ? colors.green :
        testResults.summary.systemHealth === 'good' ? colors.cyan :
            testResults.summary.systemHealth === 'fair' ? colors.yellow : colors.red;

    console.log(`系统健康度: ${healthColor(testResults.summary.systemHealth)}`);
}

// 保存测试结果
function saveTestResults() {
    const reportPath = path.join(__dirname, 'test-results.json');
    fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
    console.log(colors.green(`\n📄 测试结果已保存到: ${reportPath}`));
}

// 运行测试
if (require.main === module) {
    runComprehensiveTest().catch(console.error);
}

module.exports = { runComprehensiveTest, testResults };
