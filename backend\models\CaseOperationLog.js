const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const CaseOperationLog = sequelize.define('CaseOperationLog', {
    id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
    },
    case_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '案件ID',
        references: {
            model: 'cases',
            key: 'id'
        }
    },
    operation_type: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '操作类型',
        validate: {
            isIn: [['创建', '编辑', '状态变更', '分配', '删除', '恢复', '永久删除']]
        }
    },
    operator_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '操作人ID',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    operation_detail: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '操作详情',
    },
    old_data: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: '操作前数据',
    },
    new_data: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: '操作后数据',
    },
    ip_address: {
        type: DataTypes.STRING(45),
        allowNull: true,
        comment: 'IP地址',
    },
    user_agent: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '用户代理',
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '操作时间',
    },
}, {
    tableName: 'case_operation_logs',
    timestamps: false,
    indexes: [
        {
            fields: ['case_id']
        },
        {
            fields: ['operator_id']
        },
        {
            fields: ['operation_type']
        },
        {
            fields: ['created_at']
        }
    ]
});

module.exports = CaseOperationLog;
