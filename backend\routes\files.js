const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const { CaseFile, Case, User, Log } = require('../models');
const { authenticate, requireCaseOwnerOrAdmin } = require('../middleware/auth');
const { upload, handleUploadError, extractFileInfo } = require('../middleware/upload');

/**
 * 上传案件文件
 * POST /api/files/upload/:caseId
 */
router.post('/upload/:caseId', 
    authenticate,
    requireCaseOwnerOrAdmin,
    upload.array('files', 10),
    handleUploadError,
    extractFileInfo,
    async (req, res) => {
        try {
            const { caseId } = req.params;
            const { description, file_category = '其他' } = req.body;

            // 验证案件是否存在
            const caseRecord = await Case.findByPk(caseId);
            if (!caseRecord) {
                return res.status(404).json({
                    error: 'Case not found',
                    code: 'CASE_NOT_FOUND'
                });
            }

            if (!req.fileInfos || req.fileInfos.length === 0) {
                return res.status(400).json({
                    error: 'No files uploaded',
                    code: 'NO_FILES'
                });
            }

            const uploadedFiles = [];

            // 保存文件信息到数据库
            for (const fileInfo of req.fileInfos) {
                const caseFile = await CaseFile.create({
                    case_id: caseId,
                    file_name: fileInfo.filename,
                    original_name: fileInfo.originalName,
                    file_path: fileInfo.path,
                    file_size: fileInfo.size,
                    file_type: fileInfo.mimetype,
                    file_category: file_category,
                    uploaded_by: req.user.id,
                    description: description || null
                });

                uploadedFiles.push({
                    id: caseFile.id,
                    original_name: caseFile.original_name,
                    file_size: caseFile.file_size,
                    file_type: caseFile.file_type,
                    file_category: caseFile.file_category,
                    uploaded_at: caseFile.uploaded_at
                });
            }

            // 记录操作日志
            await Log.create({
                user_id: req.user.id,
                action: '上传文件',
                module: '文件管理',
                detail: JSON.stringify({
                    case_id: caseId,
                    case_no: caseRecord.case_no,
                    files_count: uploadedFiles.length,
                    files: uploadedFiles.map(f => f.original_name)
                }),
                ip_address: req.ip,
                user_agent: req.get('User-Agent'),
                status: 'success'
            });

            res.status(201).json({
                message: 'Files uploaded successfully',
                files: uploadedFiles
            });

        } catch (error) {
            console.error('Upload files error:', error);
            res.status(500).json({
                error: 'Failed to upload files',
                code: 'UPLOAD_ERROR'
            });
        }
    }
);

/**
 * 获取案件文件列表
 * GET /api/files/case/:caseId
 */
router.get('/case/:caseId', authenticate, requireCaseOwnerOrAdmin, async (req, res) => {
    try {
        const { caseId } = req.params;
        const { category, page = 1, limit = 20 } = req.query;

        // 验证案件是否存在
        const caseRecord = await Case.findByPk(caseId);
        if (!caseRecord) {
            return res.status(404).json({
                error: 'Case not found',
                code: 'CASE_NOT_FOUND'
            });
        }

        const where = { case_id: caseId };
        if (category) {
            where.file_category = category;
        }

        const offset = (page - 1) * limit;

        const { count, rows } = await CaseFile.findAndCountAll({
            where,
            include: [{
                model: User,
                as: 'uploader',
                attributes: ['id', 'username', 'real_name']
            }],
            order: [['uploaded_at', 'DESC']],
            limit: parseInt(limit),
            offset: parseInt(offset)
        });

        res.json({
            files: rows,
            pagination: {
                total: count,
                page: parseInt(page),
                limit: parseInt(limit),
                pages: Math.ceil(count / limit)
            }
        });

    } catch (error) {
        console.error('Get case files error:', error);
        res.status(500).json({
            error: 'Failed to get case files',
            code: 'GET_FILES_ERROR'
        });
    }
});

/**
 * 下载文件
 * GET /api/files/download/:fileId
 */
router.get('/download/:fileId', authenticate, async (req, res) => {
    try {
        const { fileId } = req.params;

        const caseFile = await CaseFile.findByPk(fileId, {
            include: [{
                model: Case,
                as: 'case'
            }]
        });

        if (!caseFile) {
            return res.status(404).json({
                error: 'File not found',
                code: 'FILE_NOT_FOUND'
            });
        }

        // 权限检查：只有案件所有者或管理员可以下载
        const userRoles = req.user.roles.map(role => role.name);
        if (!userRoles.includes('admin') && caseFile.case.owner_id !== req.user.id) {
            return res.status(403).json({
                error: 'Access denied',
                code: 'ACCESS_DENIED'
            });
        }

        // 检查文件是否存在
        if (!fs.existsSync(caseFile.file_path)) {
            return res.status(404).json({
                error: 'File not found on disk',
                code: 'FILE_NOT_FOUND_ON_DISK'
            });
        }

        // 记录下载日志
        await Log.create({
            user_id: req.user.id,
            action: '下载文件',
            module: '文件管理',
            detail: JSON.stringify({
                file_id: caseFile.id,
                file_name: caseFile.original_name,
                case_id: caseFile.case_id,
                case_no: caseFile.case.case_no
            }),
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            status: 'success'
        });

        // 设置响应头
        res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(caseFile.original_name)}"`);
        res.setHeader('Content-Type', caseFile.file_type);
        res.setHeader('Content-Length', caseFile.file_size);

        // 发送文件
        const fileStream = fs.createReadStream(caseFile.file_path);
        fileStream.pipe(res);

    } catch (error) {
        console.error('Download file error:', error);
        res.status(500).json({
            error: 'Failed to download file',
            code: 'DOWNLOAD_ERROR'
        });
    }
});

/**
 * 删除文件
 * DELETE /api/files/:fileId
 */
router.delete('/:fileId', authenticate, async (req, res) => {
    try {
        const { fileId } = req.params;

        const caseFile = await CaseFile.findByPk(fileId, {
            include: [{
                model: Case,
                as: 'case'
            }]
        });

        if (!caseFile) {
            return res.status(404).json({
                error: 'File not found',
                code: 'FILE_NOT_FOUND'
            });
        }

        // 权限检查：只有案件所有者或管理员可以删除
        const userRoles = req.user.roles.map(role => role.name);
        if (!userRoles.includes('admin') && caseFile.case.owner_id !== req.user.id) {
            return res.status(403).json({
                error: 'Access denied',
                code: 'ACCESS_DENIED'
            });
        }

        // 删除物理文件
        if (fs.existsSync(caseFile.file_path)) {
            fs.unlinkSync(caseFile.file_path);
        }

        // 删除数据库记录
        await caseFile.destroy();

        // 记录删除日志
        await Log.create({
            user_id: req.user.id,
            action: '删除文件',
            module: '文件管理',
            detail: JSON.stringify({
                file_id: caseFile.id,
                file_name: caseFile.original_name,
                case_id: caseFile.case_id,
                case_no: caseFile.case.case_no
            }),
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            status: 'success'
        });

        res.json({
            message: 'File deleted successfully'
        });

    } catch (error) {
        console.error('Delete file error:', error);
        res.status(500).json({
            error: 'Failed to delete file',
            code: 'DELETE_ERROR'
        });
    }
});

module.exports = router;
