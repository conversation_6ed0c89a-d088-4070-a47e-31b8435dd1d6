<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库案件数据检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .test-result.success {
            background-color: #f6ffed;
            border-color: #52c41a;
            color: #389e0d;
        }
        .test-result.error {
            background-color: #fff2f0;
            border-color: #ff4d4f;
            color: #cf1322;
        }
        .test-result.info {
            background-color: #e6f7ff;
            border-color: #1890ff;
            color: #0050b3;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        input {
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            margin: 5px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ 数据库案件数据检查</h1>
        <p>检查数据库中是否有案件数据，以及案件列表API和详情API的差异</p>
        
        <div>
            <h3>测试配置</h3>
            <label>认证Token: </label>
            <input type="text" id="authToken" placeholder="Bearer token" style="width: 400px;">
            <br>
            <button onclick="quickLogin()">快速登录</button>
            <button onclick="testCasesList()">检查案件列表</button>
            <button onclick="testFirstCaseDetail()">测试第一个案件详情</button>
            <button onclick="createTestCase()">创建测试案件</button>
        </div>
    </div>

    <div class="container">
        <h3>🔐 登录状态</h3>
        <div id="loginResult" class="test-result info">点击"快速登录"按钮开始</div>
    </div>

    <div class="container">
        <h3>📋 案件列表检查</h3>
        <div id="caseListResult" class="test-result info">点击"检查案件列表"按钮开始</div>
    </div>

    <div class="container">
        <h3>📄 案件详情检查</h3>
        <div id="caseDetailResult" class="test-result info">点击"测试第一个案件详情"按钮开始</div>
    </div>

    <div class="container">
        <h3>➕ 创建测试案件</h3>
        <div id="createCaseResult" class="test-result info">点击"创建测试案件"按钮开始</div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001/api';
        let authToken = '';
        let firstCaseId = null;

        // 快速登录
        async function quickLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = '正在登录...';

            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    authToken = data.data.token;
                    document.getElementById('authToken').value = authToken;
                    
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 登录成功<br>
                        用户: ${data.data.user.username}<br>
                        用户ID: ${data.data.user.id}<br>
                        角色: ${data.data.user.roles?.map(r => r.name).join(', ') || '无角色'}
                    `;
                } else {
                    throw new Error(`登录失败: ${data.error || response.statusText}`);
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 登录失败: ${error.message}`;
            }
        }

        // 检查案件列表
        async function testCasesList() {
            const resultDiv = document.getElementById('caseListResult');
            authToken = document.getElementById('authToken').value;

            if (!authToken) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 请先登录获取Token';
                return;
            }

            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = '正在检查案件列表...';

            try {
                const response = await fetch(`${API_BASE}/cases?page=1&limit=10`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    const cases = data.data.cases || [];
                    if (cases.length > 0) {
                        firstCaseId = cases[0].id;
                    }
                    
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 案件列表获取成功<br>
                        总案件数: ${data.data.total || 0}<br>
                        当前页案件数: ${cases.length}<br>
                        ${cases.length > 0 ? `第一个案件ID: ${firstCaseId}` : '没有案件数据'}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    throw new Error(`获取失败: ${data.error || response.statusText}`);
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 案件列表检查失败: ${error.message}`;
            }
        }

        // 测试第一个案件详情
        async function testFirstCaseDetail() {
            const resultDiv = document.getElementById('caseDetailResult');
            authToken = document.getElementById('authToken').value;

            if (!authToken) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 请先登录获取Token';
                return;
            }

            if (!firstCaseId) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 请先检查案件列表获取案件ID';
                return;
            }

            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = `正在获取案件${firstCaseId}的详情...`;

            try {
                console.log(`请求URL: ${API_BASE}/cases/${firstCaseId}`);
                console.log(`Token: ${authToken}`);

                const response = await fetch(`${API_BASE}/cases/${firstCaseId}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                console.log(`响应状态: ${response.status}`);
                const data = await response.json();
                console.log('响应数据:', data);
                
                if (response.ok && data.success) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 案件详情获取成功<br>
                        案件ID: ${data.data.case.id}<br>
                        案件标题: ${data.data.case.title}<br>
                        案件状态: ${data.data.case.status}<br>
                        负责人: ${data.data.case.owner?.real_name || data.data.case.owner_name || '未设置'}<br>
                        <pre>${JSON.stringify(data.data.case, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `
                        ❌ 案件详情获取失败<br>
                        HTTP状态: ${response.status}<br>
                        错误代码: ${data.code || '无'}<br>
                        错误信息: ${data.error || data.message || '未知错误'}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
                console.error('请求错误:', error);
            }
        }

        // 创建测试案件
        async function createTestCase() {
            const resultDiv = document.getElementById('createCaseResult');
            authToken = document.getElementById('authToken').value;

            if (!authToken) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 请先登录获取Token';
                return;
            }

            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = '正在创建测试案件...';

            try {
                const caseData = {
                    title: '测试案件 - ' + new Date().toLocaleString(),
                    type: '合同纠纷',
                    description: '这是一个用于测试的案件',
                    priority: '中',
                    client_name: '测试客户',
                    client_contact: '13800138000',
                    amount: 50000,
                    deadline: '2025-12-31'
                };

                const response = await fetch(`${API_BASE}/cases`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(caseData)
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 测试案件创建成功<br>
                        案件ID: ${data.data.case.id}<br>
                        案件编号: ${data.data.case.case_no}<br>
                        案件标题: ${data.data.case.title}<br>
                        <pre>${JSON.stringify(data.data.case, null, 2)}</pre>
                    `;
                    
                    // 更新第一个案件ID
                    firstCaseId = data.data.case.id;
                } else {
                    throw new Error(`创建失败: ${data.error || response.statusText}`);
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 创建测试案件失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
