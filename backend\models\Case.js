const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Case = sequelize.define('Case', {
    id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
    },
    title: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '案件标题',
        validate: {
            notEmpty: true,
        }
    },
    case_no: {
        type: DataTypes.STRING(100),
        allowNull: false,
        unique: true,
        comment: '案件编号',
        validate: {
            notEmpty: true,
        }
    },
    type: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '案件类型',
        validate: {
            isIn: [['合同纠纷', '劳动争议', '知识产权', '公司法务', '其他']]
        }
    },
    status: {
        type: DataTypes.STRING(50),
        allowNull: false,
        defaultValue: '待处理',
        comment: '案件状态',
        validate: {
            isIn: [['待处理', '处理中', '已结案', '已归档', '已撤销', '已删除']]
        }
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '案件描述',
    },
    owner_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '负责人ID',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    priority: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: '中',
        comment: '优先级',
        validate: {
            isIn: [['低', '中', '高', '紧急']]
        }
    },
    deadline: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: '截止日期',
    },
    amount: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
        comment: '涉案金额',
    },
    client_name: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: '客户名称',
    },
    client_contact: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: '客户联系方式',
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '更新时间',
    },
    deleted_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: '删除时间（软删除）',
    },
}, {
    tableName: 'cases',
    timestamps: false,
    indexes: [
        {
            unique: true,
            fields: ['case_no']
        },
        {
            fields: ['owner_id']
        },
        {
            fields: ['status']
        },
        {
            fields: ['type']
        },
        {
            fields: ['priority']
        },
        {
            fields: ['created_at']
        }
    ],
    hooks: {
        beforeUpdate: (caseInstance) => {
            caseInstance.updated_at = new Date();
        }
    }
});

// 生成案件编号
Case.generateCaseNo = function () {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const timestamp = now.getTime().toString().slice(-6);

    return `CASE${year}${month}${day}${timestamp}`;
};

module.exports = Case;
