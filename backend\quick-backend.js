const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3001;

// 中间件
app.use(cors());
app.use(express.json());

// 模拟用户数据
const mockUser = {
  id: 1,
  username: 'admin',
  real_name: '管理员',
  email: '<EMAIL>',
  roles: [{ id: 1, name: 'admin', description: '管理员' }]
};

// 模拟JWT token
const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwidXNlcm5hbWUiOiJhZG1pbiIsImlhdCI6MTYzOTU2NzIwMCwiZXhwIjoxNjM5NjUzNjAwfQ.mock-token';

// 认证路由
app.post('/api/auth/login', (req, res) => {
  console.log('登录请求:', req.body);
  res.json({
    success: true,
    data: {
      token: mockToken,
      user: mockUser
    },
    message: '登录成功'
  });
});

app.post('/api/auth/logout', (req, res) => {
  console.log('登出请求');
  res.json({
    success: true,
    message: '登出成功'
  });
});

app.get('/api/auth/profile', (req, res) => {
  console.log('获取用户信息请求');
  res.json({
    success: true,
    data: mockUser
  });
});

// 统计数据路由
app.get('/api/stats/dashboard', (req, res) => {
  console.log('获取仪表板统计数据请求');
  res.json({
    success: true,
    data: {
      totalCases: 0,
      activeCases: 0,
      closedCases: 0,
      pendingCases: 0,
      recentCases: []
    }
  });
});

// 案件路由
app.get('/api/cases', (req, res) => {
  console.log('获取案件列表请求');
  res.json({
    success: true,
    data: {
      cases: [],
      total: 0,
      page: 1,
      pageSize: 10
    }
  });
});

app.get('/api/cases/:id', (req, res) => {
  console.log('获取案件详情请求:', req.params.id);
  res.json({
    success: true,
    data: {
      id: req.params.id,
      title: '测试案件',
      status: 'active',
      created_at: new Date().toISOString()
    }
  });
});

// 负责人路由
app.get('/api/responsibles', (req, res) => {
  console.log('获取负责人列表请求');
  res.json({
    success: true,
    data: []
  });
});

// 文件路由
app.get('/api/files', (req, res) => {
  console.log('获取文件列表请求');
  res.json({
    success: true,
    data: {
      files: [],
      total: 0
    }
  });
});

// 通知路由
app.get('/api/notifications', (req, res) => {
  console.log('获取通知列表请求');
  res.json({
    success: true,
    data: {
      notifications: [],
      total: 0,
      unreadCount: 0
    }
  });
});

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: '后端服务正常运行',
    timestamp: new Date().toISOString()
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: err.message
  });
});

// 404处理
app.use('*', (req, res) => {
  console.log('404请求:', req.method, req.originalUrl);
  res.status(404).json({
    success: false,
    message: '接口不存在'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`快速后端服务器已启动，端口: ${PORT}`);
  console.log(`健康检查: http://localhost:${PORT}/api/health`);
});

module.exports = app;
