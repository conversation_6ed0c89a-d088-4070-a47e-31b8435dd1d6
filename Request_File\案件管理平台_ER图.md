# 案件管理平台ER图

```mermaid
erDiagram
  USER {
    bigint id PK
    varchar username
    varchar password
    varchar real_name
    varchar email
    tinyint status
    datetime created_at
  }
  ROLE {
    bigint id PK
    varchar name
    varchar description
  }
  USER_ROLE {
    bigint id PK
    bigint user_id FK
    bigint role_id FK
  }
  CASE {
    bigint id PK
    varchar title
    varchar case_no
    varchar type
    varchar status
    text description
    bigint owner_id FK
    datetime created_at
  }
  CASE_FLOW {
    bigint id PK
    bigint case_id FK
    varchar action
    bigint operator_id FK
    text remark
    datetime created_at
  }
  CASE_ARCHIVE {
    bigint id PK
    bigint case_id FK
    text archive_info
    datetime created_at
  }
  CASE_FILE {
    bigint id PK
    bigint case_id FK
    varchar file_name
    varchar file_path
    bigint uploaded_by FK
    datetime uploaded_at
  }
  NOTIFY {
    bigint id PK
    bigint user_id FK
    varchar content
    tinyint status
    datetime created_at
  }
  LOG {
    bigint id PK
    bigint user_id FK
    varchar action
    text detail
    datetime created_at
  }
  
  USER_ROLE ||--|| USER : "user_id"
  USER_ROLE ||--|| ROLE : "role_id"
  CASE ||--o| USER : "owner_id"
  CASE_FLOW ||--o| CASE : "case_id"
  CASE_FLOW ||--o| USER : "operator_id"
  CASE_ARCHIVE ||--o| CASE : "case_id"
  CASE_FILE ||--o| CASE : "case_id"
  CASE_FILE ||--o| USER : "uploaded_by"
  NOTIFY ||--o| USER : "user_id"
  LOG ||--o| USER : "user_id"
``` 