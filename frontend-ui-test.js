/**
 * 前端用户界面功能测试脚本
 * 测试前端页面的加载、路由、组件渲染等功能
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 测试配置
const config = {
    frontend: 'http://localhost:3002',
    backend: 'http://localhost:3001'
};

// 测试结果
const testResults = {
    pageTests: {},
    componentTests: {},
    integrationTests: {},
    issues: [],
    summary: {}
};

// 颜色输出
const colors = {
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    blue: (text) => `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => `\x1b[36m${text}\x1b[0m`
};

function recordIssue(category, severity, description, impact, suggestion) {
    testResults.issues.push({
        category, severity, description, impact, suggestion,
        timestamp: new Date().toISOString()
    });
    
    const color = severity === 'high' ? colors.red : 
                  severity === 'medium' ? colors.yellow : colors.blue;
    console.log(color(`❌ [${severity.toUpperCase()}] ${category}: ${description}`));
}

function recordSuccess(category, description) {
    console.log(colors.green(`✅ ${category}: ${description}`));
}

// 1. 前端服务器连接测试
async function testFrontendConnection() {
    console.log(colors.cyan('\n🔍 1. 前端服务器连接测试'));
    
    try {
        const response = await axios.get(config.frontend, { timeout: 10000 });
        
        if (response.status === 200) {
            testResults.pageTests.connection = 'success';
            recordSuccess('前端连接', '前端服务器响应正常');
            
            // 检查HTML内容
            const htmlContent = response.data;
            if (htmlContent.includes('法务案件管理平台') || htmlContent.includes('Sie Dispute Manager')) {
                recordSuccess('页面内容', '页面包含预期的标题内容');
            } else {
                recordIssue('页面内容', 'medium', '页面标题内容不符合预期', '用户可能看到错误的页面标题', '检查前端页面标题配置');
            }
            
            // 检查React应用
            if (htmlContent.includes('react') || htmlContent.includes('React') || htmlContent.includes('id="root"')) {
                recordSuccess('技术栈', 'React应用结构正常');
            } else {
                recordIssue('技术栈', 'medium', '未检测到React应用结构', '前端可能无法正常渲染', '检查React应用配置');
            }
            
        } else {
            testResults.pageTests.connection = 'failed';
            recordIssue('前端连接', 'high', `前端服务器返回状态码: ${response.status}`, '前端无法正常访问', '检查前端服务器配置');
        }
    } catch (error) {
        testResults.pageTests.connection = 'error';
        recordIssue('前端连接', 'high', `前端服务器连接失败: ${error.message}`, '用户无法访问前端界面', '检查前端服务器启动状态和端口配置');
    }
}

// 2. 前端静态资源测试
async function testFrontendAssets() {
    console.log(colors.cyan('\n🔍 2. 前端静态资源测试'));
    
    const assetTests = [
        { name: 'CSS样式文件', path: '/src/index.css', type: 'css' },
        { name: 'JavaScript入口', path: '/src/main.jsx', type: 'js' },
        { name: 'App组件', path: '/src/App.jsx', type: 'js' }
    ];
    
    for (const asset of assetTests) {
        try {
            const response = await axios.get(`${config.frontend}${asset.path}`, { timeout: 5000 });
            
            if (response.status === 200) {
                testResults.pageTests[asset.name] = 'success';
                recordSuccess('静态资源', `${asset.name} 加载正常`);
            } else {
                testResults.pageTests[asset.name] = 'failed';
                recordIssue('静态资源', 'medium', `${asset.name} 返回状态码: ${response.status}`, '页面样式或功能可能异常', '检查静态资源配置');
            }
        } catch (error) {
            testResults.pageTests[asset.name] = 'error';
            // 对于开发环境，某些资源可能通过不同方式加载，这不一定是错误
            console.log(colors.yellow(`⚠️ 静态资源: ${asset.name} 无法直接访问 (开发环境正常)`));
        }
    }
}

// 3. API集成测试
async function testAPIIntegration() {
    console.log(colors.cyan('\n🔍 3. 前后端API集成测试'));
    
    try {
        // 测试CORS配置
        const corsTest = await axios.options(config.backend, {
            headers: {
                'Origin': config.frontend,
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type'
            }
        });
        
        recordSuccess('CORS配置', '跨域请求配置正常');
        testResults.integrationTests.cors = 'success';
        
    } catch (error) {
        recordIssue('CORS配置', 'high', 'CORS跨域配置可能有问题', '前端无法正常调用后端API', '检查后端CORS中间件配置');
        testResults.integrationTests.cors = 'error';
    }
    
    try {
        // 测试前端到后端的API调用
        const loginTest = await axios.post(`${config.backend}/api/auth/login`, {
            username: 'admin',
            password: 'admin123'
        }, {
            headers: {
                'Origin': config.frontend,
                'Content-Type': 'application/json'
            }
        });
        
        if (loginTest.data.success && loginTest.data.token) {
            recordSuccess('API集成', '前端登录API调用成功');
            testResults.integrationTests.login = 'success';
            
            // 使用token测试其他API
            const token = loginTest.data.token;
            const authHeaders = {
                'Authorization': `Bearer ${token}`,
                'Origin': config.frontend
            };
            
            // 测试案件列表API
            const casesTest = await axios.get(`${config.backend}/api/cases`, { headers: authHeaders });
            if (casesTest.data.success) {
                recordSuccess('API集成', '案件列表API调用成功');
                testResults.integrationTests.cases = 'success';
            }
            
            // 测试统计API
            const statsTest = await axios.get(`${config.backend}/api/stats/overview`, { headers: authHeaders });
            if (statsTest.data.success) {
                recordSuccess('API集成', '统计数据API调用成功');
                testResults.integrationTests.stats = 'success';
            }
            
        } else {
            recordIssue('API集成', 'high', '登录API未返回有效响应', '前端无法完成用户认证', '检查登录API实现');
        }
        
    } catch (error) {
        recordIssue('API集成', 'high', `API集成测试失败: ${error.message}`, '前后端无法正常通信', '检查网络连接和API配置');
        testResults.integrationTests.login = 'error';
    }
}

// 4. 前端组件功能测试（通过文件检查）
async function testFrontendComponents() {
    console.log(colors.cyan('\n🔍 4. 前端组件结构测试'));
    
    const componentPaths = [
        'frontend/src/App.jsx',
        'frontend/src/components/Layout/Layout.jsx',
        'frontend/src/pages/Auth/Login.jsx',
        'frontend/src/pages/Dashboard/Dashboard.jsx',
        'frontend/src/services/api.js',
        'frontend/src/utils/auth.js'
    ];
    
    for (const componentPath of componentPaths) {
        try {
            const fullPath = path.join(__dirname, componentPath);
            if (fs.existsSync(fullPath)) {
                const content = fs.readFileSync(fullPath, 'utf8');
                
                // 检查组件基本结构
                if (componentPath.endsWith('.jsx')) {
                    if (content.includes('import') && content.includes('export')) {
                        recordSuccess('组件结构', `${path.basename(componentPath)} 组件结构正常`);
                        testResults.componentTests[path.basename(componentPath)] = 'success';
                    } else {
                        recordIssue('组件结构', 'medium', `${path.basename(componentPath)} 组件结构异常`, '组件可能无法正常工作', '检查组件导入导出语法');
                    }
                }
                
                // 检查API服务配置
                if (componentPath.includes('api.js')) {
                    if (content.includes('axios') && content.includes('baseURL')) {
                        recordSuccess('API配置', 'API服务配置正常');
                    } else {
                        recordIssue('API配置', 'medium', 'API服务配置可能不完整', '前端API调用可能失败', '检查axios配置');
                    }
                }
                
            } else {
                recordIssue('组件文件', 'medium', `组件文件不存在: ${componentPath}`, '相关功能可能无法使用', '检查文件路径和组件实现');
                testResults.componentTests[path.basename(componentPath)] = 'missing';
            }
        } catch (error) {
            recordIssue('组件检查', 'low', `无法检查组件: ${componentPath}`, '无法验证组件状态', '检查文件权限');
        }
    }
}

// 5. 响应式设计测试（模拟）
async function testResponsiveDesign() {
    console.log(colors.cyan('\n🔍 5. 响应式设计测试'));
    
    try {
        // 检查CSS文件中的响应式设计
        const cssFiles = [
            'frontend/src/index.css',
            'frontend/src/App.css'
        ];
        
        let hasResponsiveDesign = false;
        
        for (const cssFile of cssFiles) {
            try {
                const fullPath = path.join(__dirname, cssFile);
                if (fs.existsSync(fullPath)) {
                    const content = fs.readFileSync(fullPath, 'utf8');
                    
                    if (content.includes('@media') || content.includes('responsive') || content.includes('mobile')) {
                        hasResponsiveDesign = true;
                        recordSuccess('响应式设计', `${path.basename(cssFile)} 包含响应式样式`);
                    }
                }
            } catch (error) {
                // 忽略文件读取错误
            }
        }
        
        if (!hasResponsiveDesign) {
            recordIssue('响应式设计', 'low', '未检测到响应式设计样式', '移动端用户体验可能不佳', '添加响应式CSS样式');
        }
        
        testResults.componentTests.responsive = hasResponsiveDesign ? 'success' : 'missing';
        
    } catch (error) {
        recordIssue('响应式设计', 'low', '无法检查响应式设计', '无法验证移动端适配', '检查CSS文件');
    }
}

// 生成测试总结
function generateTestSummary() {
    console.log(colors.cyan('\n📊 前端测试总结'));
    
    const totalIssues = testResults.issues.length;
    const highIssues = testResults.issues.filter(i => i.severity === 'high').length;
    const mediumIssues = testResults.issues.filter(i => i.severity === 'medium').length;
    const lowIssues = testResults.issues.filter(i => i.severity === 'low').length;
    
    testResults.summary = {
        totalIssues,
        highIssues,
        mediumIssues,
        lowIssues,
        frontendHealth: totalIssues === 0 ? 'excellent' : 
                       highIssues === 0 ? 'good' : 
                       highIssues <= 2 ? 'fair' : 'poor'
    };
    
    console.log(`前端总计问题: ${totalIssues}`);
    console.log(`高优先级问题: ${colors.red(highIssues)}`);
    console.log(`中优先级问题: ${colors.yellow(mediumIssues)}`);
    console.log(`低优先级问题: ${colors.blue(lowIssues)}`);
    
    const healthColor = testResults.summary.frontendHealth === 'excellent' ? colors.green :
                       testResults.summary.frontendHealth === 'good' ? colors.cyan :
                       testResults.summary.frontendHealth === 'fair' ? colors.yellow : colors.red;
    
    console.log(`前端健康度: ${healthColor(testResults.summary.frontendHealth)}`);
}

// 主测试函数
async function runFrontendTest() {
    console.log(colors.blue('🚀 开始前端用户界面功能测试\n'));
    
    await testFrontendConnection();
    await testFrontendAssets();
    await testAPIIntegration();
    await testFrontendComponents();
    await testResponsiveDesign();
    
    generateTestSummary();
    
    // 保存测试结果
    const reportPath = path.join(__dirname, 'frontend-test-results.json');
    fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
    console.log(colors.green(`\n📄 前端测试结果已保存到: ${reportPath}`));
}

// 运行测试
if (require.main === module) {
    runFrontendTest().catch(console.error);
}

module.exports = { runFrontendTest, testResults };
