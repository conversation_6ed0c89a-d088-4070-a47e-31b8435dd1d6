const { Responsible } = require('../models');

/**
 * 初始化负责人数据
 */
async function initResponsibles() {
    try {
        console.log('🔄 开始初始化负责人数据...');

        // 检查是否已有数据
        const existingCount = await Responsible.count();
        if (existingCount > 0) {
            console.log(`✅ 负责人表已有 ${existingCount} 条数据，跳过初始化`);
            return;
        }

        // 初始负责人数据
        const initialResponsibles = [
            {
                name: '胡聪',
                email: '<EMAIL>',
                phone: '13800138001',
                department: '法务部',
                position: '法务经理',
                status: 1,
                sort_order: 1
            },
            {
                name: '陈丽仪',
                email: '<EMAIL>',
                phone: '13800138002',
                department: '法务部',
                position: '法务专员',
                status: 1,
                sort_order: 2
            },
            {
                name: '林诗如',
                email: '<EMAIL>',
                phone: '13800138003',
                department: '法务部',
                position: '法务专员',
                status: 1,
                sort_order: 3
            },
            {
                name: '李明茜',
                email: 'liming<PERSON>@company.com',
                phone: '13800138004',
                department: '法务部',
                position: '法务助理',
                status: 1,
                sort_order: 4
            },
            {
                name: '李翠婷',
                email: '<EMAIL>',
                phone: '13800138005',
                department: '法务部',
                position: '法务助理',
                status: 1,
                sort_order: 5
            }
        ];

        // 批量创建负责人
        const createdResponsibles = await Responsible.bulkCreate(initialResponsibles);
        
        console.log(`✅ 成功创建 ${createdResponsibles.length} 个负责人:`);
        createdResponsibles.forEach(responsible => {
            console.log(`   - ${responsible.name} (${responsible.position})`);
        });

    } catch (error) {
        console.error('❌ 初始化负责人数据失败:', error);
        throw error;
    }
}

/**
 * 同步负责人表结构
 */
async function syncResponsibleTable() {
    try {
        console.log('🔄 同步负责人表结构...');
        
        // 同步表结构（如果表不存在则创建）
        await Responsible.sync({ alter: true });
        
        console.log('✅ 负责人表结构同步完成');
        
        // 初始化数据
        await initResponsibles();
        
    } catch (error) {
        console.error('❌ 同步负责人表结构失败:', error);
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    syncResponsibleTable()
        .then(() => {
            console.log('🎉 负责人管理功能初始化完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 初始化失败:', error);
            process.exit(1);
        });
}

module.exports = {
    initResponsibles,
    syncResponsibleTable
};
