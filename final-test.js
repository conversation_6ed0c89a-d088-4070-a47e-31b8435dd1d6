const http = require('http');

console.log('🔍 进行最终连接测试...');

// 测试后端健康检查
function testBackend() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: '127.0.0.1',
            port: 3001,
            path: '/health',
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                if (res.statusCode === 200) {
                    console.log('✅ 后端健康检查通过');
                    console.log('📊 后端响应:', JSON.parse(data));
                    resolve(true);
                } else {
                    console.log('❌ 后端健康检查失败:', res.statusCode);
                    reject(false);
                }
            });
        });

        req.on('error', (e) => {
            console.error('❌ 后端连接错误:', e.message);
            reject(false);
        });

        req.end();
    });
}

// 测试前端服务器
function testFrontend() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 5173,
            path: '/',
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            if (res.statusCode === 200) {
                console.log('✅ 前端服务器可访问');
                resolve(true);
            } else {
                console.log('❌ 前端服务器测试失败:', res.statusCode);
                reject(false);
            }
        });

        req.on('error', (e) => {
            console.error('❌ 前端连接错误:', e.message);
            reject(false);
        });

        req.end();
    });
}

// 运行所有测试
async function runFinalTests() {
    try {
        console.log('🚀 开始最终连接测试...\n');
        
        await testBackend();
        await testFrontend();
        
        console.log('\n🎉 所有测试通过！您的法务案件管理平台已准备就绪。');
        console.log('\n📍 访问地址：');
        console.log('   前端应用: http://localhost:5173');
        console.log('   后端 API: http://127.0.0.1:3001');
        console.log('   健康检查: http://127.0.0.1:3001/health');
        console.log('\n🔧 启动命令：');
        console.log('   后端: cd backend && node working-server.js');
        console.log('   前端: cd frontend && npm run dev');
        
    } catch (error) {
        console.log('\n❌ 部分测试失败，请检查服务器状态。');
    }
}

runFinalTests();
