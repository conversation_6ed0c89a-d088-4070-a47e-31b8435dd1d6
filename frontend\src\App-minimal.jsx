import React from 'react';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <div className="App" style={{ padding: '20px' }}>
        <h1>法务案件管理平台</h1>
        <p>系统正在运行中...</p>
        <div style={{ marginTop: '20px' }}>
          <h2>系统状态</h2>
          <ul>
            <li>✅ 前端服务器：正常运行</li>
            <li>✅ 后端 API：正常连接</li>
            <li>✅ 数据库：连接正常</li>
          </ul>
        </div>
        <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f0f0f0', borderRadius: '5px' }}>
          <p><strong>访问地址：</strong></p>
          <p>前端：http://localhost:5173</p>
          <p>后端：http://127.0.0.1:3001</p>
        </div>
      </div>
    </ConfigProvider>
  );
}

export default App;
