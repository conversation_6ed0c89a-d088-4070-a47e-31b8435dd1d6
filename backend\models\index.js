const sequelize = require('../config/database');

// 导入所有模型
const User = require('./User');
const Role = require('./Role');
const UserRole = require('./UserRole');
const Case = require('./Case');
const CaseFlow = require('./CaseFlow');
const CaseArchive = require('./CaseArchive');
const CaseFile = require('./CaseFile');
const CaseFieldDef = require('./caseFieldDef');
const CaseFieldValue = require('./caseFieldValue');
const Notify = require('./Notify');
const Log = require('./Log');
const Responsible = require('./Responsible');

// 定义模型关联关系

// 用户和角色的多对多关系
User.belongsToMany(Role, {
    through: UserRole,
    foreignKey: 'user_id',
    otherKey: 'role_id',
    as: 'roles'
});

Role.belongsToMany(User, {
    through: UserRole,
    foreignKey: 'role_id',
    otherKey: 'user_id',
    as: 'users'
});

// 用户角色关联
UserRole.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
UserRole.belongsTo(Role, { foreignKey: 'role_id', as: 'role' });

// 案件和用户的关系
Case.belongsTo(User, { foreignKey: 'owner_id', as: 'owner' });
User.hasMany(Case, { foreignKey: 'owner_id', as: 'ownedCases' });

// 案件流转记录
CaseFlow.belongsTo(Case, { foreignKey: 'case_id', as: 'case' });
CaseFlow.belongsTo(User, { foreignKey: 'operator_id', as: 'operator' });
Case.hasMany(CaseFlow, { foreignKey: 'case_id', as: 'flows' });
User.hasMany(CaseFlow, { foreignKey: 'operator_id', as: 'operations' });

// 案件归档
CaseArchive.belongsTo(Case, { foreignKey: 'case_id', as: 'case' });
CaseArchive.belongsTo(User, { foreignKey: 'archived_by', as: 'archivedBy' });
Case.hasOne(CaseArchive, { foreignKey: 'case_id', as: 'archive' });
User.hasMany(CaseArchive, { foreignKey: 'archived_by', as: 'archivedCases' });

// 案件文件
CaseFile.belongsTo(Case, { foreignKey: 'case_id', as: 'case' });
CaseFile.belongsTo(User, { foreignKey: 'uploaded_by', as: 'uploader' });
Case.hasMany(CaseFile, { foreignKey: 'case_id', as: 'files' });
User.hasMany(CaseFile, { foreignKey: 'uploaded_by', as: 'uploadedFiles' });

// 案件自定义字段
CaseFieldValue.belongsTo(Case, { foreignKey: 'case_id', as: 'case' });
CaseFieldValue.belongsTo(CaseFieldDef, { foreignKey: 'field_id', as: 'fieldDef' });
Case.hasMany(CaseFieldValue, { foreignKey: 'case_id', as: 'fieldValues' });
CaseFieldDef.hasMany(CaseFieldValue, { foreignKey: 'field_id', as: 'values' });

// 通知
Notify.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
Notify.belongsTo(User, { foreignKey: 'sender_id', as: 'sender' });
User.hasMany(Notify, { foreignKey: 'user_id', as: 'notifications' });
User.hasMany(Notify, { foreignKey: 'sender_id', as: 'sentNotifications' });

// 日志
Log.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
User.hasMany(Log, { foreignKey: 'user_id', as: 'logs' });

// 负责人和用户的关联关系（创建人、更新人）
Responsible.belongsTo(User, {
    foreignKey: 'created_by',
    as: 'creator',
    constraints: false
});

Responsible.belongsTo(User, {
    foreignKey: 'updated_by',
    as: 'updater',
    constraints: false
});

// 导出所有模型
const models = {
    sequelize,
    User,
    Role,
    UserRole,
    Case,
    CaseFlow,
    CaseArchive,
    CaseFile,
    CaseFieldDef,
    CaseFieldValue,
    Notify,
    Log,
    Responsible
};

module.exports = models;
