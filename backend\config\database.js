const { Sequelize } = require('sequelize');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const sequelize = new Sequelize(
    process.env.DB_NAME,
    process.env.DB_USER,
    process.env.DB_PASS,
    {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        dialect: 'mysql',
        logging: false,
        define: {
            freezeTableName: true,
        },
    }
);

module.exports = sequelize;

console.log('DB_USER:', process.env.DB_USER, 'DB_PASS:', process.env.DB_PASS);