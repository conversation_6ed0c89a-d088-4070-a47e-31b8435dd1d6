# 案件管理平台系统整体架构图

```mermaid
flowchart LR
  subgraph 前端
    FE["前端SPA（React/Vue）"]
  end
  subgraph 后端
    BE["后端API服务（Node.js/Express 或 Spring Boot）"]
    FS["文件存储（本地/对象存储）"]
    DB["数据库（MySQL/PostgreSQL）"]
    LOG["日志与审计"]
  end
  FE -- "RESTful API/鉴权" --> BE
  BE -- "读写" --> DB
  BE -- "上传/下载" --> FS
  BE -- "日志记录" --> LOG
  FE -- "文件上传/下载" --> BE
  FE -- "鉴权/权限校验" --> BE
  BE -- "消息/通知" --> FE
``` 