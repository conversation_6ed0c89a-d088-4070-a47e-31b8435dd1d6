const { Sequelize } = require('sequelize');
const sequelize = require('../config/database');

console.log('🔄 开始删除负责人表中的position列...');

async function removePositionColumn() {
    try {
        // 测试数据库连接
        await sequelize.authenticate();
        console.log('✅ 数据库连接成功');

        // 检查表是否存在
        const [tables] = await sequelize.query("SHOW TABLES LIKE 'responsibles'");
        if (tables.length === 0) {
            console.log('⚠️  responsibles表不存在，无需删除position列');
            return;
        }

        // 检查position列是否存在
        const [columns] = await sequelize.query("SHOW COLUMNS FROM responsibles LIKE 'position'");
        if (columns.length === 0) {
            console.log('✅ position列已经不存在，无需删除');
            return;
        }

        console.log('📍 发现position列，准备删除...');

        // 删除position列
        await sequelize.query('ALTER TABLE responsibles DROP COLUMN position');
        console.log('✅ 成功删除position列');

        // 验证删除结果
        const [verifyColumns] = await sequelize.query("SHOW COLUMNS FROM responsibles LIKE 'position'");
        if (verifyColumns.length === 0) {
            console.log('✅ 验证成功：position列已被删除');
        } else {
            console.log('❌ 验证失败：position列仍然存在');
        }

        // 显示当前表结构
        console.log('\n📋 当前responsibles表结构:');
        const [currentColumns] = await sequelize.query('SHOW COLUMNS FROM responsibles');
        currentColumns.forEach(col => {
            console.log(`   - ${col.Field} (${col.Type}) ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Key ? `[${col.Key}]` : ''}`);
        });

    } catch (error) {
        console.error('❌ 删除position列失败:', error);
        throw error;
    } finally {
        await sequelize.close();
        console.log('📍 数据库连接已关闭');
    }
}

// 运行迁移
removePositionColumn()
    .then(() => {
        console.log('\n🎉 position列删除完成！');
        process.exit(0);
    })
    .catch((error) => {
        console.error('\n❌ 迁移失败:', error);
        process.exit(1);
    });
