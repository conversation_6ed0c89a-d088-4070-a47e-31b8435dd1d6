const express = require('express');
const router = express.Router();
const { User, Role } = require('../models');
const { authenticate, requireAdmin } = require('../middleware/auth');

/**
 * 获取用户列表
 * GET /api/users
 */
router.get('/', authenticate, async (req, res) => {
    try {
        const {
            page = 1,
            limit = 50,
            status,
            search,
            role
        } = req.query;

        const offset = (page - 1) * limit;
        const where = {};

        // 状态过滤
        if (status !== undefined) {
            where.status = parseInt(status);
        }

        // 搜索过滤
        if (search) {
            const { Op } = require('sequelize');
            where[Op.or] = [
                { username: { [Op.like]: `%${search}%` } },
                { real_name: { [Op.like]: `%${search}%` } },
                { email: { [Op.like]: `%${search}%` } }
            ];
        }

        const include = [{
            model: Role,
            as: 'roles',
            attributes: ['id', 'name', 'description']
        }];

        // 角色过滤
        if (role) {
            include[0].where = { name: role };
        }

        const { count, rows } = await User.findAndCountAll({
            where,
            include,
            order: [['created_at', 'DESC']],
            limit: parseInt(limit),
            offset: parseInt(offset),
            distinct: true
        });

        res.json({
            users: rows,
            pagination: {
                total: count,
                page: parseInt(page),
                limit: parseInt(limit),
                pages: Math.ceil(count / limit)
            }
        });

    } catch (error) {
        console.error('Get users error:', error);
        res.status(500).json({
            error: 'Failed to get users',
            code: 'GET_USERS_ERROR'
        });
    }
});

/**
 * 获取用户详情
 * GET /api/users/:id
 */
router.get('/:id', authenticate, async (req, res) => {
    try {
        const user = await User.findByPk(req.params.id, {
            include: [{
                model: Role,
                as: 'roles',
                attributes: ['id', 'name', 'description']
            }]
        });

        if (!user) {
            return res.status(404).json({
                error: 'User not found',
                code: 'USER_NOT_FOUND'
            });
        }

        res.json({ user });

    } catch (error) {
        console.error('Get user detail error:', error);
        res.status(500).json({
            error: 'Failed to get user detail',
            code: 'GET_USER_ERROR'
        });
    }
});

/**
 * 创建用户（管理员专用）
 * POST /api/users
 */
router.post('/', authenticate, requireAdmin, async (req, res) => {
    try {
        const { username, password, real_name, email, role_ids = [] } = req.body;

        // 验证必填字段
        if (!username || !password || !real_name) {
            return res.status(400).json({
                error: 'Username, password and real_name are required',
                code: 'MISSING_FIELDS'
            });
        }

        // 检查用户名是否已存在
        const existingUser = await User.findOne({ where: { username } });
        if (existingUser) {
            return res.status(409).json({
                error: 'Username already exists',
                code: 'USERNAME_EXISTS'
            });
        }

        // 检查邮箱是否已存在
        if (email) {
            const existingEmail = await User.findOne({ where: { email } });
            if (existingEmail) {
                return res.status(409).json({
                    error: 'Email already exists',
                    code: 'EMAIL_EXISTS'
                });
            }
        }

        // 创建用户
        const user = await User.create({
            username,
            password,
            real_name,
            email,
            status: 1
        });

        // 分配角色
        if (role_ids.length > 0) {
            const roles = await Role.findAll({
                where: { id: role_ids }
            });
            await user.setRoles(roles);
        }

        // 返回创建的用户（包含角色信息）
        const createdUser = await User.findByPk(user.id, {
            include: [{
                model: Role,
                as: 'roles',
                attributes: ['id', 'name', 'description']
            }]
        });

        res.status(201).json({
            message: 'User created successfully',
            user: createdUser
        });

    } catch (error) {
        console.error('Create user error:', error);
        res.status(500).json({
            error: 'Failed to create user',
            code: 'CREATE_USER_ERROR'
        });
    }
});

/**
 * 更新用户信息（管理员专用）
 * PUT /api/users/:id
 */
router.put('/:id', authenticate, requireAdmin, async (req, res) => {
    try {
        const user = await User.findByPk(req.params.id);
        if (!user) {
            return res.status(404).json({
                error: 'User not found',
                code: 'USER_NOT_FOUND'
            });
        }

        const { username, real_name, email, status, role_ids } = req.body;

        // 检查用户名是否已被其他用户使用
        if (username && username !== user.username) {
            const existingUser = await User.findOne({ where: { username } });
            if (existingUser) {
                return res.status(409).json({
                    error: 'Username already exists',
                    code: 'USERNAME_EXISTS'
                });
            }
        }

        // 检查邮箱是否已被其他用户使用
        if (email && email !== user.email) {
            const existingEmail = await User.findOne({ where: { email } });
            if (existingEmail) {
                return res.status(409).json({
                    error: 'Email already exists',
                    code: 'EMAIL_EXISTS'
                });
            }
        }

        // 更新用户信息
        const updateData = {};
        if (username) updateData.username = username;
        if (real_name) updateData.real_name = real_name;
        if (email !== undefined) updateData.email = email;
        if (status !== undefined) updateData.status = parseInt(status);

        await user.update(updateData);

        // 更新角色
        if (role_ids && Array.isArray(role_ids)) {
            const roles = await Role.findAll({
                where: { id: role_ids }
            });
            await user.setRoles(roles);
        }

        // 返回更新后的用户信息
        const updatedUser = await User.findByPk(user.id, {
            include: [{
                model: Role,
                as: 'roles',
                attributes: ['id', 'name', 'description']
            }]
        });

        res.json({
            message: 'User updated successfully',
            user: updatedUser
        });

    } catch (error) {
        console.error('Update user error:', error);
        res.status(500).json({
            error: 'Failed to update user',
            code: 'UPDATE_USER_ERROR'
        });
    }
});

/**
 * 删除用户（管理员专用）
 * DELETE /api/users/:id
 */
router.delete('/:id', authenticate, requireAdmin, async (req, res) => {
    try {
        const user = await User.findByPk(req.params.id);
        if (!user) {
            return res.status(404).json({
                error: 'User not found',
                code: 'USER_NOT_FOUND'
            });
        }

        // 不能删除自己
        if (user.id === req.user.id) {
            return res.status(400).json({
                error: 'Cannot delete yourself',
                code: 'CANNOT_DELETE_SELF'
            });
        }

        await user.destroy();

        res.json({
            message: 'User deleted successfully'
        });

    } catch (error) {
        console.error('Delete user error:', error);
        res.status(500).json({
            error: 'Failed to delete user',
            code: 'DELETE_USER_ERROR'
        });
    }
});

module.exports = router;
