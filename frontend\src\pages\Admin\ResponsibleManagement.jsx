import React, { useState, useEffect } from 'react';
import {
  Typography,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  InputNumber,
  message,
  Popconfirm,
  Card,
  Row,
  Col,
  Tag
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined
} from '@ant-design/icons';

import { responsiblesAPI } from '../../services/responsibles';
import { isAdmin } from '../../utils/auth';

const { Title } = Typography;
const { Option } = Select;

const ResponsibleManagement = () => {
  const [responsibles, setResponsibles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingResponsible, setEditingResponsible] = useState(null);
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('');

  // 检查管理员权限
  const hasAdminPermission = isAdmin();

  // 获取负责人列表
  const fetchResponsibles = async () => {
    setLoading(true);
    try {
      const params = {};
      if (searchText) params.search = searchText;
      if (statusFilter !== '') params.status = statusFilter;

      const response = await responsiblesAPI.getResponsibles(params);
      if (response.data && response.data.responsibles) {
        setResponsibles(response.data.responsibles);
      }
    } catch (error) {
      console.error('获取负责人列表失败:', error);
      message.error('获取负责人列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchResponsibles();
  }, [searchText, statusFilter]);

  // 打开创建/编辑模态框
  const openModal = (responsible = null) => {
    setEditingResponsible(responsible);
    setModalVisible(true);

    if (responsible) {
      form.setFieldsValue(responsible);
    } else {
      form.resetFields();
    }
  };

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false);
    setEditingResponsible(null);
    form.resetFields();
  };

  // 保存负责人
  const handleSave = async (values) => {
    try {
      if (editingResponsible) {
        await responsiblesAPI.updateResponsible(editingResponsible.id, values);
        message.success('负责人信息更新成功');
      } else {
        await responsiblesAPI.createResponsible(values);
        message.success('负责人创建成功');
      }

      closeModal();
      fetchResponsibles();
    } catch (error) {
      console.error('保存负责人失败:', error);
      message.error('保存失败，请稍后重试');
    }
  };

  // 删除负责人
  const handleDelete = async (id) => {
    try {
      await responsiblesAPI.deleteResponsible(id);
      message.success('负责人删除成功');
      fetchResponsibles();
    } catch (error) {
      console.error('删除负责人失败:', error);
      message.error('删除失败，请稍后重试');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '排序',
      dataIndex: 'sort_order',
      key: 'sort_order',
      width: 80,
      sorter: (a, b) => a.sort_order - b.sort_order,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },

    {
      title: '部门',
      dataIndex: 'department',
      key: 'department',
      width: 120,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 200,
    },
    {
      title: '电话',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Tag color={status === 1 ? 'green' : 'red'}>
          {status === 1 ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => openModal(record)}
            disabled={!hasAdminPermission}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个负责人吗？"
            onConfirm={() => handleDelete(record.id)}
            disabled={!hasAdminPermission}
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              disabled={!hasAdminPermission}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  if (!hasAdminPermission) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Title level={4}>权限不足</Title>
          <p>您没有权限访问负责人管理功能</p>
        </div>
      </Card>
    );
  }

  return (
    <div>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Title level={3} style={{ margin: 0 }}>负责人管理</Title>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => openModal()}
            >
              添加负责人
            </Button>
          </Col>
        </Row>

        {/* 搜索和筛选 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={8}>
            <Input
              placeholder="搜索姓名、邮箱、部门"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Select
              placeholder="筛选状态"
              value={statusFilter}
              onChange={setStatusFilter}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value={1}>启用</Option>
              <Option value={0}>禁用</Option>
            </Select>
          </Col>
        </Row>

        {/* 负责人表格 */}
        <Table
          columns={columns}
          dataSource={responsibles}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 创建/编辑模态框 */}
      <Modal
        title={editingResponsible ? '编辑负责人' : '添加负责人'}
        open={modalVisible}
        onCancel={closeModal}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="姓名"
                name="name"
                rules={[
                  { required: true, message: '请输入姓名' },
                  { max: 100, message: '姓名不能超过100个字符' }
                ]}
              >
                <Input placeholder="请输入姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="部门"
                name="department"
                rules={[{ max: 100, message: '部门不能超过100个字符' }]}
              >
                <Input placeholder="请输入部门" />
              </Form.Item>
            </Col>

          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="排序"
                name="sort_order"
                rules={[{ type: 'number', min: 0, message: '排序必须为非负数' }]}
              >
                <InputNumber
                  placeholder="排序（数字越小越靠前）"
                  style={{ width: '100%' }}
                  min={0}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="状态"
                name="status"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value={1}>启用</Option>
                  <Option value={0}>禁用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="邮箱"
            name="email"
            rules={[
              { type: 'email', message: '请输入正确的邮箱格式' },
              { max: 100, message: '邮箱不能超过100个字符' }
            ]}
          >
            <Input placeholder="请输入邮箱" />
          </Form.Item>

          <Form.Item
            label="电话"
            name="phone"
            rules={[{ max: 20, message: '电话不能超过20个字符' }]}
          >
            <Input placeholder="请输入电话" />
          </Form.Item>

          <Form.Item
            label="状态"
            name="status"
            valuePropName="checked"
            getValueFromEvent={(checked) => checked ? 1 : 0}
            getValueProps={(value) => ({ checked: value === 1 })}
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={closeModal}>取消</Button>
              <Button type="primary" htmlType="submit">
                {editingResponsible ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ResponsibleManagement;
