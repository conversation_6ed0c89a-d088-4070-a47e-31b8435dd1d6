const axios = require('axios');

const BASE_URL = 'http://127.0.0.1:3001';
const FRONTEND_URL = 'http://localhost:3002';

/**
 * 测试负责人管理功能
 */
async function testResponsiblesFeature() {
    console.log('🧪 开始测试负责人管理功能...\n');

    try {
        // 1. 测试健康检查
        console.log('1️⃣ 测试服务器健康状态...');
        const healthResponse = await axios.get(`${BASE_URL}/health`);
        console.log('✅ 服务器健康状态:', healthResponse.data);
        console.log('');

        // 2. 测试登录功能
        console.log('2️⃣ 测试管理员登录...');
        const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
            username: 'admin',
            password: 'admin123'
        });
        console.log('✅ 管理员登录成功');
        console.log('   用户信息:', loginResponse.data.data.user);
        const adminToken = loginResponse.data.token;
        console.log('');

        // 3. 测试获取启用的负责人列表
        console.log('3️⃣ 测试获取启用的负责人列表...');
        const activeResponsiblesResponse = await axios.get(`${BASE_URL}/api/responsibles/active`, {
            headers: { Authorization: `Bearer ${adminToken}` }
        });
        console.log('✅ 获取启用负责人列表成功');
        console.log('   负责人数量:', activeResponsiblesResponse.data.responsibles.length);
        activeResponsiblesResponse.data.responsibles.forEach((responsible, index) => {
            console.log(`   ${index + 1}. ${responsible.name} (${responsible.position})`);
        });
        console.log('');

        // 4. 测试获取完整负责人列表
        console.log('4️⃣ 测试获取完整负责人列表...');
        const allResponsiblesResponse = await axios.get(`${BASE_URL}/api/responsibles`, {
            headers: { Authorization: `Bearer ${adminToken}` }
        });
        console.log('✅ 获取完整负责人列表成功');
        console.log('   总数量:', allResponsiblesResponse.data.pagination.total);
        console.log('');

        // 5. 测试普通用户登录
        console.log('5️⃣ 测试普通用户登录...');
        const userLoginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
            username: 'lawyer1',
            password: 'lawyer123'
        });
        console.log('✅ 普通用户登录成功');
        console.log('   用户信息:', userLoginResponse.data.data.user);
        const userToken = userLoginResponse.data.token;
        console.log('');

        // 6. 测试普通用户访问负责人列表
        console.log('6️⃣ 测试普通用户访问负责人列表...');
        const userResponsiblesResponse = await axios.get(`${BASE_URL}/api/responsibles/active`, {
            headers: { Authorization: `Bearer ${userToken}` }
        });
        console.log('✅ 普通用户可以访问负责人列表');
        console.log('   可见负责人数量:', userResponsiblesResponse.data.responsibles.length);
        console.log('');

        // 7. 测试案件创建API（模拟）
        console.log('7️⃣ 测试案件创建功能...');
        const caseData = {
            title: '测试案件 - 负责人功能验证',
            type: '合同纠纷',
            description: '这是一个测试案件，用于验证负责人选择功能',
            priority: '高',
            responsible_id: 1, // 胡聪
            client_name: '测试客户',
            client_contact: '13800138000'
        };

        const createCaseResponse = await axios.post(`${BASE_URL}/api/cases`, caseData, {
            headers: { Authorization: `Bearer ${userToken}` }
        });
        console.log('✅ 案件创建成功');
        console.log('   案件数据:', createCaseResponse.data.data);
        if (createCaseResponse.data.data.case) {
            console.log('   案件ID:', createCaseResponse.data.data.case.id);
            console.log('   负责人ID:', createCaseResponse.data.data.case.responsible_id);
        }
        console.log('');

        // 8. 功能总结
        console.log('🎉 负责人管理功能测试完成！');
        console.log('');
        console.log('📋 测试结果总结:');
        console.log('   ✅ 服务器健康检查 - 通过');
        console.log('   ✅ 管理员登录认证 - 通过');
        console.log('   ✅ 获取启用负责人列表 - 通过');
        console.log('   ✅ 获取完整负责人列表 - 通过');
        console.log('   ✅ 普通用户权限验证 - 通过');
        console.log('   ✅ 案件创建与负责人关联 - 通过');
        console.log('');
        console.log('🔧 已实现的功能:');
        console.log('   • 独立滚动区域布局优化');
        console.log('   • 负责人数据库表结构设计');
        console.log('   • 负责人管理后端API');
        console.log('   • 负责人管理前端功能');
        console.log('   • 权限控制和用户验证');
        console.log('   • 案件创建页面负责人选择');
        console.log('');
        console.log('👥 可用负责人列表:');
        activeResponsiblesResponse.data.responsibles.forEach((responsible, index) => {
            console.log(`   ${index + 1}. ${responsible.name} - ${responsible.position} (${responsible.department})`);
        });

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('   响应状态:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
    }
}

// 运行测试
if (require.main === module) {
    testResponsiblesFeature()
        .then(() => {
            console.log('\n✨ 测试完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 测试失败:', error);
            process.exit(1);
        });
}

module.exports = testResponsiblesFeature;
