const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Log = sequelize.define('Log', {
    id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
    },
    user_id: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: '操作用户ID',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    action: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '操作类型',
        validate: {
            notEmpty: true,
        }
    },
    module: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: '模块名称',
        validate: {
            isIn: [['用户管理', '案件管理', '文件管理', '通知管理', '系统设置', '认证登录']]
        }
    },
    detail: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '操作详情（JSON格式）',
    },
    ip_address: {
        type: DataTypes.STRING(45),
        allowNull: true,
        comment: 'IP地址',
    },
    user_agent: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '用户代理',
    },
    status: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: 'success',
        comment: '操作状态',
        validate: {
            isIn: [['success', 'failed', 'warning']]
        }
    },
    error_message: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '错误信息',
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
    },
}, {
    tableName: 'logs',
    timestamps: false,
    indexes: [
        {
            fields: ['user_id']
        },
        {
            fields: ['action']
        },
        {
            fields: ['module']
        },
        {
            fields: ['status']
        },
        {
            fields: ['created_at']
        }
    ]
});

module.exports = Log;
