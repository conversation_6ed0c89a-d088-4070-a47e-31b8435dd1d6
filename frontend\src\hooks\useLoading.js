import { useState, useCallback, useRef } from 'react';
import { message } from 'antd';

/**
 * 加载状态管理Hook
 * 支持多个并发加载状态、自动错误处理、超时控制等
 */
export const useLoading = (options = {}) => {
  const {
    timeout = 30000, // 默认30秒超时
    showErrorMessage = true,
    showSuccessMessage = false,
    defaultErrorMessage = '操作失败，请稍后重试',
    defaultSuccessMessage = '操作成功',
  } = options;

  const [loading, setLoading] = useState(false);
  const [loadingStates, setLoadingStates] = useState(new Map());
  const timeoutRefs = useRef(new Map());

  // 设置单个加载状态
  const setLoadingState = useCallback((key, isLoading) => {
    setLoadingStates(prev => {
      const newStates = new Map(prev);
      if (isLoading) {
        newStates.set(key, true);
      } else {
        newStates.delete(key);
      }
      return newStates;
    });
  }, []);

  // 获取单个加载状态
  const getLoadingState = useCallback((key) => {
    return loadingStates.get(key) || false;
  }, [loadingStates]);

  // 检查是否有任何加载状态
  const hasAnyLoading = useCallback(() => {
    return loadingStates.size > 0;
  }, [loadingStates]);

  // 清除超时定时器
  const clearTimeoutRef = useCallback((key) => {
    const timeoutId = timeoutRefs.current.get(key);
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutRefs.current.delete(key);
    }
  }, []);

  // 设置超时定时器
  const setTimeoutRef = useCallback((key, timeoutMs) => {
    clearTimeoutRef(key);
    const timeoutId = setTimeout(() => {
      setLoadingState(key, false);
      if (showErrorMessage) {
        message.error('操作超时，请稍后重试');
      }
    }, timeoutMs);
    timeoutRefs.current.set(key, timeoutId);
  }, [clearTimeoutRef, setLoadingState, showErrorMessage]);

  // 执行异步操作（单一加载状态）
  const executeAsync = useCallback(async (asyncFn, options = {}) => {
    const {
      onSuccess,
      onError,
      successMessage = defaultSuccessMessage,
      errorMessage = defaultErrorMessage,
      timeoutMs = timeout,
    } = options;

    setLoading(true);
    
    // 设置超时
    const timeoutId = setTimeout(() => {
      setLoading(false);
      if (showErrorMessage) {
        message.error('操作超时，请稍后重试');
      }
    }, timeoutMs);

    try {
      const result = await asyncFn();
      
      clearTimeout(timeoutId);
      setLoading(false);
      
      if (showSuccessMessage && successMessage) {
        message.success(successMessage);
      }
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (error) {
      clearTimeout(timeoutId);
      setLoading(false);
      
      if (showErrorMessage) {
        const errorMsg = error.response?.data?.message || error.message || errorMessage;
        message.error(errorMsg);
      }
      
      if (onError) {
        onError(error);
      }
      
      throw error;
    }
  }, [loading, timeout, showErrorMessage, showSuccessMessage, defaultErrorMessage, defaultSuccessMessage]);

  // 执行异步操作（多重加载状态）
  const executeAsyncWithKey = useCallback(async (key, asyncFn, options = {}) => {
    const {
      onSuccess,
      onError,
      successMessage = defaultSuccessMessage,
      errorMessage = defaultErrorMessage,
      timeoutMs = timeout,
    } = options;

    setLoadingState(key, true);
    setTimeoutRef(key, timeoutMs);

    try {
      const result = await asyncFn();
      
      clearTimeoutRef(key);
      setLoadingState(key, false);
      
      if (showSuccessMessage && successMessage) {
        message.success(successMessage);
      }
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (error) {
      clearTimeoutRef(key);
      setLoadingState(key, false);
      
      if (showErrorMessage) {
        const errorMsg = error.response?.data?.message || error.message || errorMessage;
        message.error(errorMsg);
      }
      
      if (onError) {
        onError(error);
      }
      
      throw error;
    }
  }, [setLoadingState, setTimeoutRef, clearTimeoutRef, timeout, showErrorMessage, showSuccessMessage, defaultErrorMessage, defaultSuccessMessage]);

  // 批量执行异步操作
  const executeBatch = useCallback(async (operations, options = {}) => {
    const {
      onSuccess,
      onError,
      successMessage = '所有操作完成',
      errorMessage = '部分操作失败',
      failFast = false, // 是否在第一个失败时停止
    } = options;

    const batchKey = `batch_${Date.now()}`;
    setLoadingState(batchKey, true);

    try {
      let results;
      
      if (failFast) {
        // 顺序执行，遇到错误立即停止
        results = [];
        for (const operation of operations) {
          const result = await operation();
          results.push(result);
        }
      } else {
        // 并行执行，收集所有结果
        results = await Promise.allSettled(operations.map(op => op()));
      }
      
      setLoadingState(batchKey, false);
      
      if (showSuccessMessage && successMessage) {
        message.success(successMessage);
      }
      
      if (onSuccess) {
        onSuccess(results);
      }
      
      return results;
    } catch (error) {
      setLoadingState(batchKey, false);
      
      if (showErrorMessage) {
        const errorMsg = error.response?.data?.message || error.message || errorMessage;
        message.error(errorMsg);
      }
      
      if (onError) {
        onError(error);
      }
      
      throw error;
    }
  }, [setLoadingState, showErrorMessage, showSuccessMessage]);

  // 重置所有加载状态
  const resetAll = useCallback(() => {
    setLoading(false);
    setLoadingStates(new Map());
    
    // 清除所有超时定时器
    timeoutRefs.current.forEach(timeoutId => clearTimeout(timeoutId));
    timeoutRefs.current.clear();
  }, []);

  return {
    // 单一加载状态
    loading,
    setLoading,
    executeAsync,
    
    // 多重加载状态
    loadingStates: Object.fromEntries(loadingStates),
    setLoadingState,
    getLoadingState,
    hasAnyLoading: hasAnyLoading(),
    executeAsyncWithKey,
    
    // 批量操作
    executeBatch,
    
    // 工具方法
    resetAll,
  };
};

/**
 * 简化版加载状态Hook
 * 只提供基本的加载状态管理
 */
export const useSimpleLoading = (initialState = false) => {
  const [loading, setLoading] = useState(initialState);

  const withLoading = useCallback(async (asyncFn) => {
    setLoading(true);
    try {
      const result = await asyncFn();
      return result;
    } finally {
      setLoading(false);
    }
  }, []);

  return [loading, setLoading, withLoading];
};

export default useLoading;
