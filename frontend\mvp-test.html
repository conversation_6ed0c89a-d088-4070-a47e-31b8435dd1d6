<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MVP前后端连接测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #0958d9;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #52c41a;
            color: white;
        }
        .status.error {
            background: #ff4d4f;
            color: white;
        }
        .status.testing {
            background: #faad14;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 MVP前后端连接测试</h1>
        
        <div class="test-section">
            <h3>📊 系统状态</h3>
            <p>前端页面: <span id="frontend-status" class="status success">正常</span></p>
            <p>后端服务: <span id="backend-status" class="status testing">检测中</span></p>
            <p>连接状态: <span id="connection-status" class="status testing">检测中</span></p>
        </div>

        <div class="test-section">
            <h3>🏥 后端健康检查</h3>
            <button onclick="testHealth()">测试健康检查</button>
            <button onclick="testRoot()">测试根路径</button>
            <button onclick="testEndpoint()">测试API端点</button>
            <div id="health-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔗 连接测试</h3>
            <button onclick="testConnection()">测试连接</button>
            <button onclick="testCORS()">测试CORS</button>
            <button onclick="runAllTests()">运行所有测试</button>
            <div id="connection-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📝 测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="test-log" class="result info"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = text;
        }

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
        }

        async function testHealth() {
            log('开始健康检查测试...');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                showResult('health-result', data);
                updateStatus('backend-status', 'success', '正常');
                log('✅ 健康检查成功');
            } catch (error) {
                showResult('health-result', `错误: ${error.message}`, true);
                updateStatus('backend-status', 'error', '异常');
                log(`❌ 健康检查失败: ${error.message}`);
            }
        }

        async function testRoot() {
            log('开始根路径测试...');
            try {
                const response = await fetch(`${API_BASE}/`);
                const data = await response.json();
                showResult('health-result', data);
                log('✅ 根路径测试成功');
            } catch (error) {
                showResult('health-result', `错误: ${error.message}`, true);
                log(`❌ 根路径测试失败: ${error.message}`);
            }
        }

        async function testEndpoint() {
            log('开始API端点测试...');
            try {
                const response = await fetch(`${API_BASE}/test`);
                const data = await response.json();
                showResult('health-result', data);
                log('✅ API端点测试成功');
            } catch (error) {
                showResult('health-result', `错误: ${error.message}`, true);
                log(`❌ API端点测试失败: ${error.message}`);
            }
        }

        async function testConnection() {
            log('开始连接测试...');
            updateStatus('connection-status', 'testing', '测试中');
            
            try {
                const startTime = Date.now();
                const response = await fetch(`${API_BASE}/health`);
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('connection-result', {
                        status: '连接成功',
                        responseTime: `${responseTime}ms`,
                        data: data
                    });
                    updateStatus('connection-status', 'success', '正常');
                    log(`✅ 连接测试成功，响应时间: ${responseTime}ms`);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showResult('connection-result', `连接失败: ${error.message}`, true);
                updateStatus('connection-status', 'error', '失败');
                log(`❌ 连接测试失败: ${error.message}`);
            }
        }

        async function testCORS() {
            log('开始CORS测试...');
            try {
                const response = await fetch(`${API_BASE}/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Test-Header': 'test'
                    }
                });
                const data = await response.json();
                showResult('connection-result', {
                    status: 'CORS配置正常',
                    headers: Object.fromEntries(response.headers.entries()),
                    data: data
                });
                log('✅ CORS测试成功');
            } catch (error) {
                showResult('connection-result', `CORS错误: ${error.message}`, true);
                log(`❌ CORS测试失败: ${error.message}`);
            }
        }

        async function runAllTests() {
            log('开始运行所有测试...');
            await testHealth();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testConnection();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testCORS();
            log('✅ 所有测试完成');
        }

        function clearLog() {
            document.getElementById('test-log').textContent = '';
        }

        // 页面加载时自动运行基础测试
        window.onload = function() {
            log('页面加载完成，开始自动测试...');
            setTimeout(testHealth, 1000);
        };
    </script>
</body>
</html>
