/* 登录页面样式 */
.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 1;
}

.login-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
}

.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 400px;
  padding: 20px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px 32px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header .ant-typography {
  margin-bottom: 8px;
}

.login-footer {
  text-align: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 表单样式优化 */
.login-card .ant-form-item {
  margin-bottom: 20px;
}

.login-card .ant-input-affix-wrapper,
.login-card .ant-input {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

.login-card .ant-input-affix-wrapper:hover,
.login-card .ant-input:hover {
  border-color: #40a9ff;
}

.login-card .ant-input-affix-wrapper:focus,
.login-card .ant-input-affix-wrapper-focused,
.login-card .ant-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.login-card .ant-btn-primary {
  border-radius: 8px;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s;
}

.login-card .ant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.login-card .ant-btn-primary:active {
  transform: translateY(0);
}

/* 演示按钮样式 */
.login-card .ant-btn:not(.ant-btn-primary) {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  color: #666;
  transition: all 0.3s;
}

.login-card .ant-btn:not(.ant-btn-primary):hover {
  border-color: #1890ff;
  color: #1890ff;
}

/* 分割线样式 */
.login-card .ant-divider {
  margin: 24px 0 16px 0;
  color: #999;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-content {
    padding: 16px;
  }
  
  .login-card {
    padding: 32px 24px;
  }
  
  .login-header .ant-typography h2 {
    font-size: 20px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-card {
  animation: fadeInUp 0.6s ease-out;
}
