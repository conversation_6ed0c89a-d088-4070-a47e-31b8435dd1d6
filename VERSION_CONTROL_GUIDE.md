# Sie_Dispute_Manager 版本控制指南

## 📋 概述

本文档提供了Sie_Dispute_Manager项目的版本控制使用指南，包括Git工作流程、分支策略、提交规范和回退机制。

## 🌳 分支策略

### 主要分支

- **`master`** - 生产分支
  - 包含稳定的、可发布的代码
  - 所有发布版本都从此分支创建
  - 受保护，只能通过合并请求更新

- **`develop`** - 开发分支
  - 包含最新的开发功能
  - 功能开发完成后合并到此分支
  - 定期合并到master分支进行发布

### 功能分支

- **`feature/功能名称`** - 功能开发分支
  - 从develop分支创建
  - 开发完成后合并回develop分支
  - 示例：`feature/user-management`, `feature/case-workflow`

- **`hotfix/修复名称`** - 紧急修复分支
  - 从master分支创建
  - 修复完成后同时合并到master和develop分支
  - 示例：`hotfix/security-patch`, `hotfix/critical-bug`

## 📝 提交规范

### 提交消息格式

```
<类型>: <简短描述>

<详细描述>（可选）

<相关问题>（可选）
```

### 提交类型

- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式调整（不影响功能）
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

### 示例

```bash
feat: 添加案件状态流转功能

- 实现案件状态自动流转
- 添加状态变更历史记录
- 支持自定义流转规则

Closes #123
```

## 🏷️ 版本标签系统

### 版本号规范

采用语义化版本控制（Semantic Versioning）：`主版本.次版本.修订版本`

- **主版本**：不兼容的API修改
- **次版本**：向下兼容的功能性新增
- **修订版本**：向下兼容的问题修正

### 当前版本

- **v1.0.0** - 初始版本（当前）

### 创建版本标签

```bash
# 创建带注释的标签
git tag -a v1.1.0 -m "版本 1.1.0: 新增功能描述"

# 推送标签到远程仓库
git push origin v1.1.0
```

## 🔄 常用Git命令

### 基础操作

```bash
# 查看状态
git status

# 添加文件
git add .
git add <文件名>

# 提交更改
git commit -m "提交消息"

# 推送到远程仓库
git push origin <分支名>

# 拉取最新更改
git pull origin <分支名>
```

### 分支操作

```bash
# 查看所有分支
git branch -a

# 创建新分支
git branch <分支名>

# 切换分支
git checkout <分支名>

# 创建并切换到新分支
git checkout -b <分支名>

# 合并分支
git merge <分支名>

# 删除分支
git branch -d <分支名>
```

### 查看历史

```bash
# 查看提交历史
git log

# 查看简洁历史
git log --oneline

# 查看图形化历史
git log --graph --oneline

# 查看特定文件的历史
git log <文件名>
```

## ⏪ 版本回退机制

### 1. 查看提交历史

```bash
# 查看详细提交历史
git log

# 查看简洁历史（推荐）
git log --oneline

# 查看最近5次提交
git log --oneline -5
```

### 2. 回退到特定版本

#### 软回退（保留工作区更改）

```bash
# 回退到上一个版本
git reset --soft HEAD~1

# 回退到指定提交
git reset --soft <提交哈希>
```

#### 硬回退（丢弃所有更改）

```bash
# 回退到上一个版本
git reset --hard HEAD~1

# 回退到指定提交
git reset --hard <提交哈希>

# 回退到特定标签
git reset --hard v1.0.0
```

### 3. 紧急回滚操作

#### 场景1：撤销最后一次提交

```bash
# 撤销提交但保留更改
git reset --soft HEAD~1

# 撤销提交并丢弃更改
git reset --hard HEAD~1
```

#### 场景2：回退到稳定版本

```bash
# 回退到最近的稳定标签
git reset --hard v1.0.0

# 强制推送（谨慎使用）
git push --force-with-lease origin master
```

#### 场景3：创建回退分支

```bash
# 创建回退分支保存当前状态
git checkout -b backup-$(date +%Y%m%d-%H%M%S)
git push origin backup-$(date +%Y%m%d-%H%M%S)

# 切换回主分支并回退
git checkout master
git reset --hard v1.0.0
```

## 🚨 紧急情况处理

### 数据库问题回退

1. 停止应用服务
2. 回退到稳定版本：`git reset --hard v1.0.0`
3. 恢复数据库备份
4. 重启服务并验证

### 配置文件问题

1. 检查.env文件是否正确
2. 对比.env.example模板
3. 重新配置环境变量
4. 重启服务

### 依赖问题

```bash
# 清理并重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 后端依赖
cd backend
rm -rf node_modules package-lock.json
npm install

# 前端依赖
cd frontend
rm -rf node_modules package-lock.json
npm install
```

## 📚 最佳实践

1. **频繁提交**：小步快跑，经常提交代码
2. **清晰消息**：写清楚的提交消息
3. **分支隔离**：不同功能使用不同分支开发
4. **定期合并**：及时合并develop分支的更新
5. **标签管理**：重要版本及时打标签
6. **备份重要**：重要操作前先创建备份分支

## 🔧 故障排除

### 常见问题

1. **合并冲突**
   ```bash
   # 手动解决冲突后
   git add .
   git commit -m "resolve merge conflict"
   ```

2. **误删文件**
   ```bash
   # 恢复删除的文件
   git checkout HEAD -- <文件名>
   ```

3. **撤销未提交的更改**
   ```bash
   # 撤销工作区更改
   git checkout -- <文件名>
   
   # 撤销所有未提交更改
   git reset --hard HEAD
   ```

## 📞 技术支持

如遇到版本控制相关问题，请：

1. 查看本文档的故障排除部分
2. 检查Git状态：`git status`
3. 查看提交历史：`git log --oneline`
4. 创建备份分支后再进行操作

---

**注意**：在执行任何回退操作前，请务必创建备份分支以防数据丢失！
