/**
 * MVP集成服务器 - 包含用户认证功能的完整后端服务
 * 目标：验证前端-后端-数据库的完整连接链路，集成用户认证模块
 */

const express = require('express');
const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const path = require('path');

console.log('🚀 启动MVP集成服务器...');
console.log('⏰ 启动时间:', new Date().toISOString());

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '.env') });

const app = express();
const PORT = 8001;

// JWT配置
const JWT_SECRET = process.env.JWT_SECRET || 'sie_SuperKey2025';
const JWT_EXPIRES_IN = '7d';

// 数据库配置
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASS || '',
    database: process.env.DB_NAME || 'case_manager',
    connectionLimit: 10,
    acquireTimeout: 60000,
    timeout: 60000
};

// 创建数据库连接池
let pool = null;

// JWT工具函数
const JWTUtils = {
    generateToken: (payload) => {
        return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
    },

    verifyToken: (token) => {
        return new Promise((resolve, reject) => {
            jwt.verify(token, JWT_SECRET, (err, decoded) => {
                if (err) reject(err);
                else resolve(decoded);
            });
        });
    },

    extractTokenFromHeader: (req) => {
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            return authHeader.substring(7);
        }
        return null;
    }
};

// 认证中间件
const authenticate = async (req, res, next) => {
    try {
        const token = JWTUtils.extractTokenFromHeader(req);

        if (!token) {
            return res.status(401).json({
                error: 'Access token is required',
                code: 'TOKEN_MISSING'
            });
        }

        const decoded = await JWTUtils.verifyToken(token);

        // 查询用户信息
        const connection = await pool.getConnection();
        const [users] = await connection.execute(
            'SELECT id, username, real_name, email, status FROM users WHERE id = ?',
            [decoded.id]
        );
        connection.release();

        if (users.length === 0) {
            return res.status(401).json({
                error: 'User not found',
                code: 'USER_NOT_FOUND'
            });
        }

        const user = users[0];
        if (user.status !== 1) {
            return res.status(401).json({
                error: 'User account is disabled',
                code: 'USER_DISABLED'
            });
        }

        req.user = user;
        req.token = token;
        next();
    } catch (error) {
        console.error('Authentication error:', error);
        return res.status(401).json({
            error: 'Invalid or expired token',
            code: 'TOKEN_INVALID'
        });
    }
};

async function initDatabase() {
    try {
        console.log('🔗 初始化数据库连接池...');
        pool = mysql.createPool(dbConfig);

        // 测试连接
        const connection = await pool.getConnection();
        await connection.execute('SELECT 1');
        connection.release();

        console.log('✅ 数据库连接池初始化成功');
        return true;
    } catch (error) {
        console.error('❌ 数据库连接失败:', error.message);
        return false;
    }
}

// 中间件配置
app.use(express.json({ limit: '1mb' }));

// CORS设置
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');

    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// 健康检查端点
app.get('/health', async (req, res) => {
    console.log('📍 健康检查请求:', new Date().toISOString());

    let dbStatus = 'unknown';
    try {
        if (pool) {
            const connection = await pool.getConnection();
            await connection.execute('SELECT 1');
            connection.release();
            dbStatus = 'connected';
        } else {
            dbStatus = 'not_initialized';
        }
    } catch (error) {
        dbStatus = 'error';
    }

    res.json({
        status: 'healthy',
        message: 'MVP集成服务正常运行',
        timestamp: new Date().toISOString(),
        port: PORT,
        uptime: process.uptime(),
        version: '1.0.0-mvp-integration',
        database: dbStatus
    });
});

// 数据库测试端点
app.get('/api/db-test', async (req, res) => {
    console.log('📍 数据库测试请求:', new Date().toISOString());

    try {
        if (!pool) {
            throw new Error('数据库连接池未初始化');
        }

        const connection = await pool.getConnection();

        // 执行测试查询
        const [rows] = await connection.execute('SELECT 1 as test_value, NOW() as test_time, ? as message', ['数据库连接正常']);

        connection.release();

        res.json({
            success: true,
            message: '数据库连接测试成功',
            data: rows[0],
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('❌ 数据库测试失败:', error);
        res.status(500).json({
            success: false,
            message: '数据库连接测试失败',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// 创建测试数据端点
app.post('/api/test-data', async (req, res) => {
    console.log('📍 创建测试数据请求:', new Date().toISOString());

    try {
        if (!pool) {
            throw new Error('数据库连接池未初始化');
        }

        const { message = '默认测试消息' } = req.body;
        const connection = await pool.getConnection();

        // 确保测试表存在
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS mvp_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                message VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 插入测试数据
        const [result] = await connection.execute(
            'INSERT INTO mvp_test (message) VALUES (?)',
            [message]
        );

        connection.release();

        res.json({
            success: true,
            message: '测试数据创建成功',
            data: {
                id: result.insertId,
                message: message,
                created_at: new Date().toISOString()
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('❌ 创建测试数据失败:', error);
        res.status(500).json({
            success: false,
            message: '创建测试数据失败',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// 获取测试数据端点
app.get('/api/test-data', async (req, res) => {
    console.log('📍 获取测试数据请求:', new Date().toISOString());

    try {
        if (!pool) {
            throw new Error('数据库连接池未初始化');
        }

        const connection = await pool.getConnection();

        // 查询测试数据
        const [rows] = await connection.execute(
            'SELECT * FROM mvp_test ORDER BY id DESC LIMIT 10'
        );

        connection.release();

        res.json({
            success: true,
            message: '测试数据获取成功',
            data: rows,
            count: rows.length,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('❌ 获取测试数据失败:', error);
        res.status(500).json({
            success: false,
            message: '获取测试数据失败',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// 用户登录接口
app.post('/api/auth/login', async (req, res) => {
    console.log('📍 用户登录请求:', new Date().toISOString());

    try {
        const { username, password } = req.body;

        // 验证输入
        if (!username || !password) {
            return res.status(400).json({
                success: false,
                error: 'Username and password are required',
                code: 'MISSING_CREDENTIALS'
            });
        }

        if (!pool) {
            throw new Error('数据库连接池未初始化');
        }

        const connection = await pool.getConnection();

        // 查找用户
        const [users] = await connection.execute(
            'SELECT id, username, password, real_name, email, status FROM users WHERE username = ?',
            [username]
        );

        if (users.length === 0) {
            connection.release();
            return res.status(401).json({
                success: false,
                error: 'Invalid username or password',
                code: 'INVALID_CREDENTIALS'
            });
        }

        const user = users[0];

        // 检查用户状态
        if (user.status !== 1) {
            connection.release();
            return res.status(401).json({
                success: false,
                error: 'User account is disabled',
                code: 'USER_DISABLED'
            });
        }

        // 验证密码
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            connection.release();
            return res.status(401).json({
                success: false,
                error: 'Invalid username or password',
                code: 'INVALID_CREDENTIALS'
            });
        }

        // 查询用户角色
        const [roles] = await connection.execute(`
            SELECT r.name, r.description
            FROM roles r
            JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = ?
        `, [user.id]);

        connection.release();

        // 生成JWT token
        const payload = {
            id: user.id,
            username: user.username,
            real_name: user.real_name,
            email: user.email,
            roles: roles.map(role => role.name),
            iat: Math.floor(Date.now() / 1000)
        };
        const token = JWTUtils.generateToken(payload);

        res.json({
            success: true,
            message: 'Login successful',
            token,
            user: {
                id: user.id,
                username: user.username,
                real_name: user.real_name,
                email: user.email,
                roles: roles.map(role => ({
                    name: role.name,
                    description: role.description
                }))
            }
        });

        console.log('✅ 用户登录成功:', username);

    } catch (error) {
        console.error('❌ 用户登录失败:', error);
        res.status(500).json({
            success: false,
            error: 'Login failed',
            message: error.message
        });
    }
});

// 用户信息接口（需要认证）
app.get('/api/auth/me', authenticate, async (req, res) => {
    console.log('📍 获取用户信息请求:', new Date().toISOString());

    try {
        if (!pool) {
            throw new Error('数据库连接池未初始化');
        }

        const connection = await pool.getConnection();

        // 查询用户角色
        const [roles] = await connection.execute(`
            SELECT r.name, r.description
            FROM roles r
            JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = ?
        `, [req.user.id]);

        connection.release();

        res.json({
            success: true,
            user: {
                id: req.user.id,
                username: req.user.username,
                real_name: req.user.real_name,
                email: req.user.email,
                roles: roles.map(role => ({
                    name: role.name,
                    description: role.description
                }))
            }
        });

    } catch (error) {
        console.error('❌ 获取用户信息失败:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get user info',
            message: error.message
        });
    }
});

// 案件编号生成函数
function generateCaseNo() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const timestamp = now.getTime().toString().slice(-6);

    return `CASE${year}${month}${day}${timestamp}`;
}

// 权限检查中间件
const requireLawyer = async (req, res, next) => {
    try {
        if (!req.user) {
            return res.status(401).json({
                error: 'Authentication required',
                code: 'AUTH_REQUIRED'
            });
        }

        const connection = await pool.getConnection();
        const [roles] = await connection.execute(`
            SELECT r.name
            FROM roles r
            JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = ?
        `, [req.user.id]);
        connection.release();

        const userRoles = roles.map(role => role.name);
        const hasPermission = userRoles.includes('admin') || userRoles.includes('lawyer');

        if (!hasPermission) {
            return res.status(403).json({
                error: 'Insufficient permissions',
                code: 'PERMISSION_DENIED',
                required_roles: ['admin', 'lawyer'],
                user_roles: userRoles
            });
        }

        next();
    } catch (error) {
        console.error('Authorization error:', error);
        return res.status(500).json({
            error: 'Authorization check failed',
            code: 'AUTH_CHECK_ERROR'
        });
    }
};

// 案件所有者或管理员权限检查
const requireCaseOwnerOrAdmin = async (req, res, next) => {
    try {
        if (!req.user) {
            return res.status(401).json({
                error: 'Authentication required',
                code: 'AUTH_REQUIRED'
            });
        }

        const connection = await pool.getConnection();

        // 查询用户角色
        const [roles] = await connection.execute(`
            SELECT r.name
            FROM roles r
            JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = ?
        `, [req.user.id]);

        const userRoles = roles.map(role => role.name);

        // 管理员可以访问所有案件
        if (userRoles.includes('admin')) {
            connection.release();
            return next();
        }

        // 检查是否为案件所有者
        const caseId = req.params.id || req.params.caseId;
        if (caseId) {
            const [cases] = await connection.execute(
                'SELECT owner_id FROM cases WHERE id = ?',
                [caseId]
            );

            connection.release();

            if (cases.length === 0) {
                return res.status(404).json({
                    error: 'Case not found',
                    code: 'CASE_NOT_FOUND'
                });
            }

            if (cases[0].owner_id === req.user.id) {
                return next();
            }
        } else {
            connection.release();
        }

        return res.status(403).json({
            error: 'Access denied: You can only access your own cases',
            code: 'CASE_ACCESS_DENIED'
        });

    } catch (error) {
        console.error('Authorization error:', error);
        return res.status(500).json({
            error: 'Authorization check failed',
            code: 'AUTH_CHECK_ERROR'
        });
    }
};

// 获取案件列表
app.get('/api/cases', authenticate, async (req, res) => {
    console.log('📍 获取案件列表请求:', new Date().toISOString());

    try {
        const {
            page = 1,
            limit = 10,
            status,
            type,
            priority,
            search
        } = req.query;

        const offset = (page - 1) * limit;
        let whereClause = '';
        let params = [];

        if (!pool) {
            throw new Error('数据库连接池未初始化');
        }

        const connection = await pool.getConnection();

        // 查询用户角色
        const [roles] = await connection.execute(`
            SELECT r.name
            FROM roles r
            JOIN user_roles ur ON r.id = ur.role_id
            WHERE ur.user_id = ?
        `, [req.user.id]);

        const userRoles = roles.map(role => role.name);

        // 构建WHERE条件
        const conditions = [];

        // 权限过滤：非管理员只能看到自己的案件
        if (!userRoles.includes('admin')) {
            conditions.push(`c.owner_id = ${req.user.id}`);
        }

        // 状态过滤
        if (status) {
            conditions.push(`c.status = '${status}'`);
        }

        // 类型过滤
        if (type) {
            conditions.push(`c.type = '${type}'`);
        }

        // 优先级过滤
        if (priority) {
            conditions.push(`c.priority = '${priority}'`);
        }

        // 搜索过滤
        if (search) {
            const searchTerm = search.replace(/'/g, "''"); // 防止SQL注入
            conditions.push(`(c.title LIKE '%${searchTerm}%' OR c.case_no LIKE '%${searchTerm}%' OR c.client_name LIKE '%${searchTerm}%')`);
        }

        if (conditions.length > 0) {
            whereClause = 'WHERE ' + conditions.join(' AND ');
        }

        // 查询案件列表
        const casesQuery = `
            SELECT
                c.*,
                u.real_name as owner_name,
                u.username as owner_username
            FROM cases c
            LEFT JOIN users u ON c.owner_id = u.id
            ${whereClause}
            ORDER BY c.created_at DESC
            LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
        `;

        const [cases] = await connection.query(casesQuery);

        // 查询总数
        const countQuery = `
            SELECT COUNT(*) as total
            FROM cases c
            ${whereClause}
        `;

        const [countResult] = await connection.query(countQuery);

        connection.release();

        res.json({
            success: true,
            data: cases,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: countResult[0].total,
                pages: Math.ceil(countResult[0].total / limit)
            }
        });

    } catch (error) {
        console.error('❌ 获取案件列表失败:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get cases',
            message: error.message
        });
    }
});

// 获取案件详情
app.get('/api/cases/:id', authenticate, requireCaseOwnerOrAdmin, async (req, res) => {
    console.log('📍 获取案件详情请求:', new Date().toISOString());

    try {
        if (!pool) {
            throw new Error('数据库连接池未初始化');
        }

        const connection = await pool.getConnection();

        // 查询案件详情
        const [cases] = await connection.execute(`
            SELECT
                c.*,
                u.real_name as owner_name,
                u.username as owner_username,
                u.email as owner_email
            FROM cases c
            LEFT JOIN users u ON c.owner_id = u.id
            WHERE c.id = ?
        `, [req.params.id]);

        if (cases.length === 0) {
            connection.release();
            return res.status(404).json({
                success: false,
                error: 'Case not found',
                code: 'CASE_NOT_FOUND'
            });
        }

        // 查询案件流转记录
        const [flows] = await connection.execute(`
            SELECT
                cf.*,
                u.real_name as operator_name,
                u.username as operator_username
            FROM case_flows cf
            LEFT JOIN users u ON cf.operator_id = u.id
            WHERE cf.case_id = ?
            ORDER BY cf.created_at DESC
        `, [req.params.id]);

        connection.release();

        const caseData = cases[0];
        caseData.flows = flows;

        res.json({
            success: true,
            data: caseData
        });

    } catch (error) {
        console.error('❌ 获取案件详情失败:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get case details',
            message: error.message
        });
    }
});

// 创建新案件
app.post('/api/cases', authenticate, requireLawyer, async (req, res) => {
    console.log('📍 创建案件请求:', new Date().toISOString());

    try {
        const {
            title,
            type,
            description,
            priority = '中',
            deadline,
            amount,
            client_name,
            client_contact,
            owner_id
        } = req.body;

        // 验证必填字段
        if (!title || !type) {
            return res.status(400).json({
                success: false,
                error: 'Title and type are required',
                code: 'MISSING_FIELDS'
            });
        }

        // 验证案件类型
        const validTypes = ['合同纠纷', '劳动争议', '知识产权', '公司法务', '其他'];
        if (!validTypes.includes(type)) {
            console.log('Invalid type received:', type, 'Valid types:', validTypes);
            return res.status(400).json({
                success: false,
                error: 'Invalid case type',
                code: 'INVALID_TYPE',
                received_type: type,
                valid_types: validTypes
            });
        }

        // 验证优先级
        const validPriorities = ['低', '中', '高', '紧急'];
        if (!validPriorities.includes(priority)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid priority',
                code: 'INVALID_PRIORITY'
            });
        }

        if (!pool) {
            throw new Error('数据库连接池未初始化');
        }

        const connection = await pool.getConnection();

        // 生成案件编号
        const case_no = generateCaseNo();

        // 确定负责人（如果未指定，默认为当前用户）
        const finalOwnerId = owner_id || req.user.id;

        // 验证负责人是否存在
        const [owners] = await connection.execute(
            'SELECT id, real_name FROM users WHERE id = ? AND status = 1',
            [finalOwnerId]
        );

        if (owners.length === 0) {
            connection.release();
            return res.status(400).json({
                success: false,
                error: 'Invalid owner_id',
                code: 'INVALID_OWNER'
            });
        }

        // 创建案件
        const [result] = await connection.execute(`
            INSERT INTO cases (
                title, case_no, type, description, owner_id, priority,
                deadline, amount, client_name, client_contact, status,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, '待处理', NOW(), NOW())
        `, [
            title,
            case_no,
            type,
            description,
            finalOwnerId,
            priority,
            deadline ? new Date(deadline) : null,
            amount ? parseFloat(amount) : null,
            client_name,
            client_contact
        ]);

        const caseId = result.insertId;

        // 记录案件流转
        await connection.execute(`
            INSERT INTO case_flows (
                case_id, action, operator_id, remark, new_value, created_at
            ) VALUES (?, '创建案件', ?, ?, ?, NOW())
        `, [
            caseId,
            req.user.id,
            `案件创建，负责人：${owners[0].real_name}`,
            JSON.stringify({
                title,
                type,
                priority,
                owner_id: finalOwnerId
            })
        ]);

        connection.release();

        res.status(201).json({
            success: true,
            message: 'Case created successfully',
            data: {
                id: caseId,
                case_no,
                title,
                type,
                status: '待处理',
                priority,
                owner_id: finalOwnerId,
                owner_name: owners[0].real_name
            }
        });

        console.log('✅ 案件创建成功:', case_no);

    } catch (error) {
        console.error('❌ 创建案件失败:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to create case',
            message: error.message
        });
    }
});

// 更新案件
app.put('/api/cases/:id', authenticate, requireCaseOwnerOrAdmin, async (req, res) => {
    console.log('📍 更新案件请求:', new Date().toISOString());

    try {
        const {
            title,
            type,
            description,
            priority,
            deadline,
            amount,
            client_name,
            client_contact,
            status
        } = req.body;

        if (!pool) {
            throw new Error('数据库连接池未初始化');
        }

        const connection = await pool.getConnection();

        // 查询原案件信息
        const [cases] = await connection.execute(
            'SELECT * FROM cases WHERE id = ?',
            [req.params.id]
        );

        if (cases.length === 0) {
            connection.release();
            return res.status(404).json({
                success: false,
                error: 'Case not found',
                code: 'CASE_NOT_FOUND'
            });
        }

        const oldCase = cases[0];
        const updates = [];
        const params = [];
        const changes = {};

        // 构建更新字段
        if (title !== undefined && title !== oldCase.title) {
            updates.push('title = ?');
            params.push(title);
            changes.title = { old: oldCase.title, new: title };
        }

        if (type !== undefined && type !== oldCase.type) {
            const validTypes = ['合同纠纷', '劳动争议', '知识产权', '公司法务', '其他'];
            if (!validTypes.includes(type)) {
                connection.release();
                return res.status(400).json({
                    success: false,
                    error: 'Invalid case type',
                    code: 'INVALID_TYPE'
                });
            }
            updates.push('type = ?');
            params.push(type);
            changes.type = { old: oldCase.type, new: type };
        }

        if (description !== undefined && description !== oldCase.description) {
            updates.push('description = ?');
            params.push(description);
            changes.description = { old: oldCase.description, new: description };
        }

        if (priority !== undefined && priority !== oldCase.priority) {
            const validPriorities = ['低', '中', '高', '紧急'];
            if (!validPriorities.includes(priority)) {
                connection.release();
                return res.status(400).json({
                    success: false,
                    error: 'Invalid priority',
                    code: 'INVALID_PRIORITY'
                });
            }
            updates.push('priority = ?');
            params.push(priority);
            changes.priority = { old: oldCase.priority, new: priority };
        }

        if (status !== undefined && status !== oldCase.status) {
            const validStatuses = ['待处理', '处理中', '已结案', '已归档', '已撤销'];
            if (!validStatuses.includes(status)) {
                connection.release();
                return res.status(400).json({
                    success: false,
                    error: 'Invalid status',
                    code: 'INVALID_STATUS'
                });
            }
            updates.push('status = ?');
            params.push(status);
            changes.status = { old: oldCase.status, new: status };
        }

        if (deadline !== undefined) {
            const newDeadline = deadline ? new Date(deadline) : null;
            if (newDeadline?.getTime() !== oldCase.deadline?.getTime()) {
                updates.push('deadline = ?');
                params.push(newDeadline);
                changes.deadline = { old: oldCase.deadline, new: newDeadline };
            }
        }

        if (amount !== undefined) {
            const newAmount = amount ? parseFloat(amount) : null;
            if (newAmount !== oldCase.amount) {
                updates.push('amount = ?');
                params.push(newAmount);
                changes.amount = { old: oldCase.amount, new: newAmount };
            }
        }

        if (client_name !== undefined && client_name !== oldCase.client_name) {
            updates.push('client_name = ?');
            params.push(client_name);
            changes.client_name = { old: oldCase.client_name, new: client_name };
        }

        if (client_contact !== undefined && client_contact !== oldCase.client_contact) {
            updates.push('client_contact = ?');
            params.push(client_contact);
            changes.client_contact = { old: oldCase.client_contact, new: client_contact };
        }

        if (updates.length === 0) {
            connection.release();
            return res.json({
                success: true,
                message: 'No changes detected',
                data: oldCase
            });
        }

        // 添加更新时间
        updates.push('updated_at = NOW()');
        params.push(req.params.id);

        // 执行更新
        await connection.execute(`
            UPDATE cases SET ${updates.join(', ')} WHERE id = ?
        `, params);

        // 记录案件流转
        await connection.execute(`
            INSERT INTO case_flows (
                case_id, action, operator_id, remark, old_value, new_value, created_at
            ) VALUES (?, '案件更新', ?, ?, ?, ?, NOW())
        `, [
            req.params.id,
            req.user.id,
            '案件信息更新',
            JSON.stringify(Object.fromEntries(Object.entries(changes).map(([k, v]) => [k, v.old]))),
            JSON.stringify(Object.fromEntries(Object.entries(changes).map(([k, v]) => [k, v.new])))
        ]);

        // 查询更新后的案件信息
        const [updatedCases] = await connection.execute(`
            SELECT
                c.*,
                u.real_name as owner_name,
                u.username as owner_username
            FROM cases c
            LEFT JOIN users u ON c.owner_id = u.id
            WHERE c.id = ?
        `, [req.params.id]);

        connection.release();

        res.json({
            success: true,
            message: 'Case updated successfully',
            data: updatedCases[0],
            changes: changes
        });

        console.log('✅ 案件更新成功:', oldCase.case_no);

    } catch (error) {
        console.error('❌ 更新案件失败:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update case',
            message: error.message
        });
    }
});

// 删除案件
app.delete('/api/cases/:id', authenticate, requireCaseOwnerOrAdmin, async (req, res) => {
    console.log('📍 删除案件请求:', new Date().toISOString());

    try {
        if (!pool) {
            throw new Error('数据库连接池未初始化');
        }

        const connection = await pool.getConnection();

        // 查询案件信息
        const [cases] = await connection.execute(
            'SELECT * FROM cases WHERE id = ?',
            [req.params.id]
        );

        if (cases.length === 0) {
            connection.release();
            return res.status(404).json({
                success: false,
                error: 'Case not found',
                code: 'CASE_NOT_FOUND'
            });
        }

        const caseData = cases[0];

        // 检查案件状态，已结案或已归档的案件不能删除
        if (['已结案', '已归档'].includes(caseData.status)) {
            connection.release();
            return res.status(400).json({
                success: false,
                error: 'Cannot delete completed or archived cases',
                code: 'CASE_CANNOT_DELETE'
            });
        }

        // 删除相关的流转记录
        await connection.execute(
            'DELETE FROM case_flows WHERE case_id = ?',
            [req.params.id]
        );

        // 删除案件
        await connection.execute(
            'DELETE FROM cases WHERE id = ?',
            [req.params.id]
        );

        connection.release();

        res.json({
            success: true,
            message: 'Case deleted successfully',
            data: {
                id: caseData.id,
                case_no: caseData.case_no,
                title: caseData.title
            }
        });

        console.log('✅ 案件删除成功:', caseData.case_no);

    } catch (error) {
        console.error('❌ 删除案件失败:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to delete case',
            message: error.message
        });
    }
});

// 案件状态变更
app.patch('/api/cases/:id/status', authenticate, requireCaseOwnerOrAdmin, async (req, res) => {
    console.log('📍 案件状态变更请求:', new Date().toISOString());

    try {
        const { status, remark } = req.body;

        if (!status) {
            return res.status(400).json({
                success: false,
                error: 'Status is required',
                code: 'MISSING_STATUS'
            });
        }

        const validStatuses = ['待处理', '处理中', '已结案', '已归档', '已撤销'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid status',
                code: 'INVALID_STATUS'
            });
        }

        if (!pool) {
            throw new Error('数据库连接池未初始化');
        }

        const connection = await pool.getConnection();

        // 查询原案件信息
        const [cases] = await connection.execute(
            'SELECT * FROM cases WHERE id = ?',
            [req.params.id]
        );

        if (cases.length === 0) {
            connection.release();
            return res.status(404).json({
                success: false,
                error: 'Case not found',
                code: 'CASE_NOT_FOUND'
            });
        }

        const oldCase = cases[0];

        if (oldCase.status === status) {
            connection.release();
            return res.json({
                success: true,
                message: 'Status unchanged',
                data: oldCase
            });
        }

        // 更新案件状态
        await connection.execute(`
            UPDATE cases SET status = ?, updated_at = NOW() WHERE id = ?
        `, [status, req.params.id]);

        // 记录状态变更流转
        await connection.execute(`
            INSERT INTO case_flows (
                case_id, action, operator_id, remark, old_value, new_value, created_at
            ) VALUES (?, '状态变更', ?, ?, ?, ?, NOW())
        `, [
            req.params.id,
            req.user.id,
            remark || `状态从"${oldCase.status}"变更为"${status}"`,
            JSON.stringify({ status: oldCase.status }),
            JSON.stringify({ status: status })
        ]);

        // 查询更新后的案件信息
        const [updatedCases] = await connection.execute(`
            SELECT
                c.*,
                u.real_name as owner_name,
                u.username as owner_username
            FROM cases c
            LEFT JOIN users u ON c.owner_id = u.id
            WHERE c.id = ?
        `, [req.params.id]);

        connection.release();

        res.json({
            success: true,
            message: 'Case status updated successfully',
            data: updatedCases[0],
            changes: {
                status: { old: oldCase.status, new: status }
            }
        });

        console.log('✅ 案件状态变更成功:', oldCase.case_no, `${oldCase.status} -> ${status}`);

    } catch (error) {
        console.error('❌ 案件状态变更失败:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update case status',
            message: error.message
        });
    }
});

// 负责人管理API
// 获取启用的负责人列表（用于下拉选择）
app.get('/api/responsibles/active', authenticate, async (req, res) => {
    console.log('📍 获取启用负责人列表请求:', new Date().toISOString());

    try {
        if (!pool) {
            throw new Error('数据库连接池未初始化');
        }

        const connection = await pool.getConnection();

        // 查询启用的负责人，按排序顺序和姓名排序
        const [responsibles] = await connection.execute(`
            SELECT id, name, email, department
            FROM responsibles
            WHERE status = 1
            ORDER BY sort_order ASC, name ASC
        `);

        connection.release();

        res.json({
            success: true,
            data: {
                responsibles: responsibles
            }
        });

    } catch (error) {
        console.error('❌ 获取启用负责人列表失败:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get active responsibles',
            message: error.message
        });
    }
});

// 获取完整负责人列表（用于管理页面）
app.get('/api/responsibles', authenticate, async (req, res) => {
    console.log('📍 获取负责人列表请求:', new Date().toISOString());

    try {
        if (!pool) {
            throw new Error('数据库连接池未初始化');
        }

        const { search, status, page = 1, limit = 50 } = req.query;
        const offset = (page - 1) * limit;

        const connection = await pool.getConnection();

        // 构建查询条件
        let whereClause = '';
        let queryParams = [];

        const conditions = [];
        if (search) {
            conditions.push('(name LIKE ? OR email LIKE ? OR department LIKE ?)');
            queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
        }
        if (status !== undefined && status !== '') {
            conditions.push('status = ?');
            queryParams.push(parseInt(status));
        }

        if (conditions.length > 0) {
            whereClause = 'WHERE ' + conditions.join(' AND ');
        }

        // 查询负责人列表
        let listQuery = `
            SELECT id, name, email, phone, department, status, sort_order, created_at, updated_at
            FROM responsibles
            ${whereClause}
            ORDER BY sort_order ASC, name ASC
            LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}
        `;

        const [responsibles] = await connection.execute(listQuery, queryParams);

        // 查询总数
        let countQuery = `
            SELECT COUNT(*) as total
            FROM responsibles
            ${whereClause}
        `;

        const [countResult] = await connection.execute(countQuery, queryParams);

        connection.release();

        res.json({
            success: true,
            data: {
                responsibles: responsibles,
                pagination: {
                    total: countResult[0].total,
                    page: parseInt(page),
                    limit: parseInt(limit),
                    pages: Math.ceil(countResult[0].total / limit)
                }
            }
        });

    } catch (error) {
        console.error('❌ 获取负责人列表失败:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get responsibles',
            message: error.message
        });
    }
});

// 根路径
app.get('/', (req, res) => {
    console.log('📍 根路径访问:', new Date().toISOString());
    res.json({
        message: '法务案件管理平台 MVP集成API',
        version: '1.0.0-mvp-integration',
        status: 'running',
        features: ['用户认证', '案件管理', '权限控制', '状态流转', '负责人管理'],
        endpoints: {
            // 认证相关
            login: 'POST /api/auth/login',
            userInfo: 'GET /api/auth/me',

            // 案件管理
            getCases: 'GET /api/cases',
            createCase: 'POST /api/cases',
            getCaseDetail: 'GET /api/cases/:id',
            updateCase: 'PUT /api/cases/:id',
            deleteCase: 'DELETE /api/cases/:id',
            updateCaseStatus: 'PATCH /api/cases/:id/status',

            // 负责人管理
            getActiveResponsibles: 'GET /api/responsibles/active',
            getResponsibles: 'GET /api/responsibles',

            // 系统功能
            health: 'GET /health',
            dbTest: 'GET /api/db-test',
            testData: 'GET /api/test-data'
        }
    });
});

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error('❌ 服务器错误:', err);
    res.status(500).json({
        error: '服务器内部错误',
        message: err.message,
        timestamp: new Date().toISOString()
    });
});

// 404处理
app.use('*', (req, res) => {
    console.log('❌ 404错误:', req.originalUrl);
    res.status(404).json({
        error: '接口不存在',
        path: req.originalUrl,
        timestamp: new Date().toISOString()
    });
});

// 启动服务器
async function startServer() {
    try {
        // 初始化数据库
        const dbInitialized = await initDatabase();
        if (!dbInitialized) {
            console.log('⚠️ 数据库连接失败，但服务器仍将启动（仅基础功能可用）');
        }

        // 启动HTTP服务器
        const server = app.listen(PORT, '127.0.0.1', () => {
            console.log('✅ MVP集成服务器启动成功!');
            console.log(`📍 服务地址: http://127.0.0.1:${PORT}`);
            console.log(`🏥 健康检查: http://127.0.0.1:${PORT}/health`);
            console.log(`🗄️ 数据库测试: http://127.0.0.1:${PORT}/api/db-test`);
            console.log(`📊 测试数据: http://127.0.0.1:${PORT}/api/test-data`);
            console.log('⏰ 启动完成时间:', new Date().toISOString());
        });

        // 优雅关闭处理
        process.on('SIGTERM', () => {
            console.log('📴 收到SIGTERM信号，正在关闭服务器...');
            server.close(async () => {
                if (pool) {
                    await pool.end();
                    console.log('🔌 数据库连接池已关闭');
                }
                console.log('✅ 服务器已关闭');
                process.exit(0);
            });
        });

        process.on('SIGINT', () => {
            console.log('📴 收到SIGINT信号，正在关闭服务器...');
            server.close(async () => {
                if (pool) {
                    await pool.end();
                    console.log('🔌 数据库连接池已关闭');
                }
                console.log('✅ 服务器已关闭');
                process.exit(0);
            });
        });

    } catch (error) {
        console.error('❌ 服务器启动失败:', error);
        process.exit(1);
    }
}

// 未捕获异常处理
process.on('uncaughtException', (err) => {
    console.error('❌ 未捕获异常:', err);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
    process.exit(1);
});

// 启动服务器
startServer();
