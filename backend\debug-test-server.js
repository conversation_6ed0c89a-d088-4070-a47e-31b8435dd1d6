const express = require('express');
const cors = require('cors');

console.log('🔍 Starting debug test server...');
console.log('📊 Process ID:', process.pid);
console.log('📊 Node version:', process.version);
console.log('📊 Platform:', process.platform);
console.log('📊 Memory usage:', process.memoryUsage());

const app = express();
const PORT = 9000; // 使用一个全新的端口

console.log('✅ Using PORT:', PORT);

// 进程事件监听
process.on('exit', (code) => {
    console.log('🔒 Process exiting with code:', code);
});

process.on('SIGINT', () => {
    console.log('🔒 Received SIGINT');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('🔒 Received SIGTERM');
    process.exit(0);
});

process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection:', reason);
    process.exit(1);
});

try {
    console.log('🔍 Setting up middleware...');
    
    // 基础中间件
    app.use(cors({
        origin: ['http://localhost:5173', 'http://127.0.0.1:5173'],
        credentials: true
    }));
    
    app.use(express.json());
    console.log('✅ Middleware configured');

    // 请求日志
    app.use((req, res, next) => {
        console.log(`📍 ${req.method} ${req.path} - ${new Date().toISOString()}`);
        next();
    });

    // 根路由
    app.get('/', (req, res) => {
        console.log('📍 Root route accessed');
        res.json({
            message: 'Debug test server is running',
            port: PORT,
            timestamp: new Date().toISOString(),
            pid: process.pid
        });
    });

    // 健康检查
    app.get('/health', (req, res) => {
        console.log('📍 Health check accessed');
        res.json({
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            pid: process.pid
        });
    });

    // API测试路由
    app.get('/api/test', (req, res) => {
        console.log('📍 API test accessed');
        res.json({
            message: 'API is working',
            timestamp: new Date().toISOString(),
            endpoint: '/api/test'
        });
    });

    console.log('🔍 Starting server...');
    
    const server = app.listen(PORT, '127.0.0.1', () => {
        console.log(`✅ Debug test server is running on port ${PORT}`);
        console.log(`📍 API URL: http://127.0.0.1:${PORT}`);
        console.log(`🏥 Health check: http://127.0.0.1:${PORT}/health`);
        console.log(`📊 Server address:`, server.address());
        
        // 定期输出状态
        const statusInterval = setInterval(() => {
            console.log(`📊 Server alive - PID: ${process.pid}, Uptime: ${Math.floor(process.uptime())}s`);
        }, 5000);
        
        // 清理定时器
        process.on('exit', () => {
            clearInterval(statusInterval);
        });
    });

    server.on('error', (error) => {
        console.error('❌ Server error event:', error);
        if (error.code === 'EADDRINUSE') {
            console.error(`❌ Port ${PORT} is already in use`);
        }
        process.exit(1);
    });

    server.on('listening', () => {
        console.log('✅ Server is listening on', server.address());
    });

    server.on('close', () => {
        console.log('🔒 Server closed');
    });

} catch (error) {
    console.error('❌ Setup error:', error);
    process.exit(1);
}

console.log('✅ Debug test server setup complete');
