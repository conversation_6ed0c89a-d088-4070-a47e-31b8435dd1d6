import React, { useState, useEffect } from 'react';
import {
  Typography,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Select,
  message,
  Card,
  Row,
  Col,
  Tag,
  Input,
  Tooltip
} from 'antd';
import {
  EditOutlined,
  SearchOutlined,
  UserOutlined,
  SafetyOutlined
} from '@ant-design/icons';

import { usersAPI } from '../../services/users';
import { rolesAPI } from '../../services/roles';
import { isAdmin } from '../../utils/auth';

const { Title } = Typography;
const { Option } = Select;
const { Search } = Input;

const UserPermissionManagement = () => {
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [form] = Form.useForm();

  const hasAdminPermission = isAdmin();

  // 获取用户列表
  const fetchUsers = async () => {
    setLoading(true);
    try {
      const params = {};
      if (searchText) params.search = searchText;
      if (statusFilter !== '') params.status = statusFilter;
      if (roleFilter) params.role = roleFilter;

      const response = await usersAPI.getUsers(params);

      // 尝试多种可能的数据结构
      let usersData = null;
      if (response.data && response.data.users) {
        usersData = response.data.users;
      } else if (response.users) {
        usersData = response.users;
      } else if (response.data && Array.isArray(response.data)) {
        usersData = response.data;
      } else if (Array.isArray(response)) {
        usersData = response;
      }

      if (usersData && Array.isArray(usersData)) {
        setUsers(usersData);
      } else {
        setUsers([]);
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败');
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      const response = await rolesAPI.getRoles();

      // 尝试多种可能的数据结构
      let rolesData = null;
      if (response.data && response.data.roles) {
        rolesData = response.data.roles;
      } else if (response.roles) {
        rolesData = response.roles;
      } else if (response.data && Array.isArray(response.data)) {
        rolesData = response.data;
      } else if (Array.isArray(response)) {
        rolesData = response;
      }

      if (rolesData && Array.isArray(rolesData)) {
        setRoles(rolesData);
      } else {
        message.warning('未找到角色数据，请检查系统配置');
      }
    } catch (error) {
      console.error('获取角色列表失败:', error);
      message.error('获取角色列表失败');
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [searchText, statusFilter, roleFilter]);

  useEffect(() => {
    fetchRoles();
  }, []);

  // 打开权限编辑模态框
  const openPermissionModal = (user) => {
    setEditingUser(user);
    setModalVisible(true);

    // 设置表单初始值
    const userRoleIds = user.roles ? user.roles.map(role => role.id) : [];
    form.setFieldsValue({
      role_ids: userRoleIds
    });
  };

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false);
    setEditingUser(null);
    form.resetFields();
  };

  // 保存权限设置
  const handleSavePermissions = async () => {
    try {
      const values = await form.validateFields();
      const { role_ids } = values;

      await usersAPI.updateUserRoles(editingUser.id, role_ids || []);

      message.success('用户权限更新成功');
      closeModal();
      fetchUsers(); // 重新获取用户列表
    } catch (error) {
      console.error('更新用户权限失败:', error);
      message.error('更新用户权限失败');
    }
  };

  // 获取角色标签颜色
  const getRoleTagColor = (roleName) => {
    const colorMap = {
      'admin': 'red',
      'lawyer': 'blue',
      'client': 'green',
      'assistant': 'orange'
    };
    return colorMap[roleName] || 'default';
  };

  // 表格列定义
  const columns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 120,
    },
    {
      title: '真实姓名',
      dataIndex: 'real_name',
      key: 'real_name',
      width: 120,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 200,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Tag color={status === 1 ? 'green' : 'red'}>
          {status === 1 ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '当前角色',
      dataIndex: 'roles',
      key: 'roles',
      width: 200,
      render: (roles) => (
        <Space wrap>
          {roles && roles.length > 0 ? (
            roles.map(role => (
              <Tag key={role.id} color={getRoleTagColor(role.name)}>
                {role.description || role.name}
              </Tag>
            ))
          ) : (
            <Tag color="default">无角色</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '注册时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date) => date ? new Date(date).toLocaleDateString() : '-',
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Tooltip title="分配权限">
            <Button
              type="link"
              size="small"
              icon={<SafetyOutlined />}
              onClick={() => openPermissionModal(record)}
              disabled={!hasAdminPermission}
            >
              权限
            </Button>
          </Tooltip>
        </Space>
      ),
    },
  ];

  if (!hasAdminPermission) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Title level={4}>权限不足</Title>
          <p>您没有权限访问用户权限管理功能</p>
        </div>
      </Card>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Title level={4} style={{ margin: 0 }}>
              <UserOutlined style={{ marginRight: 8 }} />
              用户权限管理
            </Title>
          </Col>
        </Row>

        {/* 搜索和筛选 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Search
              placeholder="搜索用户名、姓名或邮箱"
              allowClear
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onSearch={fetchUsers}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="状态筛选"
              allowClear
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: '100%' }}
            >
              <Option value={1}>启用</Option>
              <Option value={0}>禁用</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="角色筛选"
              allowClear
              value={roleFilter}
              onChange={setRoleFilter}
              style={{ width: '100%' }}
            >
              {roles.map(role => (
                <Option key={role.name} value={role.name}>
                  {role.description || role.name}
                </Option>
              ))}
            </Select>
          </Col>
        </Row>

        {/* 用户列表表格 */}
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            total: users.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ x: 1000 }}
        />

        {/* 权限分配模态框 */}
        <Modal
          title={`为用户 "${editingUser?.real_name}" 分配权限`}
          open={modalVisible}
          onOk={handleSavePermissions}
          onCancel={closeModal}
          width={500}
          okText="保存"
          cancelText="取消"
        >
          <Form
            form={form}
            layout="vertical"
            style={{ marginTop: 16 }}
          >
            <Form.Item
              label="用户信息"
            >
              <div style={{ padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
                <p style={{ margin: 0 }}><strong>用户名：</strong>{editingUser?.username}</p>
                <p style={{ margin: 0 }}><strong>真实姓名：</strong>{editingUser?.real_name}</p>
                <p style={{ margin: 0 }}><strong>邮箱：</strong>{editingUser?.email || '未设置'}</p>
              </div>
            </Form.Item>

            <Form.Item
              name="role_ids"
              label="分配角色"
              rules={[
                { required: true, message: '请至少选择一个角色' }
              ]}
            >
              <Select
                mode="multiple"
                placeholder={roles.length > 0 ? "请选择角色" : "正在加载角色..."}
                style={{ width: '100%' }}
                loading={roles.length === 0}
                notFoundContent={roles.length === 0 ? "正在加载角色..." : "没有可用角色"}
              >
                {roles.map(role => (
                  <Option key={role.id} value={role.id}>
                    <Tag color={getRoleTagColor(role.name)} style={{ marginRight: 8 }}>
                      {role.description || role.name}
                    </Tag>
                    {role.description && role.description !== role.name && (
                      <span style={{ color: '#666' }}>({role.name})</span>
                    )}
                  </Option>
                ))}
              </Select>
            </Form.Item>



            <div style={{ padding: '12px', backgroundColor: '#e6f7ff', borderRadius: '6px', border: '1px solid #91d5ff' }}>
              <p style={{ margin: 0, fontSize: '12px', color: '#666' }}>
                <strong>权限说明：</strong><br />
                • <Tag color="red" size="small">管理员</Tag>：拥有系统所有权限<br />
                • <Tag color="blue" size="small">法务人员</Tag>：可以处理案件和查看统计<br />
                • <Tag color="green" size="small">客户</Tag>：只能查看自己的案件<br />
                • <Tag color="orange" size="small">助理</Tag>：协助处理案件
              </p>
            </div>
          </Form>
        </Modal>
      </Card>
    </div>
  );
};

export default UserPermissionManagement;
