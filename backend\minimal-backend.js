const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3001;

// 基本中间件
app.use(cors());
app.use(express.json());

// 基础路由
app.get('/', (req, res) => {
    res.json({ message: 'Backend is running', port: PORT });
});

app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 简化的登录API
app.post('/api/auth/login', (req, res) => {
    const { username, password } = req.body;
    
    console.log('Login attempt:', { username, password });
    
    // 简单验证
    if (username === 'admin' && password === 'admin123') {
        const response = {
            success: true,
            data: {
                token: 'jwt-token-' + Date.now(),
                user: {
                    id: 1,
                    username: 'admin',
                    real_name: '系统管理员',
                    email: '<EMAIL>',
                    roles: [{ id: 1, name: 'admin', description: '系统管理员' }]
                }
            }
        };
        console.log('Login successful:', response);
        res.json(response);
    } else if (username === 'lawyer1' && password === 'lawyer123') {
        const response = {
            success: true,
            data: {
                token: 'jwt-token-' + Date.now(),
                user: {
                    id: 2,
                    username: 'lawyer1',
                    real_name: '张法务',
                    email: '<EMAIL>',
                    roles: [{ id: 2, name: 'lawyer', description: '法务人员' }]
                }
            }
        };
        console.log('Login successful:', response);
        res.json(response);
    } else {
        console.log('Login failed: Invalid credentials');
        res.status(401).json({
            error: 'Invalid username or password',
            code: 'INVALID_CREDENTIALS'
        });
    }
});

// 简化的案件API
app.get('/api/cases', (req, res) => {
    res.json({
        success: true,
        data: {
            cases: [
                {
                    id: 1,
                    case_number: 'CASE20250708001',
                    title: '示例合同纠纷案件',
                    status: '待处理',
                    priority: '中',
                    created_at: new Date().toISOString()
                }
            ],
            total: 1,
            page: 1,
            limit: 10
        }
    });
});

// 简化的统计API
app.get('/api/stats/overview', (req, res) => {
    res.json({
        success: true,
        data: {
            overview: {
                totalCases: 1,
                newCasesThisMonth: 1,
                upcomingCases: 0,
                unreadNotifications: 0
            },
            casesByStatus: {
                '待处理': 1,
                '处理中': 0,
                '已结案': 0,
                '已归档': 0
            }
        }
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`Minimal backend server running on http://localhost:${PORT}`);
    console.log(`Health check: http://localhost:${PORT}/health`);
    console.log(`Login API: http://localhost:${PORT}/api/auth/login`);
});
