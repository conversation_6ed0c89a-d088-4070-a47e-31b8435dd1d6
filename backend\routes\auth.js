const express = require('express');
const router = express.Router();
const { User, Role } = require('../models');
const JWTUtils = require('../utils/jwt');
const { authenticate } = require('../middleware/auth');

/**
 * 用户登录
 * POST /api/auth/login
 */
router.post('/login', async (req, res) => {
    try {
        const { username, password } = req.body;

        // 验证输入
        if (!username || !password) {
            return res.status(400).json({
                error: 'Username and password are required',
                code: 'MISSING_CREDENTIALS'
            });
        }

        // 查找用户（包含角色信息）
        const user = await User.findOne({
            where: { username },
            include: [{
                model: Role,
                as: 'roles',
                attributes: ['id', 'name', 'description']
            }]
        });

        if (!user) {
            return res.status(401).json({
                error: 'Invalid username or password',
                code: 'INVALID_CREDENTIALS'
            });
        }

        // 检查用户状态
        if (user.status !== 1) {
            return res.status(401).json({
                error: 'User account is disabled',
                code: 'USER_DISABLED'
            });
        }

        // 验证密码
        const isPasswordValid = await user.validatePassword(password);
        if (!isPasswordValid) {
            return res.status(401).json({
                error: 'Invalid username or password',
                code: 'INVALID_CREDENTIALS'
            });
        }

        // 生成JWT token
        const payload = JWTUtils.createUserPayload(user);
        const token = JWTUtils.generateToken(payload);

        // 记录登录日志
        const { Log } = require('../models');
        await Log.create({
            user_id: user.id,
            action: '用户登录',
            module: '认证登录',
            detail: JSON.stringify({
                username: user.username,
                ip_address: req.ip,
                user_agent: req.get('User-Agent')
            }),
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            status: 'success'
        });

        res.json({
            message: 'Login successful',
            token,
            user: {
                id: user.id,
                username: user.username,
                real_name: user.real_name,
                email: user.email,
                roles: user.roles.map(role => ({
                    id: role.id,
                    name: role.name,
                    description: role.description
                }))
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            error: 'Login failed',
            code: 'LOGIN_ERROR'
        });
    }
});

/**
 * 用户注册
 * POST /api/auth/register
 */
router.post('/register', async (req, res) => {
    try {
        const { username, password, real_name, email } = req.body;

        // 验证输入
        if (!username || !password || !real_name) {
            return res.status(400).json({
                error: 'Username, password and real_name are required',
                code: 'MISSING_FIELDS'
            });
        }

        // 检查用户名是否已存在
        const existingUser = await User.findOne({ where: { username } });
        if (existingUser) {
            return res.status(409).json({
                error: 'Username already exists',
                code: 'USERNAME_EXISTS'
            });
        }

        // 检查邮箱是否已存在
        if (email) {
            const existingEmail = await User.findOne({ where: { email } });
            if (existingEmail) {
                return res.status(409).json({
                    error: 'Email already exists',
                    code: 'EMAIL_EXISTS'
                });
            }
        }

        // 创建用户
        const user = await User.create({
            username,
            password,
            real_name,
            email,
            status: 1
        });

        // 分配默认角色（客户）
        const clientRole = await Role.findOne({ where: { name: 'client' } });
        if (clientRole) {
            const { UserRole } = require('../models');
            await UserRole.create({
                user_id: user.id,
                role_id: clientRole.id
            });
        }

        // 重新查询用户（包含角色）
        const userWithRoles = await User.findByPk(user.id, {
            include: [{
                model: Role,
                as: 'roles',
                attributes: ['id', 'name', 'description']
            }],
            attributes: { exclude: ['password'] }
        });

        // 生成JWT token
        const payload = JWTUtils.createUserPayload(userWithRoles);
        const token = JWTUtils.generateToken(payload);

        // 记录注册日志
        const { Log } = require('../models');
        await Log.create({
            user_id: user.id,
            action: '用户注册',
            module: '认证登录',
            detail: JSON.stringify({
                username: user.username,
                real_name: user.real_name,
                email: user.email
            }),
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            status: 'success'
        });

        res.status(201).json({
            message: 'Registration successful',
            token,
            user: {
                id: userWithRoles.id,
                username: userWithRoles.username,
                real_name: userWithRoles.real_name,
                email: userWithRoles.email,
                roles: userWithRoles.roles.map(role => ({
                    id: role.id,
                    name: role.name,
                    description: role.description
                }))
            }
        });

    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({
            error: 'Registration failed',
            code: 'REGISTRATION_ERROR'
        });
    }
});

/**
 * 获取当前用户信息
 * GET /api/auth/profile
 */
router.get('/profile', authenticate, async (req, res) => {
    try {
        res.json({
            user: {
                id: req.user.id,
                username: req.user.username,
                real_name: req.user.real_name,
                email: req.user.email,
                status: req.user.status,
                created_at: req.user.created_at,
                roles: req.user.roles.map(role => ({
                    id: role.id,
                    name: role.name,
                    description: role.description
                }))
            }
        });
    } catch (error) {
        console.error('Profile error:', error);
        res.status(500).json({
            error: 'Failed to get profile',
            code: 'PROFILE_ERROR'
        });
    }
});

/**
 * 刷新token
 * POST /api/auth/refresh
 */
router.post('/refresh', authenticate, async (req, res) => {
    try {
        const payload = JWTUtils.createUserPayload(req.user);
        const newToken = JWTUtils.generateToken(payload);

        res.json({
            message: 'Token refreshed successfully',
            token: newToken
        });
    } catch (error) {
        console.error('Token refresh error:', error);
        res.status(500).json({
            error: 'Failed to refresh token',
            code: 'REFRESH_ERROR'
        });
    }
});

/**
 * 用户登出
 * POST /api/auth/logout
 */
router.post('/logout', authenticate, async (req, res) => {
    try {
        // 记录登出日志
        const { Log } = require('../models');
        await Log.create({
            user_id: req.user.id,
            action: '用户登出',
            module: '认证登录',
            detail: JSON.stringify({
                username: req.user.username
            }),
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            status: 'success'
        });

        res.json({
            message: 'Logout successful'
        });
    } catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({
            error: 'Logout failed',
            code: 'LOGOUT_ERROR'
        });
    }
});

module.exports = router;
