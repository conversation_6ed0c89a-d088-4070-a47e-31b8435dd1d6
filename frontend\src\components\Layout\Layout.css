/* 布局样式 */
.layout-logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 全局布局优化 */
.ant-layout {
  background: #f5f5f5;
  height: 100vh;
  overflow: hidden;
}

.ant-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.layout-logo h2 {
  margin: 0;
  font-weight: 600;
  letter-spacing: 1px;
}

/* 侧边栏独立滚动 */
.ant-layout-sider {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.ant-layout-sider .ant-menu {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 菜单样式优化 */
.ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff !important;
}

.ant-menu-dark .ant-menu-submenu-title:hover,
.ant-menu-dark .ant-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* 头部样式 */
.ant-layout-header {
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

/* 内容区域独立滚动 */
.ant-layout-content {
  height: calc(100vh - 64px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-logo h2 {
    font-size: 16px !important;
  }

  .ant-layout-content {
    padding: 16px !important;
  }

  .ant-layout-content>div {
    padding: 20px !important;
  }

  .ant-layout-header {
    padding: 0 16px !important;
  }

  /* 移动端侧边栏调整 */
  .ant-layout-sider {
    position: fixed !important;
    z-index: 1000;
    height: 100vh !important;
  }
}

@media (max-width: 576px) {
  .ant-layout-content {
    padding: 12px !important;
  }

  .ant-layout-content>div {
    padding: 16px !important;
    border-radius: 8px !important;
  }

  .ant-layout-header {
    padding: 0 12px !important;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}