import React, { useState } from 'react';
import { useN<PERSON>gate, Link } from 'react-router-dom';
import {
  Form,
  Input,
  Button,
  Card,
  Typography,
  message,
  Divider,
  Space,
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  LoginOutlined,
} from '@ant-design/icons';

import { authAPI } from '../../services/auth';
import { setToken, setUser } from '../../utils/auth';
import './Login.css';

const { Title, Text } = Typography;

const Login = () => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const navigate = useNavigate();

  // 处理登录
  const handleLogin = async (values) => {
    setLoading(true);
    try {
      const response = await authAPI.login(values);
      const { token, user } = response.data;

      // 保存认证信息
      setToken(token);
      setUser(user);

      message.success('登录成功！');
      navigate('/dashboard');
    } catch (error) {
      console.error('登录失败:', error);
      // 错误信息已在 API 拦截器中处理
    } finally {
      setLoading(false);
    }
  };

  // 快速登录（演示用）
  const handleQuickLogin = (username, password) => {
    form.setFieldsValue({ username, password });
    handleLogin({ username, password });
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-overlay" />
      </div>

      <div className="login-content">
        <Card className="login-card" bordered={false}>
          {/* 标题 */}
          <div className="login-header">
            <Title level={2} style={{ textAlign: 'center', marginBottom: 8 }}>
              法务案件管理平台
            </Title>
            <Text type="secondary" style={{ display: 'block', textAlign: 'center' }}>
              Sie Dispute Manager
            </Text>
          </div>

          {/* 登录表单 */}
          <Form
            form={form}
            name="login"
            onFinish={handleLogin}
            autoComplete="off"
            size="large"
            style={{ marginTop: 32 }}
          >
            <Form.Item
              name="username"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3个字符' },
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="用户名"
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' },
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="密码"
                autoComplete="current-password"
              />
            </Form.Item>

            <Form.Item style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                icon={<LoginOutlined />}
              >
                登录
              </Button>
            </Form.Item>
          </Form>

          {/* 演示账户 */}
          <Divider>演示账户</Divider>

          <Space direction="vertical" style={{ width: '100%' }} size="small">
            <Button
              block
              onClick={() => handleQuickLogin('admin', 'admin123')}
              loading={loading}
            >
              管理员账户 (admin)
            </Button>

            <Button
              block
              onClick={() => handleQuickLogin('lawyer1', 'lawyer123')}
              loading={loading}
            >
              法务账户 (lawyer1)
            </Button>
          </Space>

          {/* 注册链接 */}
          <div style={{ textAlign: 'center', marginTop: 16 }}>
            <Text type="secondary">
              还没有账户？
              <Link to="/register" style={{ marginLeft: 8 }}>
                立即注册
              </Link>
            </Text>
          </div>

          {/* 系统信息 */}
          <div className="login-footer">
            <Text type="secondary" style={{ fontSize: '12px' }}>
              © 2025 Sie Dispute Manager v1.0.0
            </Text>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Login;
