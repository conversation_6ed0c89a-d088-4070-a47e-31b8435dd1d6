import React, { useState, useEffect } from 'react';
import {
  Typography,
  Card,
  Table,
  Button,
  Upload,
  Modal,
  Form,
  Input,
  Select,
  Space,
  Tag,
  Popconfirm,
  message,
  Row,
  Col,
  Statistic,
  Progress,
  Tooltip,
  Empty,
  Spin,
  Alert,
  Image,
  Checkbox,
  Dropdown,
  Menu,
  Divider,
} from 'antd';
import {
  UploadOutlined,
  DownloadOutlined,
  DeleteOutlined,
  FileOutlined,
  FolderOutlined,
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  InfoCircleOutlined,
  MoreOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FileZipOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { useSearchParams } from 'react-router-dom';
import { filesAPI } from '../../services/api';
import { casesAPI } from '../../services/cases';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;
const { Dragger } = Upload;

const FileManagement = () => {
  const [searchParams] = useSearchParams();
  const caseId = searchParams.get('caseId');

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [files, setFiles] = useState([]);
  const [cases, setCases] = useState([]);
  const [selectedCase, setSelectedCase] = useState(caseId || null);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [filters, setFilters] = useState({
    category: '',
    search: '',
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [statistics, setStatistics] = useState({
    totalFiles: 0,
    totalSize: 0,
    categories: {},
  });

  // 新增状态
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [previewFile, setPreviewFile] = useState(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [batchLoading, setBatchLoading] = useState(false);

  const [form] = Form.useForm();

  // 文件分类选项
  const FILE_CATEGORIES = [
    { value: '合同文件', label: '合同文件', color: 'blue' },
    { value: '证据材料', label: '证据材料', color: 'green' },
    { value: '法律文书', label: '法律文书', color: 'orange' },
    { value: '通信记录', label: '通信记录', color: 'purple' },
    { value: '财务资料', label: '财务资料', color: 'red' },
    { value: '其他', label: '其他', color: 'default' },
  ];

  // 获取文件大小格式化
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取文件图标
  const getFileIcon = (fileName, fileType) => {
    const ext = fileName.split('.').pop().toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(ext)) {
      return <FileImageOutlined style={{ color: '#52c41a' }} />;
    } else if (['pdf'].includes(ext)) {
      return <FilePdfOutlined style={{ color: '#ff4d4f' }} />;
    } else if (['doc', 'docx'].includes(ext)) {
      return <FileWordOutlined style={{ color: '#1890ff' }} />;
    } else if (['xls', 'xlsx'].includes(ext)) {
      return <FileExcelOutlined style={{ color: '#52c41a' }} />;
    } else if (['zip', 'rar', '7z'].includes(ext)) {
      return <FileZipOutlined style={{ color: '#722ed1' }} />;
    }
    return <FileTextOutlined style={{ color: '#8c8c8c' }} />;
  };

  // 检查文件是否可预览
  const isPreviewable = (fileName) => {
    const ext = fileName.split('.').pop().toLowerCase();
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'pdf'].includes(ext);
  };

  // 处理文件预览
  const handlePreview = (file) => {
    setPreviewFile(file);
    setPreviewModalVisible(true);
  };

  // 初始化加载
  useEffect(() => {
    fetchCases();
    if (selectedCase) {
      fetchFiles();
    }
  }, [selectedCase]);

  // 获取案件列表
  const fetchCases = async () => {
    try {
      const response = await casesAPI.getCases({ limit: 100 });
      if (response.data) {
        // 后端返回的数据结构是 { success: true, data: [案件数组], pagination: {...} }
        setCases(response.data.data || []);
      }
    } catch (error) {
      console.error('获取案件列表失败:', error);
    }
  };

  // 获取文件列表
  const fetchFiles = async () => {
    if (!selectedCase) return;

    setLoading(true);
    try {
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        ...filters,
      };

      const response = await filesAPI.getCaseFiles(selectedCase, params);

      if (response.data) {
        setFiles(response.data.files || []);
        setPagination(prev => ({
          ...prev,
          total: response.data.pagination?.total || 0,
        }));

        // 计算统计信息
        const stats = {
          totalFiles: response.data.pagination?.total || 0,
          totalSize: response.data.files?.reduce((sum, file) => sum + (file.file_size || 0), 0) || 0,
          categories: {},
        };

        response.data.files?.forEach(file => {
          const category = file.file_category || '其他';
          stats.categories[category] = (stats.categories[category] || 0) + 1;
        });

        setStatistics(stats);
      }
    } catch (error) {
      console.error('获取文件列表失败:', error);
      message.error('获取文件列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理文件上传
  const handleUpload = async (values) => {
    if (fileList.length === 0) {
      message.error('请选择要上传的文件');
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      fileList.forEach(file => {
        formData.append('files', file.originFileObj);
      });
      formData.append('description', values.description || '');
      formData.append('file_category', values.file_category || '其他');

      await filesAPI.uploadFiles(selectedCase, formData, (progress) => {
        setUploadProgress(progress);
      });

      message.success('文件上传成功');
      setUploadModalVisible(false);
      setFileList([]);
      form.resetFields();
      fetchFiles();
    } catch (error) {
      console.error('文件上传失败:', error);
      message.error('文件上传失败，请稍后重试');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  // 处理文件下载
  const handleDownload = async (file) => {
    try {
      await filesAPI.downloadFile(file.id, file.original_name);
      message.success('文件下载开始');
    } catch (error) {
      console.error('文件下载失败:', error);
      message.error('文件下载失败，请稍后重试');
    }
  };

  // 处理文件删除
  const handleDelete = async (fileId) => {
    try {
      await filesAPI.deleteFile(fileId);
      message.success('文件删除成功');
      fetchFiles();
      // 清除选中状态
      setSelectedRowKeys(prev => prev.filter(key => key !== fileId));
    } catch (error) {
      console.error('文件删除失败:', error);
      message.error('文件删除失败，请稍后重试');
    }
  };

  // 批量删除文件
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的文件');
      return;
    }

    setBatchLoading(true);
    try {
      await Promise.all(selectedRowKeys.map(fileId => filesAPI.deleteFile(fileId)));
      message.success(`成功删除 ${selectedRowKeys.length} 个文件`);
      setSelectedRowKeys([]);
      fetchFiles();
    } catch (error) {
      console.error('批量删除失败:', error);
      message.error('批量删除失败，请稍后重试');
    } finally {
      setBatchLoading(false);
    }
  };

  // 批量下载文件
  const handleBatchDownload = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要下载的文件');
      return;
    }

    setBatchLoading(true);
    try {
      const selectedFiles = files.filter(file => selectedRowKeys.includes(file.id));
      for (const file of selectedFiles) {
        await filesAPI.downloadFile(file.id, file.original_name);
      }
      message.success(`开始下载 ${selectedRowKeys.length} 个文件`);
    } catch (error) {
      console.error('批量下载失败:', error);
      message.error('批量下载失败，请稍后重试');
    } finally {
      setBatchLoading(false);
    }
  };

  // 处理搜索
  const handleSearch = (value) => {
    setFilters(prev => ({ ...prev, search: value }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 处理分类过滤
  const handleCategoryFilter = (category) => {
    setFilters(prev => ({ ...prev, category }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 表格行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record) => ({
      disabled: false,
      name: record.original_name,
    }),
  };

  // 表格列定义
  const columns = [
    {
      title: '文件名',
      dataIndex: 'original_name',
      key: 'original_name',
      ellipsis: true,
      render: (text, record) => (
        <Space>
          <span style={{ fontSize: '16px' }}>
            {getFileIcon(text, record.file_type)}
          </span>
          <Tooltip title={text}>
            <span>{text}</span>
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '分类',
      dataIndex: 'file_category',
      key: 'file_category',
      width: 120,
      render: (category) => {
        const categoryInfo = FILE_CATEGORIES.find(c => c.value === category) ||
          { color: 'default', label: category };
        return <Tag color={categoryInfo.color}>{categoryInfo.label}</Tag>;
      },
    },
    {
      title: '文件大小',
      dataIndex: 'file_size',
      key: 'file_size',
      width: 100,
      render: (size) => formatFileSize(size),
    },
    {
      title: '上传者',
      dataIndex: ['uploader', 'real_name'],
      key: 'uploader',
      width: 100,
      render: (name, record) => name || record.uploader?.username || '-',
    },
    {
      title: '上传时间',
      dataIndex: 'uploaded_at',
      key: 'uploaded_at',
      width: 150,
      render: (time) => dayjs(time).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 180,
      render: (_, record) => (
        <Space>
          {isPreviewable(record.original_name) && (
            <Tooltip title="预览">
              <Button
                type="text"
                icon={<EyeOutlined />}
                onClick={() => handlePreview(record)}
              />
            </Tooltip>
          )}
          <Tooltip title="下载">
            <Button
              type="text"
              icon={<DownloadOutlined />}
              onClick={() => handleDownload(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个文件吗？"
              description="删除后无法恢复，请谨慎操作。"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>
        <FileOutlined /> 文件管理
      </Title>

      {/* 案件选择和统计信息 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={8}>
          <Card size="small">
            <Statistic
              title="总文件数"
              value={statistics.totalFiles}
              prefix={<FileOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Card size="small">
            <Statistic
              title="总大小"
              value={formatFileSize(statistics.totalSize)}
              prefix={<FolderOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <Card size="small" title="案件选择">
            <Select
              style={{ width: '100%' }}
              placeholder="选择案件"
              value={selectedCase}
              onChange={setSelectedCase}
              showSearch
              optionFilterProp="children"
            >
              {cases.map(caseItem => (
                <Option key={caseItem.id} value={caseItem.id}>
                  {caseItem.case_no} - {caseItem.title}
                </Option>
              ))}
            </Select>
          </Card>
        </Col>
      </Row>

      {!selectedCase ? (
        <Card>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="请先选择一个案件来管理文件"
          />
        </Card>
      ) : (
        <>
          {/* 操作栏 */}
          <Card style={{ marginBottom: 16 }}>
            <Row justify="space-between" align="middle">
              <Col>
                <Space>
                  <Input.Search
                    placeholder="搜索文件名..."
                    allowClear
                    style={{ width: 250 }}
                    onSearch={handleSearch}
                  />
                  <Select
                    placeholder="文件分类"
                    allowClear
                    style={{ width: 150 }}
                    value={filters.category}
                    onChange={handleCategoryFilter}
                  >
                    {FILE_CATEGORIES.map(category => (
                      <Option key={category.value} value={category.value}>
                        <Tag color={category.color}>{category.label}</Tag>
                      </Option>
                    ))}
                  </Select>
                </Space>
              </Col>
              <Col>
                <Space>
                  {selectedRowKeys.length > 0 && (
                    <>
                      <Dropdown
                        overlay={
                          <Menu>
                            <Menu.Item
                              key="download"
                              icon={<DownloadOutlined />}
                              onClick={handleBatchDownload}
                            >
                              批量下载
                            </Menu.Item>
                            <Menu.Divider />
                            <Menu.Item
                              key="delete"
                              icon={<DeleteOutlined />}
                              danger
                              onClick={() => {
                                Modal.confirm({
                                  title: '确定要删除选中的文件吗？',
                                  content: `将删除 ${selectedRowKeys.length} 个文件，删除后无法恢复。`,
                                  okText: '确定',
                                  cancelText: '取消',
                                  onOk: handleBatchDelete,
                                });
                              }}
                            >
                              批量删除
                            </Menu.Item>
                          </Menu>
                        }
                        trigger={['click']}
                      >
                        <Button loading={batchLoading}>
                          批量操作 ({selectedRowKeys.length}) <MoreOutlined />
                        </Button>
                      </Dropdown>
                      <Button
                        onClick={() => setSelectedRowKeys([])}
                      >
                        取消选择
                      </Button>
                    </>
                  )}
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={fetchFiles}
                    loading={loading}
                  >
                    刷新
                  </Button>
                  <Button
                    type="primary"
                    icon={<UploadOutlined />}
                    onClick={() => setUploadModalVisible(true)}
                  >
                    上传文件
                  </Button>
                </Space>
              </Col>
            </Row>
          </Card>

          {/* 文件列表 */}
          <Card>
            <Table
              columns={columns}
              dataSource={files}
              rowKey="id"
              loading={loading}
              rowSelection={rowSelection}
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: (page, pageSize) => {
                  setPagination(prev => ({ ...prev, current: page, pageSize }));
                },
              }}
              scroll={{ x: 800 }}
            />
          </Card>
        </>
      )}

      {/* 文件上传模态框 */}
      <Modal
        title="上传文件"
        open={uploadModalVisible}
        onCancel={() => {
          setUploadModalVisible(false);
          setFileList([]);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpload}
        >
          <Form.Item
            name="file_category"
            label="文件分类"
            rules={[{ required: true, message: '请选择文件分类' }]}
          >
            <Select placeholder="选择文件分类">
              {FILE_CATEGORIES.map(category => (
                <Option key={category.value} value={category.value}>
                  <Tag color={category.color}>{category.label}</Tag>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="文件描述"
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入文件描述（可选）"
            />
          </Form.Item>

          <Form.Item
            label="选择文件"
            required
          >
            <Dragger
              multiple
              fileList={fileList}
              onChange={({ fileList }) => setFileList(fileList)}
              beforeUpload={() => false} // 阻止自动上传
              accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif,.zip,.rar"
            >
              <p className="ant-upload-drag-icon">
                <UploadOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">
                支持单个或批量上传。支持 PDF、Word、Excel、图片等格式，单个文件不超过 50MB
              </p>
            </Dragger>
          </Form.Item>

          {uploading && (
            <Form.Item>
              <Progress percent={uploadProgress} status="active" />
            </Form.Item>
          )}

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={uploading}
                disabled={fileList.length === 0}
              >
                开始上传
              </Button>
              <Button
                onClick={() => {
                  setUploadModalVisible(false);
                  setFileList([]);
                  form.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 文件预览模态框 */}
      <Modal
        title={`预览文件: ${previewFile?.original_name || ''}`}
        open={previewModalVisible}
        onCancel={() => {
          setPreviewModalVisible(false);
          setPreviewFile(null);
        }}
        footer={[
          <Button
            key="download"
            icon={<DownloadOutlined />}
            onClick={() => handleDownload(previewFile)}
          >
            下载
          </Button>,
          <Button
            key="close"
            onClick={() => {
              setPreviewModalVisible(false);
              setPreviewFile(null);
            }}
          >
            关闭
          </Button>,
        ]}
        width={800}
        style={{ top: 20 }}
      >
        {previewFile && (
          <div style={{ textAlign: 'center', minHeight: '400px' }}>
            {(() => {
              const ext = previewFile.original_name.split('.').pop().toLowerCase();
              if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(ext)) {
                return (
                  <Image
                    src={`/api/files/download/${previewFile.id}`}
                    alt={previewFile.original_name}
                    style={{ maxWidth: '100%', maxHeight: '600px' }}
                    fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                  />
                );
              } else if (ext === 'pdf') {
                return (
                  <div style={{ height: '600px' }}>
                    <iframe
                      src={`/api/files/download/${previewFile.id}`}
                      width="100%"
                      height="100%"
                      style={{ border: 'none' }}
                      title={previewFile.original_name}
                    />
                  </div>
                );
              } else {
                return (
                  <div style={{ padding: '40px', color: '#999' }}>
                    <FileOutlined style={{ fontSize: '64px', marginBottom: '16px' }} />
                    <p>此文件类型不支持预览</p>
                    <p>文件名: {previewFile.original_name}</p>
                    <p>文件大小: {formatFileSize(previewFile.file_size)}</p>
                    <Button
                      type="primary"
                      icon={<DownloadOutlined />}
                      onClick={() => handleDownload(previewFile)}
                    >
                      下载文件
                    </Button>
                  </div>
                );
              }
            })()}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default FileManagement;
