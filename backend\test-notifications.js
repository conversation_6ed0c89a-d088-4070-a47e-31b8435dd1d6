const http = require('http');

function makeRequest(options, data) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    body: body
                });
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(data);
        }
        req.end();
    });
}

async function login() {
    console.log('🔐 登录获取token...');
    
    const loginData = JSON.stringify({
        username: 'admin',
        password: 'admin123'
    });

    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(loginData)
        }
    };

    try {
        const response = await makeRequest(options, loginData);
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 登录成功');
            return data.token;
        } else {
            console.log('❌ 登录失败');
            return null;
        }
    } catch (error) {
        console.error('❌ 登录错误:', error.message);
        return null;
    }
}

async function testCreateNotification(token) {
    console.log('\n📢 测试创建通知...');
    
    const notificationData = JSON.stringify({
        title: '系统维护通知',
        content: '系统将于今晚22:00-24:00进行维护，期间可能无法正常访问，请提前做好相关准备。',
        type: '系统通知'
    });

    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/notifications',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(notificationData),
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options, notificationData);
        console.log('状态码:', response.statusCode);
        
        if (response.statusCode === 201) {
            const data = JSON.parse(response.body);
            console.log('✅ 通知创建成功');
            console.log('通知数量:', data.notifications_count);
            console.log('目标用户数:', data.target_users);
        } else {
            console.log('❌ 通知创建失败');
            console.log('响应:', response.body);
        }
    } catch (error) {
        console.error('❌ 创建通知错误:', error.message);
    }
}

async function testGetNotifications(token) {
    console.log('\n📋 测试获取通知列表...');
    
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/notifications?page=1&limit=10',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options);
        console.log('状态码:', response.statusCode);
        
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 获取通知列表成功');
            console.log('通知总数:', data.pagination.total);
            console.log('未读通知数:', data.unread_count);
            console.log('当前页通知数:', data.notifications.length);
            
            if (data.notifications.length > 0) {
                console.log('最新通知:', data.notifications[0].title);
                return data.notifications[0].id;
            }
        } else {
            console.log('❌ 获取通知列表失败');
            console.log('响应:', response.body);
        }
    } catch (error) {
        console.error('❌ 获取通知列表错误:', error.message);
    }
    return null;
}

async function testMarkNotificationAsRead(token, notificationId) {
    if (!notificationId) return;
    
    console.log('\n✅ 测试标记通知为已读...');
    
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: `/api/notifications/${notificationId}/read`,
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options);
        console.log('状态码:', response.statusCode);
        
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 标记为已读成功');
            console.log('通知状态:', data.notification.status);
        } else {
            console.log('❌ 标记为已读失败');
            console.log('响应:', response.body);
        }
    } catch (error) {
        console.error('❌ 标记为已读错误:', error.message);
    }
}

async function testNotificationStats(token) {
    console.log('\n📊 测试获取通知统计...');
    
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/notifications/stats',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options);
        console.log('状态码:', response.statusCode);
        
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 获取通知统计成功');
            console.log('总通知数:', data.total);
            console.log('未读通知数:', data.unread);
            console.log('已读通知数:', data.read);
            console.log('按类型统计:', data.by_type);
        } else {
            console.log('❌ 获取通知统计失败');
            console.log('响应:', response.body);
        }
    } catch (error) {
        console.error('❌ 获取通知统计错误:', error.message);
    }
}

async function testCaseStatusChangeNotification(token) {
    console.log('\n🔄 测试案件状态变更通知...');
    
    // 使用已存在的案件ID (从之前的测试中我们知道有案件ID为2)
    const caseId = 2;
    
    const statusData = JSON.stringify({
        status: '已结案',
        remark: '案件处理完毕，测试通知功能'
    });

    const options = {
        hostname: 'localhost',
        port: 3000,
        path: `/api/cases/${caseId}/status`,
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(statusData),
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options, statusData);
        console.log('状态码:', response.statusCode);
        
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 案件状态更新成功');
            console.log('新状态:', data.case.status);
            console.log('📢 应该已自动发送状态变更通知');
        } else {
            console.log('❌ 案件状态更新失败');
            console.log('响应:', response.body);
        }
    } catch (error) {
        console.error('❌ 案件状态更新错误:', error.message);
    }
}

async function main() {
    console.log('🧪 开始测试通知功能...\n');
    
    const token = await login();
    if (!token) {
        console.log('❌ 无法获取token，测试终止');
        return;
    }
    
    await testCreateNotification(token);
    await testCaseStatusChangeNotification(token);
    
    // 等待一下让通知生成
    console.log('\n⏳ 等待通知生成...');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const notificationId = await testGetNotifications(token);
    await testMarkNotificationAsRead(token, notificationId);
    await testNotificationStats(token);
    
    console.log('\n✅ 通知功能测试完成');
}

main();
