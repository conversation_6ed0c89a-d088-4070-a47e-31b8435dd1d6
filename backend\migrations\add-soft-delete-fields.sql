-- 添加软删除支持的数据库迁移脚本
-- 执行时间: 2025-07-10

-- 1. 为 cases 表添加 deleted_at 字段
ALTER TABLE `cases` 
ADD COLUMN `deleted_at` DATETIME NULL COMMENT '删除时间（软删除）' AFTER `updated_at`;

-- 2. 为 cases 表的 status 字段添加 '已删除' 选项
ALTER TABLE `cases` 
MODIFY COLUMN `status` VARCHAR(50) NOT NULL DEFAULT '待处理' 
COMMENT '案件状态' 
CHECK (`status` IN ('待处理', '处理中', '已结案', '已归档', '已撤销', '已删除'));

-- 3. 创建案件操作日志表
CREATE TABLE IF NOT EXISTS `case_operation_logs` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `case_id` BIGINT NOT NULL COMMENT '案件ID',
    `operation_type` VARCHAR(50) NOT NULL COMMENT '操作类型',
    `operator_id` BIGINT NULL COMMENT '操作人ID',
    `operation_detail` TEXT NULL COMMENT '操作详情',
    `old_data` JSON NULL COMMENT '操作前数据',
    `new_data` JSON NULL COMMENT '操作后数据',
    `ip_address` VARCHAR(45) NULL COMMENT 'IP地址',
    `user_agent` TEXT NULL COMMENT '用户代理',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    PRIMARY KEY (`id`),
    INDEX `idx_case_id` (`case_id`),
    INDEX `idx_operator_id` (`operator_id`),
    INDEX `idx_operation_type` (`operation_type`),
    INDEX `idx_created_at` (`created_at`),
    CONSTRAINT `fk_case_operation_logs_case_id` 
        FOREIGN KEY (`case_id`) REFERENCES `cases` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_case_operation_logs_operator_id` 
        FOREIGN KEY (`operator_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
    CONSTRAINT `chk_operation_type` 
        CHECK (`operation_type` IN ('创建', '编辑', '状态变更', '分配', '删除', '恢复', '永久删除'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='案件操作日志表';

-- 4. 为 deleted_at 字段添加索引以提高查询性能
ALTER TABLE `cases` 
ADD INDEX `idx_deleted_at` (`deleted_at`);

-- 5. 添加复合索引以优化回收站查询
ALTER TABLE `cases` 
ADD INDEX `idx_deleted_at_owner` (`deleted_at`, `owner_id`);

-- 验证表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'case_manager' 
  AND TABLE_NAME = 'cases' 
  AND COLUMN_NAME IN ('deleted_at', 'status')
ORDER BY ORDINAL_POSITION;

-- 验证新表是否创建成功
SELECT COUNT(*) as table_exists 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'case_manager' 
  AND TABLE_NAME = 'case_operation_logs';
