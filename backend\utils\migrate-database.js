const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

/**
 * 执行数据库迁移
 */
async function runMigration() {
    let connection = null;
    
    try {
        console.log('🚀 开始执行数据库迁移...');
        
        // 创建数据库连接
        connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASS || '',
            database: process.env.DB_NAME || 'case_manager',
            multipleStatements: true
        });
        
        console.log('✅ 数据库连接成功');
        
        // 检查 deleted_at 字段是否已存在
        const [columns] = await connection.execute(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = ? 
              AND TABLE_NAME = 'cases' 
              AND COLUMN_NAME = 'deleted_at'
        `, [process.env.DB_NAME]);
        
        if (columns.length > 0) {
            console.log('✅ deleted_at 字段已存在，跳过迁移');
            return;
        }
        
        console.log('📋 开始添加 deleted_at 字段...');
        
        // 1. 添加 deleted_at 字段
        await connection.execute(`
            ALTER TABLE \`cases\` 
            ADD COLUMN \`deleted_at\` DATETIME NULL COMMENT '删除时间（软删除）' AFTER \`updated_at\`
        `);
        console.log('✅ 已添加 deleted_at 字段');
        
        // 2. 更新 status 字段的约束（如果支持的话）
        try {
            await connection.execute(`
                ALTER TABLE \`cases\` 
                MODIFY COLUMN \`status\` VARCHAR(50) NOT NULL DEFAULT '待处理' COMMENT '案件状态'
            `);
            console.log('✅ 已更新 status 字段约束');
        } catch (error) {
            console.log('⚠️ 更新 status 字段约束失败（可能不支持CHECK约束）:', error.message);
        }
        
        // 3. 检查并创建案件操作日志表
        const [tables] = await connection.execute(`
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = ? 
              AND TABLE_NAME = 'case_operation_logs'
        `, [process.env.DB_NAME]);
        
        if (tables.length === 0) {
            console.log('📋 创建案件操作日志表...');
            await connection.execute(`
                CREATE TABLE \`case_operation_logs\` (
                    \`id\` BIGINT NOT NULL AUTO_INCREMENT,
                    \`case_id\` BIGINT NOT NULL COMMENT '案件ID',
                    \`operation_type\` VARCHAR(50) NOT NULL COMMENT '操作类型',
                    \`operator_id\` BIGINT NULL COMMENT '操作人ID',
                    \`operation_detail\` TEXT NULL COMMENT '操作详情',
                    \`old_data\` JSON NULL COMMENT '操作前数据',
                    \`new_data\` JSON NULL COMMENT '操作后数据',
                    \`ip_address\` VARCHAR(45) NULL COMMENT 'IP地址',
                    \`user_agent\` TEXT NULL COMMENT '用户代理',
                    \`created_at\` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
                    PRIMARY KEY (\`id\`),
                    INDEX \`idx_case_id\` (\`case_id\`),
                    INDEX \`idx_operator_id\` (\`operator_id\`),
                    INDEX \`idx_operation_type\` (\`operation_type\`),
                    INDEX \`idx_created_at\` (\`created_at\`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
                COMMENT='案件操作日志表'
            `);
            console.log('✅ 已创建案件操作日志表');
        } else {
            console.log('✅ 案件操作日志表已存在');
        }
        
        // 4. 添加索引
        try {
            await connection.execute(`
                ALTER TABLE \`cases\` 
                ADD INDEX \`idx_deleted_at\` (\`deleted_at\`)
            `);
            console.log('✅ 已添加 deleted_at 索引');
        } catch (error) {
            if (error.code === 'ER_DUP_KEYNAME') {
                console.log('✅ deleted_at 索引已存在');
            } else {
                console.log('⚠️ 添加 deleted_at 索引失败:', error.message);
            }
        }
        
        try {
            await connection.execute(`
                ALTER TABLE \`cases\` 
                ADD INDEX \`idx_deleted_at_owner\` (\`deleted_at\`, \`owner_id\`)
            `);
            console.log('✅ 已添加复合索引');
        } catch (error) {
            if (error.code === 'ER_DUP_KEYNAME') {
                console.log('✅ 复合索引已存在');
            } else {
                console.log('⚠️ 添加复合索引失败:', error.message);
            }
        }
        
        // 验证迁移结果
        const [newColumns] = await connection.execute(`
            SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                COLUMN_COMMENT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = ? 
              AND TABLE_NAME = 'cases' 
              AND COLUMN_NAME = 'deleted_at'
        `, [process.env.DB_NAME]);
        
        if (newColumns.length > 0) {
            console.log('✅ 迁移验证成功:', newColumns[0]);
        }
        
        console.log('🎉 数据库迁移完成！');
        
    } catch (error) {
        console.error('❌ 数据库迁移失败:', error);
        throw error;
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔌 数据库连接已关闭');
        }
    }
}

/**
 * 检查数据库结构是否需要迁移
 */
async function checkMigrationNeeded() {
    let connection = null;
    
    try {
        connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASS || '',
            database: process.env.DB_NAME || 'case_manager'
        });
        
        const [columns] = await connection.execute(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = ? 
              AND TABLE_NAME = 'cases' 
              AND COLUMN_NAME = 'deleted_at'
        `, [process.env.DB_NAME]);
        
        return columns.length === 0;
        
    } catch (error) {
        console.error('检查迁移状态失败:', error);
        return true; // 如果检查失败，假设需要迁移
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

module.exports = {
    runMigration,
    checkMigrationNeeded
};

// 如果直接运行此脚本，执行迁移
if (require.main === module) {
    runMigration()
        .then(() => {
            console.log('迁移脚本执行完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('迁移脚本执行失败:', error);
            process.exit(1);
        });
}
