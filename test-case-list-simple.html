<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案件列表简单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .case-item {
            border: 1px solid #ddd;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>🔧 案件列表数据格式测试</h1>
    
    <div>
        <button onclick="testAPI()">测试API数据格式</button>
        <button onclick="clearResults()">清空结果</button>
    </div>
    
    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testAPI() {
            log('🧪 开始测试案件列表API数据格式...', 'info');
            
            try {
                // 1. 先登录
                log('1. 正在登录...', 'info');
                const loginResponse = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });

                const loginData = await loginResponse.json();
                if (!loginData.token) {
                    log('❌ 登录失败', 'error');
                    return;
                }
                log('✅ 登录成功', 'success');

                // 2. 获取案件列表
                log('2. 正在获取案件列表...', 'info');
                const casesResponse = await fetch('/api/cases?page=1&limit=5', {
                    headers: { 'Authorization': `Bearer ${loginData.token}` }
                });

                const casesData = await casesResponse.json();
                
                // 3. 分析数据结构
                log('3. 分析API响应数据结构...', 'info');
                log(`响应状态: ${casesResponse.status}`, 'info');
                log(`success字段: ${casesData.success}`, 'info');
                log(`data字段类型: ${typeof casesData.data}`, 'info');
                log(`data是否为数组: ${Array.isArray(casesData.data)}`, 'info');
                
                if (Array.isArray(casesData.data)) {
                    log(`✅ data是数组，长度: ${casesData.data.length}`, 'success');
                } else {
                    log(`❌ data不是数组: ${casesData.data}`, 'error');
                }

                if (casesData.pagination) {
                    log(`分页信息: total=${casesData.pagination.total}, page=${casesData.pagination.page}`, 'info');
                }

                // 4. 模拟前端处理逻辑
                log('4. 模拟前端数据处理...', 'info');
                let cases = [];
                let pagination = { current: 1, pageSize: 10, total: 0 };
                
                if (casesData.data) {
                    const casesDataProcessed = Array.isArray(casesData.data) ? casesData.data : [];
                    cases = casesDataProcessed;
                    
                    if (casesData.pagination) {
                        pagination = {
                            ...pagination,
                            total: casesData.pagination.total || 0,
                            current: casesData.pagination.page || 1,
                        };
                    }
                } else {
                    cases = [];
                }

                log(`✅ 前端处理完成: cases数组长度=${cases.length}, total=${pagination.total}`, 'success');

                // 5. 显示案件列表
                if (cases.length > 0) {
                    log('5. 案件列表预览:', 'info');
                    const casesContainer = document.createElement('div');
                    cases.slice(0, 3).forEach((caseItem, index) => {
                        const caseDiv = document.createElement('div');
                        caseDiv.className = 'case-item';
                        caseDiv.innerHTML = `
                            <strong>${index + 1}. ${caseItem.title}</strong><br>
                            编号: ${caseItem.case_no}<br>
                            状态: ${caseItem.status}<br>
                            负责人: ${caseItem.owner?.real_name || '未指定'}
                        `;
                        casesContainer.appendChild(caseDiv);
                    });
                    document.getElementById('results').appendChild(casesContainer);
                } else {
                    log('⚠️ 没有案件数据', 'error');
                }

                // 6. 显示完整响应
                log('6. 完整API响应:', 'info');
                const pre = document.createElement('pre');
                pre.textContent = JSON.stringify(casesData, null, 2);
                document.getElementById('results').appendChild(pre);

            } catch (error) {
                log('❌ 测试失败: ' + error.message, 'error');
                console.error('测试错误:', error);
            }
        }

        // 页面加载时的提示
        window.onload = function() {
            log('🚀 案件列表数据格式测试页面已加载', 'info');
            log('点击"测试API数据格式"按钮开始测试', 'info');
        };
    </script>
</body>
</html>
