import axios from 'axios';
import { message } from 'antd';
import { getToken, clearAuth } from '../utils/auth';
import errorHandler from '../utils/errorHandler';

// 创建 axios 实例
const api = axios.create({
  baseURL: '/api', // 使用相对路径，通过Vite代理到后端
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证 token
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 打印请求信息（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log('API Request:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        data: config.data,
      });
    }

    return config;
  },
  (error) => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 打印响应信息（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log('API Response:', {
        status: response.status,
        url: response.config.url,
        data: response.data,
      });
    }

    return response;
  },
  (error) => {
    // 只处理认证错误和网络错误，其他错误由组件处理
    if (error.response?.status === 401) {
      errorHandler.handleApiError(error, {
        showMessage: true,
        showNotification: false,
      });
    } else if (!error.response) {
      // 网络错误
      errorHandler.handleApiError(error, {
        showMessage: true,
        showNotification: false,
        retryable: true,
      });
    }

    return Promise.reject(error);
  }
);

// 通用请求方法
export const request = {
  get: (url, config = {}) => api.get(url, config),
  post: (url, data = {}, config = {}) => api.post(url, data, config),
  put: (url, data = {}, config = {}) => api.put(url, data, config),
  delete: (url, config = {}) => api.delete(url, config),
  patch: (url, data = {}, config = {}) => api.patch(url, data, config),
};

// 文件上传请求
export const uploadRequest = (url, formData, onProgress) => {
  return api.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const percentCompleted = Math.round(
          (progressEvent.loaded * 100) / progressEvent.total
        );
        onProgress(percentCompleted);
      }
    },
  });
};

// 文件下载请求
export const downloadRequest = async (url, filename = null) => {
  try {
    const response = await api.get(url, {
      responseType: 'blob',
    });

    // 从响应头获取文件名
    const contentDisposition = response.headers['content-disposition'];
    let downloadFilename = filename;

    if (!downloadFilename && contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (filenameMatch && filenameMatch[1]) {
        downloadFilename = filenameMatch[1].replace(/['"]/g, '');
      }
    }

    // 创建下载链接
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = downloadFilename || 'download';

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);

    return response;
  } catch (error) {
    console.error('文件下载失败:', error);
    throw error;
  }
};

// 文件管理相关 API
export const filesAPI = {
  /**
   * 获取案件文件列表
   * @param {number} caseId - 案件ID
   * @param {Object} params - 查询参数
   * @returns {Promise} 文件列表响应
   */
  getCaseFiles: (caseId, params = {}) => {
    return request.get(`/files/case/${caseId}`, { params });
  },

  /**
   * 上传文件到案件
   * @param {number} caseId - 案件ID
   * @param {FormData} formData - 文件数据
   * @param {Function} onProgress - 上传进度回调
   * @returns {Promise} 上传响应
   */
  uploadFiles: (caseId, formData, onProgress) => {
    return uploadRequest(`/files/upload/${caseId}`, formData, onProgress);
  },

  /**
   * 下载文件
   * @param {number} fileId - 文件ID
   * @param {string} filename - 文件名
   * @returns {Promise} 下载响应
   */
  downloadFile: (fileId, filename) => {
    return downloadRequest(`/files/download/${fileId}`, filename);
  },

  /**
   * 删除文件
   * @param {number} fileId - 文件ID
   * @returns {Promise} 删除响应
   */
  deleteFile: (fileId) => {
    return request.delete(`/files/${fileId}`);
  },

  /**
   * 获取所有文件列表（管理员）
   * @param {Object} params - 查询参数
   * @returns {Promise} 文件列表响应
   */
  getAllFiles: (params = {}) => {
    return request.get('/files', { params });
  },
};

export default api;
