const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const CaseFile = sequelize.define('CaseFile', {
    id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
    },
    case_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '案件ID',
        references: {
            model: 'cases',
            key: 'id'
        }
    },
    file_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '文件名',
        validate: {
            notEmpty: true,
        }
    },
    original_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '原始文件名',
        validate: {
            notEmpty: true,
        }
    },
    file_path: {
        type: DataTypes.STRING(500),
        allowNull: false,
        comment: '文件路径',
        validate: {
            notEmpty: true,
        }
    },
    file_size: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '文件大小（字节）',
    },
    file_type: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '文件类型/MIME类型',
    },
    file_category: {
        type: DataTypes.STRING(50),
        allowNull: false,
        defaultValue: '其他',
        comment: '文件分类',
        validate: {
            isIn: [['合同文件', '证据材料', '法律文书', '通信记录', '财务资料', '其他']]
        }
    },
    uploaded_by: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '上传人ID',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '文件描述',
    },
    uploaded_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '上传时间',
    },
}, {
    tableName: 'case_files',
    timestamps: false,
    indexes: [
        {
            fields: ['case_id']
        },
        {
            fields: ['uploaded_by']
        },
        {
            fields: ['file_category']
        },
        {
            fields: ['uploaded_at']
        }
    ]
});

module.exports = CaseFile;
