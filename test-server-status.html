<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器状态测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #1890ff;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .loading {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔍 服务器状态检查</h1>
    
    <div class="test-section">
        <h2 class="test-title">服务器连接测试</h2>
        
        <button onclick="testHealth()">测试健康检查</button>
        <div id="health-result" class="test-result loading">等待测试...</div>
        
        <button onclick="testLogin()">测试登录API</button>
        <div id="login-result" class="test-result loading">等待测试...</div>
        
        <button onclick="testCases()">测试案件API</button>
        <div id="cases-result" class="test-result loading">等待测试...</div>
        
        <button onclick="testRecycle()">测试回收站API</button>
        <div id="recycle-result" class="test-result loading">等待测试...</div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8001';

        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在测试健康检查...';

            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 健康检查通过<br>
                        状态: ${data.status}<br>
                        数据库: ${data.database}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 健康检查失败: ${error.message}`;
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在测试登录API...';

            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.token) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 登录API正常<br>
                        用户: ${data.user?.username}<br>
                        Token: ${data.token ? '已获取' : '未获取'}<br>
                    `;
                    window.authToken = data.token; // 保存token供其他测试使用
                } else {
                    throw new Error(data.message || `HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 登录API失败: ${error.message}`;
            }
        }

        async function testCases() {
            const resultDiv = document.getElementById('cases-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在测试案件API...';

            try {
                if (!window.authToken) {
                    throw new Error('请先执行登录测试');
                }

                const response = await fetch(`${API_BASE}/api/cases?page=1&limit=5`, {
                    headers: {
                        'Authorization': `Bearer ${window.authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 案件API正常<br>
                        案件数量: ${data.data ? data.data.length : 0}<br>
                        分页信息: ${data.pagination ? '正常' : '缺失'}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    throw new Error(data.message || `HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 案件API失败: ${error.message}`;
            }
        }

        async function testRecycle() {
            const resultDiv = document.getElementById('recycle-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在测试回收站API...';

            try {
                if (!window.authToken) {
                    throw new Error('请先执行登录测试');
                }

                const response = await fetch(`${API_BASE}/api/cases/recycle?page=1&limit=5`, {
                    headers: {
                        'Authorization': `Bearer ${window.authToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 回收站API正常<br>
                        已删除案件数量: ${data.data ? data.data.length : 0}<br>
                        这表明软删除功能已正常工作<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    throw new Error(data.message || `HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 回收站API失败: ${error.message}`;
            }
        }

        // 页面加载后自动执行测试
        window.onload = function() {
            setTimeout(() => {
                testHealth();
                setTimeout(() => testLogin(), 1000);
                setTimeout(() => testCases(), 2000);
                setTimeout(() => testRecycle(), 3000);
            }, 500);
        };
    </script>
</body>
</html>
