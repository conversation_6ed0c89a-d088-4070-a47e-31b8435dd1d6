/**
 * MVP数据库连接测试脚本
 * 目标：独立测试数据库连接，不依赖复杂的ORM配置
 */

const mysql = require('mysql2/promise');
const path = require('path');

console.log('🔍 开始MVP数据库连接测试...');
console.log('⏰ 测试时间:', new Date().toISOString());

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '.env') });

// 数据库配置
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASS || '',
    database: process.env.DB_NAME || 'case_manager',
    connectTimeout: 10000,
    acquireTimeout: 10000,
    timeout: 10000
};

console.log('📋 数据库配置:');
console.log('  Host:', dbConfig.host);
console.log('  Port:', dbConfig.port);
console.log('  User:', dbConfig.user);
console.log('  Password:', dbConfig.password ? '***' : '未设置');
console.log('  Database:', dbConfig.database);

async function testDatabaseConnection() {
    let connection = null;

    try {
        console.log('\n🔗 尝试连接数据库...');

        // 创建连接
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ 数据库连接成功!');

        // 测试基本查询
        console.log('\n📊 执行基本查询测试...');
        const [rows] = await connection.execute('SELECT 1 as test_value, NOW() as test_time');
        console.log('✅ 查询测试成功:', rows[0]);

        // 测试数据库是否存在
        console.log('\n🗄️ 检查数据库是否存在...');
        const [databases] = await connection.execute(`SHOW DATABASES LIKE '${dbConfig.database}'`);
        if (databases.length > 0) {
            console.log('✅ 数据库存在:', dbConfig.database);
        } else {
            console.log('⚠️ 数据库不存在，尝试创建...');
            await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbConfig.database}\``);
            console.log('✅ 数据库创建成功:', dbConfig.database);
        }

        // 切换到目标数据库
        await connection.query(`USE \`${dbConfig.database}\``);
        console.log('✅ 已切换到数据库:', dbConfig.database);

        // 测试表操作
        console.log('\n📋 测试表操作...');

        // 创建测试表
        const createTableSQL = `
            CREATE TABLE IF NOT EXISTS mvp_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                message VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `;
        await connection.execute(createTableSQL);
        console.log('✅ 测试表创建成功');

        // 插入测试数据
        const insertSQL = 'INSERT INTO mvp_test (message) VALUES (?)';
        const testMessage = `MVP测试 - ${new Date().toISOString()}`;
        const [insertResult] = await connection.execute(insertSQL, [testMessage]);
        console.log('✅ 测试数据插入成功, ID:', insertResult.insertId);

        // 查询测试数据
        const [testRows] = await connection.execute('SELECT * FROM mvp_test ORDER BY id DESC LIMIT 5');
        console.log('✅ 测试数据查询成功:');
        testRows.forEach(row => {
            console.log(`  ID: ${row.id}, Message: ${row.message}, Time: ${row.created_at}`);
        });

        // 清理测试数据（可选）
        await connection.execute('DELETE FROM mvp_test WHERE message LIKE "MVP测试%"');
        console.log('✅ 测试数据清理完成');

        console.log('\n🎉 数据库连接测试全部通过!');

        return {
            success: true,
            config: {
                host: dbConfig.host,
                port: dbConfig.port,
                database: dbConfig.database,
                user: dbConfig.user
            },
            testResults: {
                connection: true,
                basicQuery: true,
                databaseExists: true,
                tableOperations: true,
                insertData: true,
                selectData: true
            }
        };

    } catch (error) {
        console.error('\n❌ 数据库连接测试失败:');
        console.error('错误类型:', error.constructor.name);
        console.error('错误代码:', error.code);
        console.error('错误消息:', error.message);

        if (error.code === 'ECONNREFUSED') {
            console.error('\n💡 可能的解决方案:');
            console.error('1. 检查MySQL服务是否启动');
            console.error('2. 检查端口配置是否正确');
            console.error('3. 检查防火墙设置');
        } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.error('\n💡 可能的解决方案:');
            console.error('1. 检查用户名和密码是否正确');
            console.error('2. 检查用户是否有数据库访问权限');
        } else if (error.code === 'ER_BAD_DB_ERROR') {
            console.error('\n💡 可能的解决方案:');
            console.error('1. 数据库不存在，需要先创建');
            console.error('2. 检查数据库名称是否正确');
        }

        return {
            success: false,
            error: {
                code: error.code,
                message: error.message,
                type: error.constructor.name
            },
            config: dbConfig
        };

    } finally {
        if (connection) {
            try {
                await connection.end();
                console.log('🔌 数据库连接已关闭');
            } catch (closeError) {
                console.error('❌ 关闭连接时出错:', closeError.message);
            }
        }
    }
}

// 运行测试
testDatabaseConnection()
    .then(result => {
        console.log('\n📊 测试结果摘要:');
        console.log('成功:', result.success ? '✅' : '❌');
        if (result.success) {
            console.log('配置:', JSON.stringify(result.config, null, 2));
            console.log('测试项目:', JSON.stringify(result.testResults, null, 2));
        } else {
            console.log('错误:', JSON.stringify(result.error, null, 2));
        }
        process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
        console.error('\n❌ 测试脚本执行失败:', error);
        process.exit(1);
    });
