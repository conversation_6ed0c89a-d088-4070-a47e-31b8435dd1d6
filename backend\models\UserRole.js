const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const UserRole = sequelize.define('UserRole', {
    id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
    },
    user_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '用户ID',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    role_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '角色ID',
        references: {
            model: 'roles',
            key: 'id'
        }
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
    },
}, {
    tableName: 'user_roles',
    timestamps: false,
    indexes: [
        {
            unique: true,
            fields: ['user_id', 'role_id']
        },
        {
            fields: ['user_id']
        },
        {
            fields: ['role_id']
        }
    ]
});

module.exports = UserRole;
