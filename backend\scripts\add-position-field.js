const sequelize = require('../config/database');

async function addPositionField() {
    try {
        console.log('🔧 开始添加 position 字段到 responsibles 表...');
        
        // 检查字段是否已存在
        const [results] = await sequelize.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'responsibles' 
            AND COLUMN_NAME = 'position'
        `);
        
        if (results.length > 0) {
            console.log('✅ position 字段已存在，无需添加');
            return;
        }
        
        // 添加 position 字段
        await sequelize.query(`
            ALTER TABLE responsibles 
            ADD COLUMN position VARCHAR(100) NULL COMMENT '职位' 
            AFTER department
        `);
        
        console.log('✅ position 字段添加成功');
        
        // 为现有数据添加默认值
        await sequelize.query(`
            UPDATE responsibles 
            SET position = '法务专员' 
            WHERE position IS NULL
        `);
        
        console.log('✅ 现有数据已更新默认职位');
        
    } catch (error) {
        console.error('❌ 添加 position 字段失败:', error);
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    addPositionField()
        .then(() => {
            console.log('🎉 数据库迁移完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 数据库迁移失败:', error);
            process.exit(1);
        });
}

module.exports = addPositionField;
