# 依赖目录
node_modules/
*/node_modules/

# 构建产物
dist/
build/
*/dist/
*/build/

# 环境配置文件（敏感信息）
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
backend/.env
frontend/.env

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov/
coverage/
*.lcov
.nyc_output/

# 缓存目录
.cache/
.parcel-cache/
.npm/
.eslintcache
.stylelintcache

# 可选的npm缓存目录
.npm

# 可选的REPL历史
.node_repl_history

# 输出的npm包
*.tgz

# Yarn完整性文件
.yarn-integrity

# dotenv环境变量文件
.env
.env.test

# parcel-bundler缓存
.cache
.parcel-cache

# Next.js构建输出
.next

# Nuxt.js构建/生成输出
.nuxt
dist

# Gatsby文件
.cache/
public

# Storybook构建输出
.out
.storybook-out

# 临时文件夹
tmp/
temp/

# 上传文件目录
uploads/
backend/uploads/
frontend/uploads/

# 数据库文件
*.sqlite
*.sqlite3
*.db

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows图像文件缓存
Thumbs.db
ehthumbs.db

# 文件夹配置文件
Desktop.ini

# 回收站使用的文件
$RECYCLE.BIN/

# Windows快捷方式文件
*.lnk

# 测试结果文件
test-results.json
frontend-test-results.json

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.rar
*.7z
*.tar.gz

# 证书文件
*.pem
*.key
*.crt
*.cert

# 本地配置文件
config/local.js
config/local.json
