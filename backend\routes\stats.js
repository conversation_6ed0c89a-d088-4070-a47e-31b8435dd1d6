const express = require('express');
const router = express.Router();
const { Case, User, CaseFlow, CaseFile, Notify, Log } = require('../models');
const { authenticate, requireLawyer } = require('../middleware/auth');
const { Op } = require('sequelize');

/**
 * 获取统计总览
 * GET /api/stats/overview
 */
router.get('/overview', authenticate, async (req, res) => {
    try {
        const userId = req.user.id;
        const userRoles = req.user.roles.map(role => role.name);
        const isAdmin = userRoles.includes('admin');

        // 基础统计查询条件
        const caseWhere = isAdmin ? {} : { owner_id: userId };

        // 案件总数统计
        const totalCases = await Case.count({ where: caseWhere });

        // 按状态统计案件
        const casesByStatus = await Case.findAll({
            where: caseWhere,
            attributes: [
                'status',
                [Case.sequelize.fn('COUNT', Case.sequelize.col('id')), 'count']
            ],
            group: ['status'],
            raw: true
        });

        // 按类型统计案件
        const casesByType = await Case.findAll({
            where: caseWhere,
            attributes: [
                'type',
                [Case.sequelize.fn('COUNT', Case.sequelize.col('id')), 'count']
            ],
            group: ['type'],
            raw: true
        });

        // 按优先级统计案件
        const casesByPriority = await Case.findAll({
            where: caseWhere,
            attributes: [
                'priority',
                [Case.sequelize.fn('COUNT', Case.sequelize.col('id')), 'count']
            ],
            group: ['priority'],
            raw: true
        });

        // 本月新增案件
        const thisMonth = new Date();
        thisMonth.setDate(1);
        thisMonth.setHours(0, 0, 0, 0);

        const newCasesThisMonth = await Case.count({
            where: {
                ...caseWhere,
                created_at: {
                    [Op.gte]: thisMonth
                }
            }
        });

        // 即将到期案件（7天内）
        const upcomingDeadline = new Date();
        upcomingDeadline.setDate(upcomingDeadline.getDate() + 7);

        const upcomingCases = await Case.count({
            where: {
                ...caseWhere,
                deadline: {
                    [Op.between]: [new Date(), upcomingDeadline]
                },
                status: {
                    [Op.notIn]: ['已结案', '已归档', '已撤销']
                }
            }
        });

        // 未读通知数
        const unreadNotifications = await Notify.count({
            where: {
                user_id: userId,
                status: 0
            }
        });

        // 管理员额外统计
        let adminStats = {};
        if (isAdmin) {
            adminStats = {
                totalUsers: await User.count({ where: { status: 1 } }),
                totalFiles: await CaseFile.count(),
                totalNotifications: await Notify.count(),
                totalLogs: await Log.count()
            };
        }

        res.json({
            overview: {
                totalCases,
                newCasesThisMonth,
                upcomingCases,
                unreadNotifications,
                ...adminStats
            },
            casesByStatus: casesByStatus.reduce((acc, item) => {
                acc[item.status] = parseInt(item.count);
                return acc;
            }, {}),
            casesByType: casesByType.reduce((acc, item) => {
                acc[item.type] = parseInt(item.count);
                return acc;
            }, {}),
            casesByPriority: casesByPriority.reduce((acc, item) => {
                acc[item.priority] = parseInt(item.count);
                return acc;
            }, {})
        });

    } catch (error) {
        console.error('Get overview stats error:', error);
        res.status(500).json({
            error: 'Failed to get overview statistics',
            code: 'GET_OVERVIEW_ERROR'
        });
    }
});

/**
 * 获取案件统计详情
 * GET /api/stats/cases
 */
router.get('/cases', authenticate, requireLawyer, async (req, res) => {
    try {
        const {
            period = 'month', // month, quarter, year
            start_date,
            end_date
        } = req.query;

        const userId = req.user.id;
        const userRoles = req.user.roles.map(role => role.name);
        const isAdmin = userRoles.includes('admin');

        // 基础查询条件
        const where = isAdmin ? {} : { owner_id: userId };

        // 时间范围处理
        if (start_date && end_date) {
            where.created_at = {
                [Op.between]: [new Date(start_date), new Date(end_date)]
            };
        } else {
            const now = new Date();
            let startDate;

            switch (period) {
                case 'quarter':
                    startDate = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
                    break;
                case 'year':
                    startDate = new Date(now.getFullYear(), 0, 1);
                    break;
                default: // month
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            }

            where.created_at = {
                [Op.gte]: startDate
            };
        }

        // 按时间统计案件创建趋势
        const caseTrend = await Case.findAll({
            where,
            attributes: [
                [Case.sequelize.fn('DATE', Case.sequelize.col('created_at')), 'date'],
                [Case.sequelize.fn('COUNT', Case.sequelize.col('id')), 'count']
            ],
            group: [Case.sequelize.fn('DATE', Case.sequelize.col('created_at'))],
            order: [[Case.sequelize.fn('DATE', Case.sequelize.col('created_at')), 'ASC']],
            raw: true
        });

        // 案件处理时长统计
        const caseProcessingTime = await Case.findAll({
            where: {
                ...where,
                status: {
                    [Op.in]: ['已结案', '已归档']
                }
            },
            attributes: [
                'id',
                'created_at',
                'updated_at',
                [
                    Case.sequelize.fn(
                        'DATEDIFF',
                        Case.sequelize.col('updated_at'),
                        Case.sequelize.col('created_at')
                    ),
                    'processing_days'
                ]
            ],
            raw: true
        });

        // 负责人工作量统计（仅管理员可见）
        let ownerWorkload = [];
        if (isAdmin) {
            ownerWorkload = await Case.findAll({
                where,
                include: [{
                    model: User,
                    as: 'owner',
                    attributes: ['id', 'real_name']
                }],
                attributes: [
                    'owner_id',
                    [Case.sequelize.fn('COUNT', Case.sequelize.col('Case.id')), 'case_count']
                ],
                group: ['owner_id', 'owner.id'],
                raw: true
            });
        }

        // 案件金额统计
        const amountStats = await Case.findAll({
            where: {
                ...where,
                amount: {
                    [Op.not]: null
                }
            },
            attributes: [
                [Case.sequelize.fn('SUM', Case.sequelize.col('amount')), 'total_amount'],
                [Case.sequelize.fn('AVG', Case.sequelize.col('amount')), 'avg_amount'],
                [Case.sequelize.fn('MAX', Case.sequelize.col('amount')), 'max_amount'],
                [Case.sequelize.fn('MIN', Case.sequelize.col('amount')), 'min_amount'],
                [Case.sequelize.fn('COUNT', Case.sequelize.col('id')), 'count']
            ],
            raw: true
        });

        res.json({
            period,
            caseTrend: caseTrend.map(item => ({
                date: item.date,
                count: parseInt(item.count)
            })),
            processingTime: {
                avgDays: caseProcessingTime.length > 0
                    ? Math.round(caseProcessingTime.reduce((sum, item) => sum + parseInt(item.processing_days), 0) / caseProcessingTime.length)
                    : 0,
                cases: caseProcessingTime.map(item => ({
                    id: item.id,
                    days: parseInt(item.processing_days)
                }))
            },
            ownerWorkload: ownerWorkload.map(item => ({
                owner_id: item.owner_id,
                owner_name: item['owner.real_name'],
                case_count: parseInt(item.case_count)
            })),
            amountStats: amountStats[0] ? {
                total: parseFloat(amountStats[0].total_amount) || 0,
                average: parseFloat(amountStats[0].avg_amount) || 0,
                max: parseFloat(amountStats[0].max_amount) || 0,
                min: parseFloat(amountStats[0].min_amount) || 0,
                count: parseInt(amountStats[0].count) || 0
            } : {
                total: 0, average: 0, max: 0, min: 0, count: 0
            }
        });

    } catch (error) {
        console.error('Get case stats error:', error);
        res.status(500).json({
            error: 'Failed to get case statistics',
            code: 'GET_CASE_STATS_ERROR'
        });
    }
});

/**
 * 获取用户活动统计
 * GET /api/stats/activity
 */
router.get('/activity', authenticate, async (req, res) => {
    try {
        const {
            period = 'week', // week, month
            user_id
        } = req.query;

        const userId = user_id || req.user.id;
        const userRoles = req.user.roles.map(role => role.name);
        const isAdmin = userRoles.includes('admin');

        // 权限检查：非管理员只能查看自己的活动
        if (!isAdmin && userId != req.user.id) {
            return res.status(403).json({
                error: 'Access denied',
                code: 'ACCESS_DENIED'
            });
        }

        // 时间范围
        const now = new Date();
        const startDate = new Date();
        if (period === 'month') {
            startDate.setDate(now.getDate() - 30);
        } else {
            startDate.setDate(now.getDate() - 7);
        }

        // 用户操作日志统计
        const activityLogs = await Log.findAll({
            where: {
                user_id: userId,
                created_at: {
                    [Op.gte]: startDate
                }
            },
            attributes: [
                [Log.sequelize.fn('DATE', Log.sequelize.col('created_at')), 'date'],
                'action',
                [Log.sequelize.fn('COUNT', Log.sequelize.col('id')), 'count']
            ],
            group: [
                Log.sequelize.fn('DATE', Log.sequelize.col('created_at')),
                'action'
            ],
            order: [[Log.sequelize.fn('DATE', Log.sequelize.col('created_at')), 'DESC']],
            raw: true
        });

        // 按模块统计活动
        const moduleActivity = await Log.findAll({
            where: {
                user_id: userId,
                created_at: {
                    [Op.gte]: startDate
                }
            },
            attributes: [
                'module',
                [Log.sequelize.fn('COUNT', Log.sequelize.col('id')), 'count']
            ],
            group: ['module'],
            raw: true
        });

        // 最近案件操作
        const recentCaseActivity = await CaseFlow.findAll({
            where: {
                operator_id: userId,
                created_at: {
                    [Op.gte]: startDate
                }
            },
            include: [{
                model: Case,
                as: 'case',
                attributes: ['id', 'title', 'case_no']
            }],
            order: [['created_at', 'DESC']],
            limit: 10
        });

        res.json({
            period,
            user_id: userId,
            activityLogs: activityLogs.map(item => ({
                date: item.date,
                action: item.action,
                count: parseInt(item.count)
            })),
            moduleActivity: moduleActivity.reduce((acc, item) => {
                acc[item.module] = parseInt(item.count);
                return acc;
            }, {}),
            recentCaseActivity: recentCaseActivity.map(item => ({
                id: item.id,
                action: item.action,
                case_id: item.case_id,
                case_title: item.case.title,
                case_no: item.case.case_no,
                remark: item.remark,
                created_at: item.created_at
            }))
        });

    } catch (error) {
        console.error('Get activity stats error:', error);
        res.status(500).json({
            error: 'Failed to get activity statistics',
            code: 'GET_ACTIVITY_ERROR'
        });
    }
});

/**
 * 获取系统性能统计（管理员专用）
 * GET /api/stats/system
 */
router.get('/system', authenticate, async (req, res) => {
    try {
        const userRoles = req.user.roles.map(role => role.name);
        if (!userRoles.includes('admin')) {
            return res.status(403).json({
                error: 'Admin access required',
                code: 'ADMIN_REQUIRED'
            });
        }

        // 数据库表统计
        const tableStats = {
            users: await User.count(),
            cases: await Case.count(),
            case_flows: await CaseFlow.count(),
            case_files: await CaseFile.count(),
            notifications: await Notify.count(),
            logs: await Log.count()
        };

        // 最近7天的系统活动
        const last7Days = new Date();
        last7Days.setDate(last7Days.getDate() - 7);

        const recentActivity = await Log.findAll({
            where: {
                created_at: {
                    [Op.gte]: last7Days
                }
            },
            attributes: [
                [Log.sequelize.fn('DATE', Log.sequelize.col('created_at')), 'date'],
                [Log.sequelize.fn('COUNT', Log.sequelize.col('id')), 'count']
            ],
            group: [Log.sequelize.fn('DATE', Log.sequelize.col('created_at'))],
            order: [[Log.sequelize.fn('DATE', Log.sequelize.col('created_at')), 'ASC']],
            raw: true
        });

        // 错误日志统计
        const errorStats = await Log.findAll({
            where: {
                status: 'failed',
                created_at: {
                    [Op.gte]: last7Days
                }
            },
            attributes: [
                'module',
                [Log.sequelize.fn('COUNT', Log.sequelize.col('id')), 'count']
            ],
            group: ['module'],
            raw: true
        });

        // 文件存储统计
        const fileStats = await CaseFile.findAll({
            attributes: [
                [CaseFile.sequelize.fn('COUNT', CaseFile.sequelize.col('id')), 'total_files'],
                [CaseFile.sequelize.fn('SUM', CaseFile.sequelize.col('file_size')), 'total_size']
            ],
            raw: true
        });

        res.json({
            tableStats,
            recentActivity: recentActivity.map(item => ({
                date: item.date,
                count: parseInt(item.count)
            })),
            errorStats: errorStats.reduce((acc, item) => {
                acc[item.module] = parseInt(item.count);
                return acc;
            }, {}),
            fileStats: {
                totalFiles: parseInt(fileStats[0]?.total_files) || 0,
                totalSize: parseInt(fileStats[0]?.total_size) || 0,
                avgSize: fileStats[0]?.total_files > 0
                    ? Math.round(parseInt(fileStats[0].total_size) / parseInt(fileStats[0].total_files))
                    : 0
            }
        });

    } catch (error) {
        console.error('Get system stats error:', error);
        res.status(500).json({
            error: 'Failed to get system statistics',
            code: 'GET_SYSTEM_ERROR'
        });
    }
});

module.exports = router;
