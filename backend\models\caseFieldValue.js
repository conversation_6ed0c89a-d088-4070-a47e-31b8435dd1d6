const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const CaseFieldValue = sequelize.define('CaseFieldValue', {
    id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
    },
    case_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '案件ID',
    },
    field_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '字段定义ID',
    },
    value: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '字段值',
    },
}, {
    tableName: 'case_field_value',
    timestamps: false,
});

module.exports = CaseFieldValue; 