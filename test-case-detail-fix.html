<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案件详情修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .test-result.success {
            background-color: #f6ffed;
            border-color: #52c41a;
            color: #389e0d;
        }
        .test-result.error {
            background-color: #fff2f0;
            border-color: #ff4d4f;
            color: #cf1322;
        }
        .test-result.info {
            background-color: #e6f7ff;
            border-color: #1890ff;
            color: #0050b3;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        input {
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            margin: 5px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 案件详情修复测试</h1>
        <p>测试修复后的案件详情功能</p>
        
        <div>
            <h3>测试配置</h3>
            <label>认证Token: </label>
            <input type="text" id="authToken" placeholder="Bearer token" style="width: 400px;">
            <br>
            <button onclick="quickLogin()">快速登录获取Token</button>
            <button onclick="testExistingCase()">测试存在的案件</button>
            <button onclick="testNonExistingCase()">测试不存在的案件</button>
        </div>
    </div>

    <div class="container">
        <h3>🔐 登录测试</h3>
        <div id="loginResult" class="test-result info">点击"快速登录获取Token"按钮开始</div>
    </div>

    <div class="container">
        <h3>✅ 存在案件测试</h3>
        <div id="existingCaseResult" class="test-result info">点击"测试存在的案件"按钮开始</div>
    </div>

    <div class="container">
        <h3>❌ 不存在案件测试</h3>
        <div id="nonExistingCaseResult" class="test-result info">点击"测试不存在的案件"按钮开始</div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001/api';
        let authToken = '';

        // 快速登录获取Token
        async function quickLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = '正在登录...';

            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    authToken = data.data.token;
                    document.getElementById('authToken').value = authToken;
                    
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 登录成功<br>
                        用户: ${data.data.user.username}<br>
                        Token已自动填入
                    `;
                } else {
                    throw new Error(`登录失败: ${data.error || response.statusText}`);
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 登录失败: ${error.message}`;
            }
        }

        // 测试存在的案件
        async function testExistingCase() {
            const resultDiv = document.getElementById('existingCaseResult');
            authToken = document.getElementById('authToken').value;

            if (!authToken) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 请先登录获取Token';
                return;
            }

            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = '正在测试存在的案件...';

            try {
                // 首先获取案件列表，找到一个存在的案件ID
                const listResponse = await fetch(`${API_BASE}/cases?page=1&limit=1`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const listData = await listResponse.json();
                
                if (!listResponse.ok || !listData.success || listData.data.cases.length === 0) {
                    throw new Error('没有找到任何案件，请先创建案件');
                }

                const caseId = listData.data.cases[0].id;
                
                // 测试获取案件详情
                const detailResponse = await fetch(`${API_BASE}/cases/${caseId}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const detailData = await detailResponse.json();
                
                if (detailResponse.ok && detailData.success) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 存在案件测试成功<br>
                        案件ID: ${detailData.data.case.id}<br>
                        案件标题: ${detailData.data.case.title}<br>
                        案件状态: ${detailData.data.case.status}<br>
                        <pre>${JSON.stringify(detailData.data.case, null, 2).substring(0, 500)}...</pre>
                    `;
                } else {
                    throw new Error(`获取案件详情失败: ${detailData.error || detailResponse.statusText}`);
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 存在案件测试失败: ${error.message}`;
            }
        }

        // 测试不存在的案件
        async function testNonExistingCase() {
            const resultDiv = document.getElementById('nonExistingCaseResult');
            authToken = document.getElementById('authToken').value;

            if (!authToken) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 请先登录获取Token';
                return;
            }

            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = '正在测试不存在的案件...';

            try {
                // 使用一个不太可能存在的案件ID
                const nonExistingId = 99999;
                
                const response = await fetch(`${API_BASE}/cases/${nonExistingId}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.status === 404 && data.code === 'CASE_NOT_FOUND') {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 不存在案件测试成功<br>
                        正确返回404错误<br>
                        错误代码: ${data.code}<br>
                        错误信息: ${data.error}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `
                        ❌ 不存在案件测试失败<br>
                        期望404错误，但得到: ${response.status}<br>
                        响应数据: <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 不存在案件测试失败: ${error.message}`;
            }
        }
    </script>
</body>
</html>
