const express = require('express');
const router = express.Router();
const { Responsible, User } = require('../models');
const { authenticate, requireAdmin } = require('../middleware/auth');
const { Op } = require('sequelize');

/**
 * 获取负责人列表
 * GET /api/responsibles
 */
router.get('/', authenticate, async (req, res) => {
    try {
        const {
            page = 1,
            limit = 50,
            status,
            search,
            sort_by = 'sort_order',
            sort_order = 'ASC'
        } = req.query;

        const offset = (page - 1) * limit;
        const where = {};

        // 状态过滤
        if (status !== undefined) {
            where.status = parseInt(status);
        }

        // 搜索过滤
        if (search) {
            where[Op.or] = [
                { name: { [Op.like]: `%${search}%` } },
                { email: { [Op.like]: `%${search}%` } },
                { department: { [Op.like]: `%${search}%` } }
            ];
        }

        const { count, rows } = await Responsible.findAndCountAll({
            where,
            include: [
                {
                    model: User,
                    as: 'creator',
                    attributes: ['id', 'username', 'real_name'],
                    required: false
                },
                {
                    model: User,
                    as: 'updater',
                    attributes: ['id', 'username', 'real_name'],
                    required: false
                }
            ],
            order: [[sort_by, sort_order.toUpperCase()]],
            limit: parseInt(limit),
            offset: parseInt(offset)
        });

        res.json({
            responsibles: rows,
            pagination: {
                total: count,
                page: parseInt(page),
                limit: parseInt(limit),
                pages: Math.ceil(count / limit)
            }
        });

    } catch (error) {
        console.error('Get responsibles error:', error);
        res.status(500).json({
            error: 'Failed to get responsibles',
            code: 'GET_RESPONSIBLES_ERROR'
        });
    }
});

/**
 * 获取启用的负责人列表（用于下拉选择）
 * GET /api/responsibles/active
 */
router.get('/active', authenticate, async (req, res) => {
    try {
        const responsibles = await Responsible.findAll({
            where: { status: 1 },
            attributes: ['id', 'name', 'email', 'department'],
            order: [['sort_order', 'ASC'], ['name', 'ASC']]
        });

        res.json({
            responsibles
        });

    } catch (error) {
        console.error('Get active responsibles error:', error);
        res.status(500).json({
            error: 'Failed to get active responsibles',
            code: 'GET_ACTIVE_RESPONSIBLES_ERROR'
        });
    }
});

/**
 * 获取负责人详情
 * GET /api/responsibles/:id
 */
router.get('/:id', authenticate, async (req, res) => {
    try {
        const responsible = await Responsible.findByPk(req.params.id, {
            include: [
                {
                    model: User,
                    as: 'creator',
                    attributes: ['id', 'username', 'real_name'],
                    required: false
                },
                {
                    model: User,
                    as: 'updater',
                    attributes: ['id', 'username', 'real_name'],
                    required: false
                }
            ]
        });

        if (!responsible) {
            return res.status(404).json({
                error: 'Responsible not found',
                code: 'RESPONSIBLE_NOT_FOUND'
            });
        }

        res.json({ responsible });

    } catch (error) {
        console.error('Get responsible detail error:', error);
        res.status(500).json({
            error: 'Failed to get responsible detail',
            code: 'GET_RESPONSIBLE_ERROR'
        });
    }
});

/**
 * 创建负责人（管理员专用）
 * POST /api/responsibles
 */
router.post('/', authenticate, requireAdmin, async (req, res) => {
    try {
        const {
            name,
            email,
            phone,
            department,
            status = 1,
            sort_order = 0
        } = req.body;

        // 验证必填字段
        if (!name) {
            return res.status(400).json({
                error: 'Name is required',
                code: 'MISSING_FIELDS'
            });
        }

        // 检查邮箱是否已存在
        if (email) {
            const existingEmail = await Responsible.findOne({ where: { email } });
            if (existingEmail) {
                return res.status(409).json({
                    error: 'Email already exists',
                    code: 'EMAIL_EXISTS'
                });
            }
        }

        // 创建负责人
        const responsible = await Responsible.create({
            name,
            email,
            phone,
            department,
            status,
            sort_order,
            created_by: req.user.id,
            updated_by: req.user.id
        });

        // 获取完整信息返回
        const createdResponsible = await Responsible.findByPk(responsible.id, {
            include: [
                {
                    model: User,
                    as: 'creator',
                    attributes: ['id', 'username', 'real_name'],
                    required: false
                }
            ]
        });

        res.status(201).json({
            responsible: createdResponsible,
            message: 'Responsible created successfully'
        });

    } catch (error) {
        console.error('Create responsible error:', error);
        res.status(500).json({
            error: 'Failed to create responsible',
            code: 'CREATE_RESPONSIBLE_ERROR'
        });
    }
});

/**
 * 更新负责人信息（管理员专用）
 * PUT /api/responsibles/:id
 */
router.put('/:id', authenticate, requireAdmin, async (req, res) => {
    try {
        const responsible = await Responsible.findByPk(req.params.id);
        if (!responsible) {
            return res.status(404).json({
                error: 'Responsible not found',
                code: 'RESPONSIBLE_NOT_FOUND'
            });
        }

        const {
            name,
            email,
            phone,
            department,
            status,
            sort_order
        } = req.body;

        // 检查邮箱是否已被其他负责人使用
        if (email && email !== responsible.email) {
            const existingEmail = await Responsible.findOne({
                where: {
                    email,
                    id: { [Op.ne]: responsible.id }
                }
            });
            if (existingEmail) {
                return res.status(409).json({
                    error: 'Email already exists',
                    code: 'EMAIL_EXISTS'
                });
            }
        }

        // 更新负责人信息
        await responsible.update({
            name: name || responsible.name,
            email: email !== undefined ? email : responsible.email,
            phone: phone !== undefined ? phone : responsible.phone,
            department: department !== undefined ? department : responsible.department,
            status: status !== undefined ? status : responsible.status,
            sort_order: sort_order !== undefined ? sort_order : responsible.sort_order,
            updated_by: req.user.id
        });

        // 获取更新后的完整信息
        const updatedResponsible = await Responsible.findByPk(responsible.id, {
            include: [
                {
                    model: User,
                    as: 'creator',
                    attributes: ['id', 'username', 'real_name'],
                    required: false
                },
                {
                    model: User,
                    as: 'updater',
                    attributes: ['id', 'username', 'real_name'],
                    required: false
                }
            ]
        });

        res.json({
            responsible: updatedResponsible,
            message: 'Responsible updated successfully'
        });

    } catch (error) {
        console.error('Update responsible error:', error);
        res.status(500).json({
            error: 'Failed to update responsible',
            code: 'UPDATE_RESPONSIBLE_ERROR'
        });
    }
});

/**
 * 删除负责人（管理员专用）
 * DELETE /api/responsibles/:id
 */
router.delete('/:id', authenticate, requireAdmin, async (req, res) => {
    try {
        const responsible = await Responsible.findByPk(req.params.id);
        if (!responsible) {
            return res.status(404).json({
                error: 'Responsible not found',
                code: 'RESPONSIBLE_NOT_FOUND'
            });
        }

        // 检查是否有案件正在使用此负责人
        // 注意：这里需要根据实际的案件表结构来检查
        // 如果案件表中有responsible_id字段，需要检查是否有关联案件

        await responsible.destroy();

        res.json({
            message: 'Responsible deleted successfully'
        });

    } catch (error) {
        console.error('Delete responsible error:', error);
        res.status(500).json({
            error: 'Failed to delete responsible',
            code: 'DELETE_RESPONSIBLE_ERROR'
        });
    }
});

/**
 * 批量更新负责人排序（管理员专用）
 * PUT /api/responsibles/batch/sort
 */
router.put('/batch/sort', authenticate, requireAdmin, async (req, res) => {
    try {
        const { responsibles } = req.body;

        if (!Array.isArray(responsibles)) {
            return res.status(400).json({
                error: 'Responsibles array is required',
                code: 'INVALID_DATA'
            });
        }

        // 批量更新排序
        const updatePromises = responsibles.map(item => {
            return Responsible.update(
                {
                    sort_order: item.sort_order,
                    updated_by: req.user.id
                },
                { where: { id: item.id } }
            );
        });

        await Promise.all(updatePromises);

        res.json({
            message: 'Responsible sort order updated successfully'
        });

    } catch (error) {
        console.error('Batch update sort error:', error);
        res.status(500).json({
            error: 'Failed to update sort order',
            code: 'BATCH_UPDATE_ERROR'
        });
    }
});

module.exports = router;
