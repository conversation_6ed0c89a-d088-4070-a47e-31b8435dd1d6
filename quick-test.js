const axios = require('axios');

async function quickTest() {
    try {
        console.log('🧪 快速测试案件创建...');
        
        // 1. 登录
        const loginResponse = await axios.post('http://127.0.0.1:3001/api/auth/login', {
            username: 'admin',
            password: 'admin123'
        });
        
        console.log('✅ 登录成功');
        const token = loginResponse.data.token;
        
        // 2. 创建案件
        const caseResponse = await axios.post('http://127.0.0.1:3001/api/cases', {
            title: '快速测试案件',
            type: '合同纠纷',
            description: '测试案件',
            priority: '高',
            owner_id: 1
        }, {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        console.log('✅ 案件创建成功:', caseResponse.data);
        
        // 3. 获取案件列表
        const listResponse = await axios.get('http://127.0.0.1:3001/api/cases', {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        console.log('✅ 案件列表:', listResponse.data);
        
        // 4. 测试分配API
        if (caseResponse.data.data && caseResponse.data.data.case) {
            const caseId = caseResponse.data.data.case.id;
            console.log('🔄 测试案件分配，案件ID:', caseId);
            
            const assignResponse = await axios.post(`http://127.0.0.1:3001/api/cases/${caseId}/assign`, {
                owner_id: 2,
                remark: '测试分配'
            }, {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            
            console.log('✅ 案件分配成功:', assignResponse.data);
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('   状态:', error.response.status);
            console.error('   数据:', error.response.data);
        }
    }
}

quickTest();
