const http = require('http');

function makeRequest(options, data) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    body: body
                });
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(data);
        }
        req.end();
    });
}

async function login() {
    console.log('🔐 登录获取token...');
    
    const loginData = JSON.stringify({
        username: 'admin',
        password: 'admin123'
    });

    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(loginData)
        }
    };

    try {
        const response = await makeRequest(options, loginData);
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 登录成功');
            return data.token;
        } else {
            console.log('❌ 登录失败');
            return null;
        }
    } catch (error) {
        console.error('❌ 登录错误:', error.message);
        return null;
    }
}

async function testCreateSpecificNotification(token) {
    console.log('\n📢 测试创建指定用户通知...');
    
    const notificationData = JSON.stringify({
        user_id: 1, // 指定给admin用户
        title: '测试通知',
        content: '这是一个测试通知消息',
        type: '系统通知'
    });

    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/notifications',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(notificationData),
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options, notificationData);
        console.log('状态码:', response.statusCode);
        console.log('响应:', response.body);
        
        if (response.statusCode === 201) {
            const data = JSON.parse(response.body);
            console.log('✅ 指定用户通知创建成功');
            console.log('通知数量:', data.notifications_count);
        } else {
            console.log('❌ 指定用户通知创建失败');
        }
    } catch (error) {
        console.error('❌ 创建指定用户通知错误:', error.message);
    }
}

async function main() {
    console.log('🧪 开始简单通知测试...\n');
    
    const token = await login();
    if (!token) {
        console.log('❌ 无法获取token，测试终止');
        return;
    }
    
    await testCreateSpecificNotification(token);
    
    console.log('\n✅ 简单通知测试完成');
}

main();
