const models = require('../models');
const { User, Role, UserRole, Case, CaseFieldDef } = models;

async function initDatabase() {
    try {
        console.log('🔄 开始初始化数据库...');
        
        // 强制同步数据库（开发环境）
        await models.sequelize.sync({ force: true });
        console.log('✅ 数据库表结构同步完成');
        
        // 创建默认角色
        const roles = await Role.bulkCreate([
            { name: 'admin', description: '系统管理员' },
            { name: 'lawyer', description: '法务人员' },
            { name: 'client', description: '客户' },
            { name: 'assistant', description: '助理' }
        ]);
        console.log('✅ 默认角色创建完成');
        
        // 创建默认管理员用户
        const adminUser = await User.create({
            username: 'admin',
            password: 'admin123',
            real_name: '系统管理员',
            email: '<EMAIL>',
            status: 1
        });
        console.log('✅ 默认管理员用户创建完成');
        
        // 分配管理员角色
        await UserRole.create({
            user_id: adminUser.id,
            role_id: roles[0].id // admin role
        });
        console.log('✅ 管理员角色分配完成');
        
        // 创建测试法务用户
        const lawyerUser = await User.create({
            username: 'lawyer1',
            password: 'lawyer123',
            real_name: '张法务',
            email: '<EMAIL>',
            status: 1
        });
        
        await UserRole.create({
            user_id: lawyerUser.id,
            role_id: roles[1].id // lawyer role
        });
        console.log('✅ 测试法务用户创建完成');
        
        // 创建默认案件字段定义
        const fieldDefs = await CaseFieldDef.bulkCreate([
            {
                name: 'contract_type',
                label: '合同类型',
                type: 'select',
                required: true,
                options: JSON.stringify(['销售合同', '采购合同', '服务合同', '租赁合同', '其他']),
                order: 1
            },
            {
                name: 'dispute_reason',
                label: '纠纷原因',
                type: 'textarea',
                required: true,
                options: null,
                order: 2
            },
            {
                name: 'evidence_status',
                label: '证据状态',
                type: 'select',
                required: false,
                options: JSON.stringify(['充分', '一般', '不足', '待收集']),
                order: 3
            }
        ]);
        console.log('✅ 默认案件字段定义创建完成');
        
        // 创建示例案件
        const sampleCase = await Case.create({
            title: '某公司合同纠纷案',
            case_no: Case.generateCaseNo(),
            type: '合同纠纷',
            status: '处理中',
            description: '因合同履行问题产生的纠纷，涉及货款支付延迟等问题。',
            owner_id: lawyerUser.id,
            priority: '高',
            deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后
            amount: 500000.00,
            client_name: '某科技有限公司',
            client_contact: '13800138000'
        });
        console.log('✅ 示例案件创建完成');
        
        console.log('\n🎉 数据库初始化完成！');
        console.log('\n📋 默认账户信息：');
        console.log('管理员账户: admin / admin123');
        console.log('法务账户: lawyer1 / lawyer123');
        console.log(`示例案件编号: ${sampleCase.case_no}`);
        
    } catch (error) {
        console.error('❌ 数据库初始化失败:', error);
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    initDatabase()
        .then(() => {
            console.log('✅ 初始化脚本执行完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ 初始化脚本执行失败:', error);
            process.exit(1);
        });
}

module.exports = initDatabase;
