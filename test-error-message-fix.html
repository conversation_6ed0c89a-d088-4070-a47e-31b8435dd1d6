<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误消息修复验证</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .test-button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background-color: #40a9ff;
        }
        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .summary {
            background-color: #f0f0f0;
            padding: 20px;
            border-radius: 6px;
            margin-top: 30px;
        }
        .fix-info {
            background-color: #fff7e6;
            border: 1px solid #ffd591;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 错误消息修复验证</h1>
        
        <div class="fix-info">
            <h3>🎯 修复内容</h3>
            <p><strong>问题：</strong>前端错误处理器在处理404错误时，硬编码显示"请求的资源不存在"，而不是后端返回的具体错误消息"案件不存在或已被删除"。</p>
            <p><strong>修复：</strong>修改 errorHandler.jsx 中的404错误处理逻辑，优先使用后端返回的 data.error 或 data.message。</p>
            <p><strong>修复位置：</strong>frontend/src/utils/errorHandler.jsx 第107行</p>
        </div>

        <div class="test-section">
            <h3>🧪 测试1：验证后端错误消息</h3>
            <p>测试后端是否正确返回中文错误消息</p>
            <button class="test-button" onclick="testBackendErrorMessage()">测试后端错误消息</button>
            <div id="backend-test-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>🧪 测试2：验证前端错误处理</h3>
            <p>模拟前端错误处理器处理404错误的逻辑</p>
            <button class="test-button" onclick="testFrontendErrorHandler()">测试前端错误处理</button>
            <div id="frontend-test-result" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>🧪 测试3：端到端测试</h3>
            <p>完整测试从API调用到错误显示的整个流程</p>
            <button class="test-button" onclick="testEndToEnd()">端到端测试</button>
            <div id="e2e-test-result" class="test-result"></div>
        </div>

        <div class="summary" id="test-summary">
            <h3>📊 测试总结</h3>
            <p>点击上方按钮开始测试...</p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8001/api';
        let testResults = {
            backend: false,
            frontend: false,
            e2e: false
        };

        // 模拟前端错误处理器的逻辑（修复后）
        function parseError(error) {
            let errorInfo = {
                type: 'UNKNOWN_ERROR',
                message: '发生未知错误',
                code: null,
                status: null,
                retryable: false,
            };

            if (error.response) {
                const { status, data } = error.response;
                errorInfo.status = status;
                errorInfo.code = data?.code || status;

                switch (status) {
                    case 404:
                        errorInfo.type = 'NOT_FOUND';
                        // 修复后：优先使用后端返回的错误消息
                        errorInfo.message = data?.error || data?.message || '请求的资源不存在';
                        break;
                    default:
                        errorInfo.message = data?.error || data?.message || '未知错误';
                }
            }

            return errorInfo;
        }

        // 测试1：验证后端错误消息
        async function testBackendErrorMessage() {
            const resultDiv = document.getElementById('backend-test-result');
            resultDiv.className = 'test-result info';
            resultDiv.textContent = '正在测试后端错误消息...';

            try {
                const response = await fetch(`${API_BASE}/cases/999`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token') || 'test-token'}`
                    }
                });

                const data = await response.json();
                
                if (response.status === 404 && data.error === '案件不存在或已被删除') {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = `✅ 后端错误消息正确\n状态码: ${response.status}\n错误消息: "${data.error}"\n错误代码: ${data.code}`;
                    testResults.backend = true;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.textContent = `❌ 后端错误消息不正确\n状态码: ${response.status}\n实际消息: "${data.error || data.message}"\n期望消息: "案件不存在或已被删除"`;
                    testResults.backend = false;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `❌ 测试失败: ${error.message}`;
                testResults.backend = false;
            }
            updateSummary();
        }

        // 测试2：验证前端错误处理
        function testFrontendErrorHandler() {
            const resultDiv = document.getElementById('frontend-test-result');
            resultDiv.className = 'test-result info';
            resultDiv.textContent = '正在测试前端错误处理...';

            try {
                // 模拟404错误响应
                const mockError = {
                    response: {
                        status: 404,
                        data: {
                            success: false,
                            error: '案件不存在或已被删除',
                            code: 'CASE_NOT_FOUND'
                        }
                    }
                };

                const errorInfo = parseError(mockError);
                
                if (errorInfo.message === '案件不存在或已被删除') {
                    resultDiv.className = 'test-result success';
                    resultDiv.textContent = `✅ 前端错误处理正确\n错误类型: ${errorInfo.type}\n错误消息: "${errorInfo.message}"\n状态码: ${errorInfo.status}`;
                    testResults.frontend = true;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.textContent = `❌ 前端错误处理不正确\n实际消息: "${errorInfo.message}"\n期望消息: "案件不存在或已被删除"`;
                    testResults.frontend = false;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `❌ 测试失败: ${error.message}`;
                testResults.frontend = false;
            }
            updateSummary();
        }

        // 测试3：端到端测试
        async function testEndToEnd() {
            const resultDiv = document.getElementById('e2e-test-result');
            resultDiv.className = 'test-result info';
            resultDiv.textContent = '正在进行端到端测试...';

            try {
                // 模拟完整的API调用和错误处理流程
                const response = await fetch(`${API_BASE}/cases/999`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token') || 'test-token'}`
                    }
                });

                if (!response.ok) {
                    const data = await response.json();
                    
                    // 模拟前端错误处理
                    const mockError = {
                        response: {
                            status: response.status,
                            data: data
                        }
                    };
                    
                    const errorInfo = parseError(mockError);
                    
                    if (errorInfo.message === '案件不存在或已被删除') {
                        resultDiv.className = 'test-result success';
                        resultDiv.textContent = `✅ 端到端测试成功\n完整流程验证通过\n最终显示消息: "${errorInfo.message}"`;
                        testResults.e2e = true;
                    } else {
                        resultDiv.className = 'test-result error';
                        resultDiv.textContent = `❌ 端到端测试失败\n最终消息: "${errorInfo.message}"\n期望消息: "案件不存在或已被删除"`;
                        testResults.e2e = false;
                    }
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.textContent = `❌ 端到端测试失败\n期望404错误，但收到: ${response.status}`;
                    testResults.e2e = false;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `❌ 端到端测试失败: ${error.message}`;
                testResults.e2e = false;
            }
            updateSummary();
        }

        // 更新测试总结
        function updateSummary() {
            const summaryDiv = document.getElementById('test-summary');
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(result => result).length;
            
            let summaryHTML = `<h3>📊 测试总结</h3>`;
            summaryHTML += `<p><strong>总测试数:</strong> ${totalTests}</p>`;
            summaryHTML += `<p><strong>通过测试:</strong> ${passedTests}</p>`;
            summaryHTML += `<p><strong>失败测试:</strong> ${totalTests - passedTests}</p>`;
            
            summaryHTML += `<h4>详细结果:</h4>`;
            summaryHTML += `<ul>`;
            summaryHTML += `<li>后端错误消息: ${testResults.backend ? '✅ 通过' : '❌ 失败'}</li>`;
            summaryHTML += `<li>前端错误处理: ${testResults.frontend ? '✅ 通过' : '❌ 失败'}</li>`;
            summaryHTML += `<li>端到端测试: ${testResults.e2e ? '✅ 通过' : '❌ 失败'}</li>`;
            summaryHTML += `</ul>`;
            
            if (passedTests === totalTests) {
                summaryHTML += `<p style="color: #52c41a; font-weight: bold;">🎉 所有测试通过！错误消息修复成功！</p>`;
            } else {
                summaryHTML += `<p style="color: #ff4d4f; font-weight: bold;">⚠️ 还有测试未通过，需要进一步检查。</p>`;
            }
            
            summaryDiv.innerHTML = summaryHTML;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('错误消息修复验证页面已加载');
        });
    </script>
</body>
</html>
