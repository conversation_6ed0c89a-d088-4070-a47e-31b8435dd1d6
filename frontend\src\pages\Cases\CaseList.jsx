import React, { useState, useEffect } from 'react';
import {
  Typography,
  Table,
  Card,
  Button,
  Space,
  Tag,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  message,
  Spin,
  Tooltip,
  Popconfirm,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  FilterOutlined,
  UndoOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';

import { casesAPI, CASE_CONSTANTS } from '../../services/cases';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const CaseList = () => {
  const navigate = useNavigate();

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [cases, setCases] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  });

  // 搜索和过滤状态
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    type: '',
    priority: '',
    dateRange: null,
    sort_by: 'created_at',
    sort_order: 'desc',
  });

  // 获取案件列表数据
  const fetchCases = async (params = {}) => {
    setLoading(true);
    try {
      const queryParams = {
        page: pagination.current,
        limit: pagination.pageSize,
        ...filters,
        ...params,
      };

      // 处理日期范围
      if (queryParams.dateRange && queryParams.dateRange.length === 2) {
        queryParams.start_date = queryParams.dateRange[0].format('YYYY-MM-DD');
        queryParams.end_date = queryParams.dateRange[1].format('YYYY-MM-DD');
        delete queryParams.dateRange;
      }

      // 移除空值
      Object.keys(queryParams).forEach(key => {
        if (queryParams[key] === '' || queryParams[key] === null || queryParams[key] === undefined) {
          delete queryParams[key];
        }
      });

      const response = await casesAPI.getCases(queryParams);

      // response是axios响应，实际数据在response.data中
      const responseData = response.data;

      if (responseData && responseData.success) {
        // 处理后端返回的数据格式
        // 后端返回: { success: true, data: [...], pagination: {...} }
        const casesData = Array.isArray(responseData.data) ? responseData.data : [];
        setCases(casesData);

        if (responseData.pagination) {
          setPagination(prev => ({
            ...prev,
            total: responseData.pagination.total || 0,
            current: responseData.pagination.page || 1,
          }));
        }
      } else {
        console.warn('⚠️ 响应格式异常:', responseData);
        // 如果没有数据，设置为空数组
        setCases([]);
      }
    } catch (error) {
      console.error('❌ 获取案件列表失败:', error);
      console.error('❌ 错误详情:', error.response || error.message);
      message.error('获取案件列表失败，请稍后重试');
      // 确保在错误情况下也设置空数组
      setCases([]);
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载数据
  useEffect(() => {
    fetchCases();
  }, []);

  // 搜索处理
  const handleSearch = (value) => {
    const newFilters = { ...filters, search: value };
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchCases({ ...newFilters, page: 1 });
  };

  // 过滤器变化处理
  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchCases({ ...newFilters, page: 1 });
  };

  // 表格分页、排序、过滤变化处理
  const handleTableChange = (paginationConfig, filters, sorter) => {
    const newPagination = {
      ...pagination,
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
    };

    const newFilters = { ...filters };

    // 处理排序
    if (sorter.field) {
      newFilters.sort_by = sorter.field;
      newFilters.sort_order = sorter.order === 'ascend' ? 'asc' : 'desc';
    }

    setPagination(newPagination);
    setFilters(newFilters);

    fetchCases({
      ...newFilters,
      page: newPagination.current,
      limit: newPagination.pageSize,
    });
  };

  // 删除案件
  const handleDelete = async (id) => {
    try {
      await casesAPI.deleteCase(id);
      message.success('案件删除成功');
      fetchCases(); // 重新加载数据
    } catch (error) {
      console.error('删除案件失败:', error);
      message.error('删除案件失败，请稍后重试');
    }
  };

  // 重置过滤器
  const handleReset = () => {
    const resetFilters = {
      search: '',
      status: '',
      type: '',
      priority: '',
      dateRange: null,
      sort_by: 'created_at',
      sort_order: 'desc',
    };
    setFilters(resetFilters);
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchCases({ ...resetFilters, page: 1 });
  };

  // 表格列定义
  const columns = [
    {
      title: '案件编号',
      dataIndex: 'case_no',
      key: 'case_no',
      width: 160,
      fixed: 'left',
      ellipsis: {
        showTitle: true,
      },
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => navigate(`/cases/${record.id}`)}
          style={{ padding: 0, height: 'auto', textAlign: 'left' }}
          title={text}
        >
          {text}
        </Button>
      ),
    },
    {
      title: '案件标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          {text}
        </Tooltip>
      ),
    },
    {
      title: '案件类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type) => (
        <Tag color="blue">{type}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Tag color={CASE_CONSTANTS.STATUS_COLORS[status] || 'default'}>
          {status}
        </Tag>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority) => (
        <Tag color={CASE_CONSTANTS.PRIORITY_COLORS[priority] || 'default'}>
          {priority}
        </Tag>
      ),
    },
    {
      title: '负责人',
      dataIndex: ['owner', 'real_name'],
      key: 'owner',
      width: 100,
      render: (text, record) => text || record.owner?.username || '-',
    },
    {
      title: '客户名称',
      dataIndex: 'client_name',
      key: 'client_name',
      width: 120,
      render: (text) => text || '-',
    },
    {
      title: '涉案金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      align: 'right',
      render: (amount) => {
        if (!amount) return '-';
        return `¥${Number(amount).toLocaleString()}`;
      },
    },
    {
      title: '截止日期',
      dataIndex: 'deadline',
      key: 'deadline',
      width: 100,
      render: (date) => {
        if (!date) return '-';
        const deadline = dayjs(date);
        const now = dayjs();
        const isOverdue = deadline.isBefore(now, 'day');
        const isNearDeadline = deadline.diff(now, 'day') <= 3 && deadline.isAfter(now, 'day');

        return (
          <span style={{
            color: isOverdue ? '#f5222d' : isNearDeadline ? '#faad14' : undefined
          }}>
            {deadline.format('MM-DD')}
          </span>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 100,
      sorter: true,
      render: (date) => dayjs(date).format('MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/cases/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="编辑案件">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => navigate(`/cases/${record.id}/edit`)}
            />
          </Tooltip>
          <Tooltip title="删除案件">
            <Popconfirm
              title="确定要删除这个案件吗？"
              description="案件将移入回收站，30天后自动永久删除。"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>


      {/* 页面标题和操作按钮 */}
      <div style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={2} style={{ margin: 0 }}>
              案件列表
            </Title>
          </Col>
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => navigate('/cases/create')}
              >
                创建案件
              </Button>
              <Button
                icon={<UndoOutlined />}
                onClick={() => navigate('/cases/recycle')}
              >
                回收站
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchCases()}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* 搜索和过滤器 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Search
              placeholder="搜索案件标题、编号、客户名称"
              allowClear
              enterButton={<SearchOutlined />}
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              onSearch={handleSearch}
            />
          </Col>

          <Col xs={12} sm={6} md={4} lg={3}>
            <Select
              placeholder="状态"
              allowClear
              style={{ width: '100%' }}
              value={filters.status}
              onChange={(value) => handleFilterChange('status', value)}
            >
              {Object.entries(CASE_CONSTANTS.STATUS).map(([key, value]) => (
                <Option key={key} value={value}>{value}</Option>
              ))}
            </Select>
          </Col>

          <Col xs={12} sm={6} md={4} lg={3}>
            <Select
              placeholder="类型"
              allowClear
              style={{ width: '100%' }}
              value={filters.type}
              onChange={(value) => handleFilterChange('type', value)}
            >
              {Object.entries(CASE_CONSTANTS.TYPES).map(([key, value]) => (
                <Option key={key} value={value}>{value}</Option>
              ))}
            </Select>
          </Col>

          <Col xs={12} sm={6} md={4} lg={3}>
            <Select
              placeholder="优先级"
              allowClear
              style={{ width: '100%' }}
              value={filters.priority}
              onChange={(value) => handleFilterChange('priority', value)}
            >
              {Object.entries(CASE_CONSTANTS.PRIORITIES).map(([key, value]) => (
                <Option key={key} value={value}>{value}</Option>
              ))}
            </Select>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              style={{ width: '100%' }}
              value={filters.dateRange}
              onChange={(dates) => handleFilterChange('dateRange', dates)}
            />
          </Col>

          <Col xs={24} sm={12} md={4} lg={3}>
            <Button
              icon={<FilterOutlined />}
              onClick={handleReset}
              style={{ width: '100%' }}
            >
              重置
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 案件列表表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={cases}
          rowKey="id"
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
          size="small"
          bordered
        />
      </Card>
    </div>
  );
};

export default CaseList;
