/**
 * 负责人API接口测试脚本
 * 用于验证负责人相关API接口是否正常工作
 */

const axios = require('axios');

const BASE_URL = 'http://127.0.0.1:8001';

async function testResponsiblesAPI() {
    try {
        console.log('🧪 开始测试负责人API接口...');
        console.log('⏰ 测试时间:', new Date().toISOString());
        console.log('');

        // 1. 测试管理员登录
        console.log('1️⃣ 测试管理员登录...');
        const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
            username: 'admin',
            password: 'admin123'
        });

        if (loginResponse.data.success) {
            console.log('✅ 管理员登录成功');
            const token = loginResponse.data.token;
            const authHeaders = { Authorization: `Bearer ${token}` };
            console.log('');

            // 2. 测试获取启用的负责人列表
            console.log('2️⃣ 测试获取启用的负责人列表...');
            try {
                const activeResponse = await axios.get(`${BASE_URL}/api/responsibles/active`, {
                    headers: authHeaders
                });

                if (activeResponse.data.success) {
                    console.log('✅ 获取启用负责人列表成功');
                    console.log('   响应格式:', Object.keys(activeResponse.data));
                    if (activeResponse.data.data && activeResponse.data.data.responsibles) {
                        console.log('   负责人数量:', activeResponse.data.data.responsibles.length);
                        if (activeResponse.data.data.responsibles.length > 0) {
                            console.log('   第一个负责人:', activeResponse.data.data.responsibles[0]);
                        }
                    }
                } else {
                    console.log('❌ 获取启用负责人列表失败');
                    console.log('   错误信息:', activeResponse.data.error);
                }
            } catch (error) {
                console.log('❌ 获取启用负责人列表请求失败');
                console.log('   错误状态:', error.response?.status);
                console.log('   错误信息:', error.response?.data || error.message);
            }
            console.log('');

            // 3. 测试获取完整负责人列表
            console.log('3️⃣ 测试获取完整负责人列表...');
            try {
                const allResponse = await axios.get(`${BASE_URL}/api/responsibles`, {
                    headers: authHeaders
                });

                if (allResponse.data.success) {
                    console.log('✅ 获取完整负责人列表成功');
                    console.log('   响应格式:', Object.keys(allResponse.data));
                    if (allResponse.data.data && allResponse.data.data.responsibles) {
                        console.log('   负责人数量:', allResponse.data.data.responsibles.length);
                        if (allResponse.data.data.responsibles.length > 0) {
                            console.log('   第一个负责人:', allResponse.data.data.responsibles[0]);
                        }
                    }
                    if (allResponse.data.data && allResponse.data.data.pagination) {
                        console.log('   分页信息:', allResponse.data.data.pagination);
                    }
                } else {
                    console.log('❌ 获取完整负责人列表失败');
                    console.log('   错误信息:', allResponse.data.error);
                }
            } catch (error) {
                console.log('❌ 获取完整负责人列表请求失败');
                console.log('   错误状态:', error.response?.status);
                console.log('   错误信息:', error.response?.data || error.message);
            }
            console.log('');

            // 4. 测试数据库连接
            console.log('4️⃣ 测试数据库连接...');
            try {
                const dbResponse = await axios.get(`${BASE_URL}/api/db-test`, {
                    headers: authHeaders
                });

                if (dbResponse.data.success) {
                    console.log('✅ 数据库连接正常');
                } else {
                    console.log('❌ 数据库连接失败');
                    console.log('   错误信息:', dbResponse.data.error);
                }
            } catch (error) {
                console.log('❌ 数据库连接测试失败');
                console.log('   错误信息:', error.response?.data || error.message);
            }

        } else {
            console.log('❌ 管理员登录失败');
            console.log('   错误信息:', loginResponse.data.error);
        }

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.response) {
            console.error('   响应状态:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
    }

    console.log('');
    console.log('🏁 负责人API接口测试完成');
}

// 运行测试
testResponsiblesAPI();
