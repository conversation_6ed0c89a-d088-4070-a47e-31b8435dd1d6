# 系统修复和测试方案

## 立即执行的修复步骤

### 步骤1：系统环境检查

#### 1.1 检查防火墙设置
```powershell
# 检查Windows防火墙状态
netsh advfirewall show allprofiles

# 临时禁用防火墙（仅用于测试）
netsh advfirewall set allprofiles state off

# 重新启用防火墙
netsh advfirewall set allprofiles state on
```

#### 1.2 检查端口占用
```powershell
# 检查常用端口
netstat -ano | findstr :3000
netstat -ano | findstr :3001
netstat -ano | findstr :3002
netstat -ano | findstr :8000
netstat -ano | findstr :8001
```

#### 1.3 检查Node.js进程
```powershell
# 查看所有Node.js进程
tasklist | findstr node

# 结束所有Node.js进程
taskkill /f /im node.exe
```

### 步骤2：使用替代端口配置

#### 2.1 修改后端配置使用端口8000
创建新的后端启动文件：`backend/safe-server.js`

#### 2.2 修改前端API配置
更新`frontend/src/services/api.js`中的baseURL为`http://localhost:8000/api`

#### 2.3 修改前端开发服务器端口
更新`frontend/vite.config.js`中的端口为8080

### 步骤3：分步测试方案

#### 3.1 后端独立测试
```bash
# 进入后端目录
cd backend

# 启动安全服务器
node safe-server.js

# 在另一个终端测试连接
curl http://localhost:8000/health
```

#### 3.2 前端独立测试
```bash
# 进入前端目录
cd frontend

# 启动前端服务器
npm run dev

# 在浏览器访问
# http://localhost:8080
```

#### 3.3 集成连接测试
```bash
# 测试API连接
curl http://localhost:8000/api/test

# 在浏览器开发者工具中检查网络请求
```

## 具体修复文件

### 1. 创建安全的后端服务器

文件：`backend/safe-server.js`
- 使用端口8000
- 简化的中间件配置
- 基础的API路由
- 详细的错误日志

### 2. 更新前端配置

#### 2.1 API配置更新
文件：`frontend/src/services/api.js`
```javascript
baseURL: 'http://localhost:8000/api'
```

#### 2.2 Vite配置更新
文件：`frontend/vite.config.js`
```javascript
server: {
  port: 8080,
  host: '0.0.0.0',
  strictPort: true,
  open: false
}
```

### 3. 创建测试脚本

#### 3.1 后端测试脚本
文件：`test-backend-connection.js`
- 自动启动后端服务器
- 执行健康检查
- 测试基础API功能

#### 3.2 前端测试脚本
文件：`test-frontend-connection.js`
- 检查前端服务器状态
- 验证API连接
- 测试页面加载

## 问题排查清单

### ✅ 已解决的问题
- [x] 前端errorHandler导入错误
- [x] 前端API端口配置错误
- [x] 代码结构分析完成

### 🔄 正在解决的问题
- [ ] Node.js进程异常终止
- [ ] 端口监听失败
- [ ] 前后端连接测试

### 📋 待验证的解决方案
- [ ] 使用替代端口（8000/8080）
- [ ] 防火墙配置调整
- [ ] 管理员权限运行
- [ ] 简化的服务器配置

## 成功标准

### 后端成功标准
1. ✅ 服务器进程持续运行
2. ✅ 端口正常监听
3. ✅ 健康检查API响应正常
4. ✅ 基础API路由可访问

### 前端成功标准
1. ✅ Vite开发服务器正常启动
2. ✅ 页面在浏览器中正常加载
3. ✅ 无JavaScript错误
4. ✅ API请求成功发送

### 集成成功标准
1. ✅ 前端可以成功调用后端API
2. ✅ 登录功能正常工作
3. ✅ 页面数据正常显示
4. ✅ 无网络连接错误

## 应急备选方案

### 方案A：使用不同的技术栈
- 考虑使用Python Flask或Django作为后端
- 使用静态文件服务器托管前端

### 方案B：云端部署
- 使用云服务器部署应用
- 避免本地环境问题

### 方案C：容器化部署
- 使用Docker容器运行应用
- 隔离环境依赖问题

## 下一步行动

### 立即执行（优先级：高）
1. 创建safe-server.js文件
2. 更新前端配置文件
3. 执行分步测试

### 短期执行（优先级：中）
1. 系统环境优化
2. 添加详细的错误日志
3. 实施自动化测试

### 长期规划（优先级：低）
1. 容器化部署方案
2. 云端部署迁移
3. 性能优化和监控
