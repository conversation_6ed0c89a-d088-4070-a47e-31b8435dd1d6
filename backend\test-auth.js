const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testAuth() {
    console.log('🧪 开始测试认证功能...\n');

    try {
        // 1. 测试登录
        console.log('1️⃣ 测试用户登录...');
        const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
            username: 'admin',
            password: 'admin123'
        });

        console.log('✅ 登录成功');
        console.log('Token:', loginResponse.data.token.substring(0, 50) + '...');
        console.log('用户信息:', loginResponse.data.user);

        const token = loginResponse.data.token;

        // 2. 测试获取用户信息
        console.log('\n2️⃣ 测试获取用户信息...');
        const profileResponse = await axios.get(`${BASE_URL}/auth/profile`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        console.log('✅ 获取用户信息成功');
        console.log('用户信息:', profileResponse.data.user);

        // 3. 测试token刷新
        console.log('\n3️⃣ 测试token刷新...');
        const refreshResponse = await axios.post(`${BASE_URL}/auth/refresh`, {}, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        console.log('✅ Token刷新成功');
        console.log('新Token:', refreshResponse.data.token.substring(0, 50) + '...');

        // 4. 测试用户注册
        console.log('\n4️⃣ 测试用户注册...');
        const registerResponse = await axios.post(`${BASE_URL}/auth/register`, {
            username: 'testuser',
            password: 'test123',
            real_name: '测试用户',
            email: '<EMAIL>'
        });

        console.log('✅ 用户注册成功');
        console.log('新用户信息:', registerResponse.data.user);

        // 5. 测试错误情况 - 无效登录
        console.log('\n5️⃣ 测试无效登录...');
        try {
            await axios.post(`${BASE_URL}/auth/login`, {
                username: 'invalid',
                password: 'invalid'
            });
        } catch (error) {
            console.log('✅ 无效登录正确被拒绝');
            console.log('错误信息:', error.response.data.error);
        }

        // 6. 测试无token访问受保护路由
        console.log('\n6️⃣ 测试无token访问受保护路由...');
        try {
            await axios.get(`${BASE_URL}/auth/profile`);
        } catch (error) {
            console.log('✅ 无token访问正确被拒绝');
            console.log('错误信息:', error.response.data.error);
        }

        // 7. 测试登出
        console.log('\n7️⃣ 测试用户登出...');
        const logoutResponse = await axios.post(`${BASE_URL}/auth/logout`, {}, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        console.log('✅ 用户登出成功');
        console.log('响应:', logoutResponse.data.message);

        console.log('\n🎉 所有认证功能测试通过！');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('错误详情:', error.response.data);
        }
    }
}

// 检查服务器是否运行
async function checkServer() {
    try {
        await axios.get(`${BASE_URL.replace('/api', '')}/health`);
        console.log('✅ 服务器运行正常\n');
        return true;
    } catch (error) {
        console.error('❌ 服务器未运行，请先启动服务器');
        return false;
    }
}

async function main() {
    const serverRunning = await checkServer();
    if (serverRunning) {
        await testAuth();
    }
}

main();
