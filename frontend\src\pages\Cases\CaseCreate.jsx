import React, { useState, useEffect } from 'react';
import {
  Typography,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Button,
  Card,
  Row,
  Col,
  message,
  Space,
  Spin,
  Upload,
  Divider,
} from 'antd';
import {
  SaveOutlined,
  ArrowLeftOutlined,
  UploadOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import dayjs from 'dayjs';

import { casesAPI, CASE_CONSTANTS } from '../../services/cases';

import { uploadRequest } from '../../services/api';
import { getUser } from '../../utils/auth';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const CaseCreate = () => {
  const navigate = useNavigate();
  const { id } = useParams(); // 获取案件ID，如果存在则为编辑模式
  const [form] = Form.useForm();

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [caseData, setCaseData] = useState(null); // 编辑模式下的案件数据

  const isEditMode = !!id; // 是否为编辑模式

  const currentUser = getUser();



  // 获取案件详情（编辑模式）
  const fetchCaseDetail = async () => {
    if (!isEditMode) return;

    setLoading(true);
    try {
      const response = await casesAPI.getCaseDetail(id);
      console.log('编辑模式案件详情API响应:', response.data); // 调试日志

      // 修复：正确的响应数据结构检查
      if (response.data && response.data.success && response.data.data && response.data.data.case) {
        const caseInfo = response.data.data.case;
        setCaseData(caseInfo);

        // 填充表单数据
        form.setFieldsValue({
          title: caseInfo.title,
          type: caseInfo.type,
          description: caseInfo.description,
          priority: caseInfo.priority,
          deadline: caseInfo.deadline ? dayjs(caseInfo.deadline) : null,
          amount: caseInfo.amount,
          client_name: caseInfo.client_name,
          client_contact: caseInfo.client_contact,
          owner_name: caseInfo.owner_name || '未知负责人'
        });
      }
    } catch (error) {
      console.error('获取案件详情失败:', error);
      message.error('获取案件详情失败');
      navigate('/cases');
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    if (isEditMode) {
      // 编辑模式：获取案件详情
      fetchCaseDetail();
    } else {
      // 创建模式：设置默认值
      form.setFieldsValue({
        priority: '中',
        owner_name: currentUser?.real_name || currentUser?.username || '当前用户'
      });
    }
  }, [id]); // 依赖案件ID

  // 表单提交
  const handleSubmit = async (values) => {
    setLoading(true);
    let targetCaseId = null;

    try {
      // 处理日期格式，移除不需要的字段
      const submitData = {
        ...values,
        deadline: values.deadline ? values.deadline.format('YYYY-MM-DD') : null,
      };

      // 移除前端显示用的字段，不发送给后端
      delete submitData.owner_name;

      if (isEditMode) {
        // 编辑模式：更新案件
        await casesAPI.updateCase(id, submitData);
        targetCaseId = id;
        message.success('案件更新成功');
      } else {
        // 创建模式：创建案件
        const response = await casesAPI.createCase(submitData);
        console.log('创建案件API响应:', response.data); // 调试日志

        // 修复：正确的响应数据结构检查
        if (response.data && response.data.success && response.data.data && response.data.data.case) {
          targetCaseId = response.data.data.case.id;
          message.success('案件创建成功');
        } else {
          console.error('创建案件响应格式不正确:', response.data);
          throw new Error('创建案件响应格式不正确');
        }
      }

      // 如果有文件需要上传（仅创建模式）
      if (!isEditMode && fileList.length > 0 && targetCaseId) {
        try {
          await uploadFiles(targetCaseId);
          message.success('文件上传成功');
        } catch (uploadError) {
          console.error('文件上传失败:', uploadError);
          // 文件上传失败不影响案件创建成功的流程
          message.warning('案件创建成功，但部分文件上传失败。您可以稍后在案件详情页重新上传文件。');
        }
      }

      // 跳转到案件详情页
      if (targetCaseId) {
        navigate(`/cases/${targetCaseId}`);
      }
    } catch (error) {
      console.error(isEditMode ? '更新案件失败:' : '创建案件失败:', error);
      message.error(isEditMode ? '更新案件失败，请稍后重试' : '创建案件失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 上传文件
  const uploadFiles = async (caseId) => {
    if (fileList.length === 0) return;

    setUploadLoading(true);
    try {
      // 验证文件大小和格式
      const maxSize = 50 * 1024 * 1024; // 50MB
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/bmp',
        'text/plain'
      ];

      for (const file of fileList) {
        if (file.size > maxSize) {
          throw new Error(`文件 "${file.name}" 超过50MB大小限制`);
        }
        if (!allowedTypes.includes(file.type)) {
          throw new Error(`文件 "${file.name}" 格式不支持`);
        }
      }

      const formData = new FormData();
      fileList.forEach(file => {
        formData.append('files', file.originFileObj);
      });
      formData.append('description', '案件创建时上传的文件');
      formData.append('file_category', '其他');

      await uploadRequest(`/files/upload/${caseId}`, formData);
    } catch (error) {
      console.error('文件上传失败:', error);
      throw error; // 重新抛出错误，让调用方处理
    } finally {
      setUploadLoading(false);
    }
  };

  // 文件上传配置
  const uploadProps = {
    beforeUpload: (file) => {
      // 文件大小验证（50MB）
      const maxSize = 50 * 1024 * 1024;
      if (file.size > maxSize) {
        message.error(`文件 "${file.name}" 超过50MB大小限制`);
        return false;
      }

      // 文件数量验证（最多10个）
      if (fileList.length >= 10) {
        message.error('最多只能上传10个文件');
        return false;
      }

      // 文件类型验证
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/bmp',
        'text/plain'
      ];

      if (!allowedTypes.includes(file.type)) {
        message.error(`文件 "${file.name}" 格式不支持`);
        return false;
      }

      return false; // 阻止自动上传
    },
    fileList,
    onChange: ({ fileList: newFileList }) => {
      setFileList(newFileList);
    },
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    multiple: true,
    accept: '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif,.bmp',
    maxCount: 10,
  };

  // 表单验证规则
  const formRules = {
    title: [
      { required: true, message: '请输入案件标题' },
      { min: 2, max: 255, message: '案件标题长度应在2-255个字符之间' },
    ],
    type: [
      { required: true, message: '请选择案件类型' },
    ],
    priority: [
      { required: true, message: '请选择优先级' },
    ],

    client_contact: [
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' },
    ],
    amount: [
      { type: 'number', min: 0, message: '涉案金额不能为负数' },
    ],
  };

  return (
    <div>
      {/* 页面标题和返回按钮 */}
      <div style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => navigate('/cases')}
              >
                返回列表
              </Button>
              <Title level={2} style={{ margin: 0 }}>
                {isEditMode ? '编辑案件' : '创建案件'}
              </Title>
            </Space>
          </Col>
        </Row>
      </div>

      {/* 案件信息表单 */}
      <Card title="案件基本信息" style={{ marginBottom: 24 }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          autoComplete="off"
        >
          <Row gutter={24}>
            {/* 案件标题 */}
            <Col xs={24} md={12}>
              <Form.Item
                label="案件标题"
                name="title"
                rules={formRules.title}
              >
                <Input
                  placeholder="请输入案件标题"
                  maxLength={255}
                  showCount
                />
              </Form.Item>
            </Col>

            {/* 案件类型 */}
            <Col xs={24} md={6}>
              <Form.Item
                label="案件类型"
                name="type"
                rules={formRules.type}
              >
                <Select placeholder="请选择案件类型">
                  {Object.entries(CASE_CONSTANTS.TYPES).map(([key, value]) => (
                    <Option key={key} value={value}>{value}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            {/* 优先级 */}
            <Col xs={24} md={6}>
              <Form.Item
                label="优先级"
                name="priority"
                rules={formRules.priority}
              >
                <Select placeholder="请选择优先级">
                  {Object.entries(CASE_CONSTANTS.PRIORITIES).map(([key, value]) => (
                    <Option key={key} value={value}>
                      <span style={{ color: CASE_CONSTANTS.PRIORITY_COLORS[value] }}>
                        {value}
                      </span>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            {/* 负责人 */}
            <Col xs={24} md={12}>
              <Form.Item
                label="负责人"
                name="owner_name"
              >
                <Input
                  value={currentUser?.real_name || currentUser?.username || '当前用户'}
                  disabled
                  style={{ backgroundColor: '#f5f5f5' }}
                  addonBefore="👤"
                />
              </Form.Item>
            </Col>

            {/* 截止日期 */}
            <Col xs={24} md={6}>
              <Form.Item
                label="截止日期"
                name="deadline"
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="请选择截止日期"
                  disabledDate={(current) => current && current < dayjs().startOf('day')}
                />
              </Form.Item>
            </Col>

            {/* 涉案金额 */}
            <Col xs={24} md={6}>
              <Form.Item
                label="涉案金额（元）"
                name="amount"
                rules={formRules.amount}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入涉案金额"
                  min={0}
                  precision={2}
                  formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value.replace(/\$\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>

            {/* 客户名称 */}
            <Col xs={24} md={8}>
              <Form.Item
                label="客户名称"
                name="client_name"
              >
                <Input placeholder="请输入客户名称" />
              </Form.Item>
            </Col>

            {/* 客户联系方式 */}
            <Col xs={24} md={8}>
              <Form.Item
                label="客户联系方式"
                name="client_contact"
                rules={formRules.client_contact}
              >
                <Input placeholder="请输入客户手机号码" />
              </Form.Item>
            </Col>

            {/* 案件描述 */}
            <Col xs={24}>
              <Form.Item
                label="案件描述"
                name="description"
              >
                <TextArea
                  rows={4}
                  placeholder="请详细描述案件情况、争议焦点、处理要求等..."
                  maxLength={2000}
                  showCount
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 文件上传 */}
      <Card title="相关文件" style={{ marginBottom: 24 }}>
        <Upload {...uploadProps}>
          <Button icon={<UploadOutlined />}>
            选择文件
          </Button>
        </Upload>
        <div style={{ marginTop: 8, color: '#666', fontSize: '12px' }}>
          支持格式：PDF、Word、Excel、PowerPoint、图片等，单个文件最大50MB，最多上传10个文件
        </div>

        {fileList.length > 0 && (
          <div style={{ marginTop: 16 }}>
            <Divider orientation="left" orientationMargin="0">
              已选择文件 ({fileList.length})
            </Divider>
            {fileList.map((file, index) => (
              <div key={index} style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px 0',
                borderBottom: '1px solid #f0f0f0'
              }}>
                <span>{file.name}</span>
                <Button
                  type="text"
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => {
                    const newFileList = fileList.filter((_, i) => i !== index);
                    setFileList(newFileList);
                  }}
                />
              </div>
            ))}
          </div>
        )}
      </Card>

      {/* 操作按钮 */}
      <Card>
        <Row justify="end">
          <Col>
            <Space>
              <Button
                onClick={() => navigate('/cases')}
                disabled={loading || uploadLoading}
              >
                取消
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={loading || uploadLoading}
                onClick={() => form.submit()}
              >
                {loading ? (isEditMode ? '更新中...' : '创建中...') : uploadLoading ? '上传中...' : (isEditMode ? '更新案件' : '创建案件')}
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default CaseCreate;
