const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

console.log('🔍 Starting simple backend...');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件配置
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

console.log('✅ Middleware configured');

// 基础路由
app.get('/', (req, res) => {
    console.log('📍 Root route accessed');
    res.json({
        message: '法务案件管理平台 API - Simple Mode',
        version: '1.0.0',
        status: 'running'
    });
});

// 健康检查（不连接数据库）
app.get('/health', (req, res) => {
    console.log('📍 Health check accessed');
    res.json({
        status: 'healthy',
        database: 'skipped',
        timestamp: new Date().toISOString()
    });
});

// 简单的认证路由
app.post('/api/auth/login', (req, res) => {
    console.log('📍 Login attempt:', req.body);
    const { username, password } = req.body;

    // 简单验证
    if (username === 'admin' && password === 'admin123') {
        res.json({
            success: true,
            token: 'test-token-123',
            data: {
                token: 'test-token-123',
                user: {
                    id: 1,
                    username: 'admin',
                    real_name: '系统管理员',
                    email: '<EMAIL>',
                    roles: [{ name: 'admin' }]
                }
            }
        });
    } else {
        res.status(401).json({
            success: false,
            error: 'Invalid credentials'
        });
    }
});

// 404 处理
app.use('*', (req, res) => {
    console.log('📍 404 route:', req.originalUrl);
    res.status(404).json({
        error: 'API endpoint not found',
        path: req.originalUrl,
        method: req.method
    });
});

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('❌ Server error:', error);
    res.status(500).json({
        error: error.message || 'Internal server error'
    });
});

console.log('🚀 Starting server...');

// 启动服务器
const server = app.listen(PORT, () => {
    console.log(`✅ Simple backend is running on port ${PORT}`);
    console.log(`📍 API URL: http://localhost:${PORT}`);
    console.log(`🏥 Health check: http://localhost:${PORT}/health`);
    console.log(`🔑 Login API: http://localhost:${PORT}/api/auth/login`);
});

server.on('error', (error) => {
    console.error('❌ Server error:', error);
    if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use`);
    }
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('🔄 SIGTERM received, shutting down gracefully...');
    server.close(() => process.exit(0));
});

process.on('SIGINT', () => {
    console.log('🔄 SIGINT received, shutting down gracefully...');
    server.close(() => process.exit(0));
});

module.exports = app;
