const mysql = require('mysql2/promise');

async function checkCases() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'sie_h<PERSON><PERSON>tian2025',
      database: 'case_manager'
    });

    console.log('检查案件数据...');

    // 查询最近的案件
    const [cases] = await connection.execute(`
      SELECT id, title, status, deleted_at, created_at 
      FROM cases 
      ORDER BY id DESC 
      LIMIT 10
    `);

    console.log('最近10个案件:');
    cases.forEach(c => {
      console.log(`ID: ${c.id}, 标题: ${c.title}, 状态: ${c.status}, 删除时间: ${c.deleted_at}`);
    });

    // 检查特定案件ID
    const testIds = [12, 15, 20, 23];
    console.log('\n检查特定案件:');

    for (const id of testIds) {
      const [result] = await connection.execute(`
        SELECT id, title, status, deleted_at 
        FROM cases 
        WHERE id = ?
      `, [id]);

      if (result.length > 0) {
        const c = result[0];
        console.log(`案件${id}: 标题=${c.title}, 状态=${c.status}, 删除时间=${c.deleted_at}`);

        // 测试新的查询条件
        const [filtered] = await connection.execute(`
          SELECT id, title, status, deleted_at
          FROM cases
          WHERE id = ? AND deleted_at IS NULL
        `, [id]);

        console.log(`  - 新条件过滤后结果: ${filtered.length > 0 ? '可访问' : '被过滤'}`);

        // 如果案件被软删除，尝试恢复它
        if (filtered.length === 0 && result.length > 0 && result[0].deleted_at) {
          console.log(`  - 案件${id}被软删除，尝试恢复...`);
          await connection.execute(`
            UPDATE cases
            SET deleted_at = NULL, status = '待处理'
            WHERE id = ?
          `, [id]);
          console.log(`  - 案件${id}已恢复`);
        }
      } else {
        console.log(`案件${id}: 不存在`);
      }
    }

    await connection.end();
  } catch (error) {
    console.error('查询失败:', error.message);
  }
}

checkCases();
