/* 争议管理系统响应式设计样式 */

/* 基础响应式断点 */
:root {
  --mobile-breakpoint: 576px;
  --tablet-breakpoint: 768px;
  --desktop-breakpoint: 992px;
  --large-desktop-breakpoint: 1200px;
}

/* 全局响应式设置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: #f5f5f5;
}

/* 容器响应式 */
.responsive-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

/* 移动端优化 */
@media (max-width: 576px) {
  /* 隐藏桌面端元素 */
  .desktop-only {
    display: none !important;
  }
  
  /* 移动端布局调整 */
  .ant-layout-sider {
    position: fixed !important;
    z-index: 1000;
    height: 100vh;
    left: -200px;
    transition: left 0.3s ease;
  }
  
  .ant-layout-sider.mobile-open {
    left: 0;
  }
  
  /* 移动端内容区域 */
  .ant-layout-content {
    margin-left: 0 !important;
    padding: 8px !important;
  }
  
  /* 移动端表格 */
  .ant-table-wrapper {
    overflow-x: auto;
  }
  
  .ant-table {
    min-width: 600px;
  }
  
  /* 移动端表单 */
  .ant-form-item {
    margin-bottom: 16px;
  }
  
  .ant-form-item-label {
    text-align: left !important;
  }
  
  /* 移动端按钮组 */
  .mobile-button-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .mobile-button-group .ant-btn {
    width: 100%;
  }
  
  /* 移动端卡片 */
  .ant-card {
    margin-bottom: 16px;
  }
  
  .ant-card-head-title {
    font-size: 16px;
  }
  
  /* 移动端统计卡片 */
  .ant-statistic-title {
    font-size: 12px;
  }
  
  .ant-statistic-content {
    font-size: 18px;
  }
  
  /* 移动端搜索框 */
  .mobile-search {
    width: 100% !important;
    margin-bottom: 16px;
  }
  
  /* 移动端分页 */
  .ant-pagination {
    text-align: center;
  }
  
  .ant-pagination-options {
    display: none;
  }
}

/* 平板端优化 */
@media (min-width: 577px) and (max-width: 768px) {
  .tablet-hidden {
    display: none !important;
  }
  
  .ant-layout-content {
    padding: 16px;
  }
  
  /* 平板端表格 */
  .ant-table-wrapper {
    overflow-x: auto;
  }
  
  /* 平板端按钮组 */
  .tablet-button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .tablet-button-group .ant-btn {
    flex: 1;
    min-width: 120px;
  }
}

/* 桌面端优化 */
@media (min-width: 769px) {
  .mobile-only {
    display: none !important;
  }
  
  .tablet-only {
    display: none !important;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  /* 增大触摸目标 */
  .ant-btn {
    min-height: 44px;
    padding: 8px 16px;
  }
  
  .ant-input {
    min-height: 44px;
    padding: 8px 12px;
  }
  
  .ant-select-selector {
    min-height: 44px;
  }
  
  /* 触摸友好的表格 */
  .ant-table-tbody > tr > td {
    padding: 12px 8px;
  }
  
  /* 触摸友好的菜单 */
  .ant-menu-item {
    height: 48px;
    line-height: 48px;
  }
}

/* 文件管理页面响应式 */
.file-management-responsive {
  /* 移动端文件列表 */
  @media (max-width: 576px) {
    .file-upload-area {
      padding: 16px;
    }
    
    .file-stats-row .ant-col {
      margin-bottom: 16px;
    }
    
    .file-actions {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    
    .file-table .ant-table-thead {
      display: none;
    }
    
    .file-table .ant-table-tbody tr {
      display: block;
      border: 1px solid #f0f0f0;
      margin-bottom: 8px;
      border-radius: 6px;
    }
    
    .file-table .ant-table-tbody td {
      display: block;
      text-align: left;
      border: none;
      padding: 8px 12px;
    }
    
    .file-table .ant-table-tbody td:before {
      content: attr(data-label) ": ";
      font-weight: bold;
      color: #666;
    }
  }
}

/* 通知页面响应式 */
.notifications-responsive {
  @media (max-width: 576px) {
    .notification-filters {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    
    .notification-filters .ant-input,
    .notification-filters .ant-select {
      width: 100% !important;
    }
    
    .notification-item {
      padding: 12px;
    }
    
    .notification-actions {
      margin-top: 8px;
      display: flex;
      justify-content: space-between;
    }
  }
}

/* 统计页面响应式 */
.statistics-responsive {
  @media (max-width: 576px) {
    .stats-controls {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    
    .stats-controls .ant-select,
    .stats-controls .ant-picker {
      width: 100% !important;
    }
    
    .chart-container {
      overflow-x: auto;
    }
    
    .user-stats-table {
      font-size: 12px;
    }
    
    .user-stats-table .ant-table-thead th {
      padding: 8px 4px;
    }
    
    .user-stats-table .ant-table-tbody td {
      padding: 8px 4px;
    }
  }
}

/* 案件页面响应式 */
.cases-responsive {
  @media (max-width: 576px) {
    .case-filters {
      display: grid;
      grid-template-columns: 1fr;
      gap: 8px;
    }
    
    .case-actions {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    
    .case-detail-tabs .ant-tabs-tab {
      padding: 8px 12px;
      font-size: 14px;
    }
    
    .case-form .ant-form-item-label {
      text-align: left;
    }
  }
}

/* 通用工具类 */
.text-center-mobile {
  @media (max-width: 576px) {
    text-align: center !important;
  }
}

.full-width-mobile {
  @media (max-width: 576px) {
    width: 100% !important;
  }
}

.hidden-mobile {
  @media (max-width: 576px) {
    display: none !important;
  }
}

.hidden-tablet {
  @media (min-width: 577px) and (max-width: 768px) {
    display: none !important;
  }
}

.hidden-desktop {
  @media (min-width: 769px) {
    display: none !important;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .ant-layout-sider {
    display: none !important;
  }
  
  .ant-layout-content {
    margin: 0 !important;
    padding: 0 !important;
  }
  
  .ant-card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }
}
