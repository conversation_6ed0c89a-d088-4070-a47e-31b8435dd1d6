const express = require('express');
const cors = require('cors');
const path = require('path');

console.log('🔍 Starting safe server...');
console.log('📊 Node.js version:', process.version);
console.log('📊 Platform:', process.platform);

const app = express();
const PORT = 8000; // 使用不同的端口避免冲突

console.log('✅ Using PORT:', PORT);

// 基础中间件配置
app.use(cors({
    origin: ['http://localhost:8080', 'http://127.0.0.1:8080'],
    credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

console.log('✅ Middleware configured');

// 请求日志中间件
app.use((req, res, next) => {
    console.log(`📍 ${req.method} ${req.path} - ${new Date().toISOString()}`);
    next();
});

// 基础路由
app.get('/', (req, res) => {
    console.log('📍 Root route accessed');
    res.json({
        message: '法务案件管理平台 API - 安全服务器',
        version: '1.0.0',
        status: 'running',
        port: PORT,
        timestamp: new Date().toISOString()
    });
});

// 健康检查
app.get('/health', (req, res) => {
    console.log('📍 Health check accessed');
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage()
    });
});

// API测试路由
app.get('/api/test', (req, res) => {
    console.log('📍 API test accessed');
    res.json({
        message: 'API is working correctly',
        timestamp: new Date().toISOString(),
        endpoint: '/api/test'
    });
});

// 模拟登录API
app.post('/api/auth/login', (req, res) => {
    console.log('📍 Login API accessed');
    const { username, password } = req.body;
    
    // 简单的测试认证
    if (username === 'admin' && password === 'admin') {
        res.json({
            success: true,
            message: '登录成功',
            token: 'test-token-' + Date.now(),
            user: {
                id: 1,
                username: 'admin',
                name: '管理员'
            }
        });
    } else {
        res.status(401).json({
            success: false,
            message: '用户名或密码错误'
        });
    }
});

// 模拟用户信息API
app.get('/api/auth/me', (req, res) => {
    console.log('📍 User info API accessed');
    const token = req.headers.authorization;
    
    if (token && token.startsWith('Bearer test-token-')) {
        res.json({
            success: true,
            user: {
                id: 1,
                username: 'admin',
                name: '管理员',
                role: 'admin'
            }
        });
    } else {
        res.status(401).json({
            success: false,
            message: '未授权访问'
        });
    }
});

// 模拟案件列表API
app.get('/api/cases', (req, res) => {
    console.log('📍 Cases API accessed');
    res.json({
        success: true,
        data: [
            {
                id: 1,
                title: '测试案件1',
                status: 'active',
                created_at: new Date().toISOString()
            },
            {
                id: 2,
                title: '测试案件2',
                status: 'pending',
                created_at: new Date().toISOString()
            }
        ],
        total: 2
    });
});

// 404 处理
app.use('*', (req, res) => {
    console.log('📍 404 route:', req.originalUrl);
    res.status(404).json({
        error: 'API endpoint not found',
        path: req.originalUrl,
        method: req.method,
        timestamp: new Date().toISOString()
    });
});

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('❌ Server error:', error);
    res.status(500).json({
        error: error.message || 'Internal server error',
        timestamp: new Date().toISOString(),
        ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
    });
});

// 启动服务器
console.log('🚀 Starting server...');

const server = app.listen(PORT, '127.0.0.1', () => {
    console.log(`✅ Safe server is running on port ${PORT}`);
    console.log(`📍 API URL: http://127.0.0.1:${PORT}`);
    console.log(`🏥 Health check: http://127.0.0.1:${PORT}/health`);
    console.log(`🔑 Login API: http://127.0.0.1:${PORT}/api/auth/login`);
    console.log(`📊 Process ID: ${process.pid}`);
});

server.on('error', (error) => {
    console.error('❌ Server error event:', error);
    if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use`);
        console.log('💡 Try using a different port or kill the process using this port');
    }
});

server.on('listening', () => {
    console.log('✅ Server is listening on', server.address());
});

// 优雅关闭处理
process.on('SIGINT', () => {
    console.log('\n🔒 Received SIGINT, shutting down gracefully...');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});

process.on('SIGTERM', () => {
    console.log('\n🔒 Received SIGTERM, shutting down gracefully...');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});

// 未捕获异常处理
process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

console.log('✅ Safe server setup complete');

module.exports = app;
