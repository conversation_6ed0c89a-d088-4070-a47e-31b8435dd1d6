<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MVP前后端数据库集成测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
        }

        .test-section h3 {
            margin-top: 0;
            color: #333;
        }

        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #40a9ff;
        }

        button:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }

        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }

        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }

        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #0958d9;
        }

        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .status.success {
            background: #52c41a;
            color: white;
        }

        .status.error {
            background: #ff4d4f;
            color: white;
        }

        .status.testing {
            background: #faad14;
            color: white;
        }

        .input-group {
            margin: 10px 0;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .data-table th,
        .data-table td {
            border: 1px solid #d9d9d9;
            padding: 8px;
            text-align: left;
        }

        .data-table th {
            background: #fafafa;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🔧 MVP前后端数据库集成测试</h1>

        <div class="test-section">
            <h3>📊 系统状态</h3>
            <p>前端页面: <span id="frontend-status" class="status success">正常</span></p>
            <p>后端服务: <span id="backend-status" class="status testing">检测中</span></p>
            <p>数据库连接: <span id="database-status" class="status testing">检测中</span></p>
            <p>集成状态: <span id="integration-status" class="status testing">检测中</span></p>
        </div>

        <div class="test-section">
            <h3>🏥 服务器健康检查</h3>
            <button onclick="testHealth()">健康检查</button>
            <button onclick="testDatabaseConnection()">数据库连接测试</button>
            <div id="health-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔐 用户认证测试</h3>
            <div class="input-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" placeholder="输入用户名" value="admin">
            </div>
            <div class="input-group">
                <label for="password">密码:</label>
                <input type="password" id="password" placeholder="输入密码" value="admin123">
            </div>
            <button onclick="testLogin()">用户登录</button>
            <button onclick="testUserInfo()">获取用户信息</button>
            <button onclick="testLogout()">退出登录</button>
            <div id="auth-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📋 案件管理测试</h3>
            <div class="input-group">
                <label for="case-title">案件标题:</label>
                <input type="text" id="case-title" placeholder="输入案件标题" value="测试案件">
            </div>
            <div class="input-group">
                <label for="case-type">案件类型:</label>
                <select id="case-type">
                    <option value="合同纠纷">合同纠纷</option>
                    <option value="劳动争议">劳动争议</option>
                    <option value="知识产权">知识产权</option>
                    <option value="公司法务">公司法务</option>
                    <option value="其他">其他</option>
                </select>
            </div>
            <div class="input-group">
                <label for="case-priority">优先级:</label>
                <select id="case-priority">
                    <option value="低">低</option>
                    <option value="中" selected>中</option>
                    <option value="高">高</option>
                    <option value="紧急">紧急</option>
                </select>
            </div>
            <div class="input-group">
                <label for="case-description">案件描述:</label>
                <textarea id="case-description" placeholder="输入案件描述" rows="3">这是一个测试案件</textarea>
            </div>
            <div class="input-group">
                <label for="client-name">客户名称:</label>
                <input type="text" id="client-name" placeholder="输入客户名称" value="测试客户">
            </div>
            <button onclick="createCase()">创建案件</button>
            <button onclick="getCases()">获取案件列表</button>
            <button onclick="clearCaseData()">清空显示</button>
            <div id="case-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 数据库操作测试</h3>
            <div class="input-group">
                <label for="test-message">测试消息:</label>
                <input type="text" id="test-message" placeholder="输入测试消息" value="前端集成测试消息">
            </div>
            <button onclick="createTestData()">创建测试数据</button>
            <button onclick="getTestData()">获取测试数据</button>
            <button onclick="clearTestData()">清空显示</button>
            <div id="data-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔗 完整集成测试</h3>
            <button onclick="runFullIntegrationTest()">运行完整集成测试</button>
            <button onclick="runStressTest()">压力测试</button>
            <div id="integration-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📝 测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <button onclick="exportLog()">导出日志</button>
            <div id="test-log" class="result info"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8001';
        let authToken = null;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = text;
        }

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
        }

        async function testHealth() {
            log('开始健康检查测试...');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                showResult('health-result', data);

                updateStatus('backend-status', 'success', '正常');
                if (data.database === 'connected') {
                    updateStatus('database-status', 'success', '已连接');
                } else {
                    updateStatus('database-status', 'error', '未连接');
                }

                log('✅ 健康检查成功');
            } catch (error) {
                showResult('health-result', `错误: ${error.message}`, true);
                updateStatus('backend-status', 'error', '异常');
                updateStatus('database-status', 'error', '异常');
                log(`❌ 健康检查失败: ${error.message}`);
            }
        }

        async function testDatabaseConnection() {
            log('开始数据库连接测试...');
            try {
                const response = await fetch(`${API_BASE}/api/db-test`);
                const data = await response.json();
                showResult('health-result', data);

                if (data.success) {
                    updateStatus('database-status', 'success', '连接正常');
                    log('✅ 数据库连接测试成功');
                } else {
                    updateStatus('database-status', 'error', '连接失败');
                    log(`❌ 数据库连接测试失败: ${data.message}`);
                }
            } catch (error) {
                showResult('health-result', `错误: ${error.message}`, true);
                updateStatus('database-status', 'error', '连接异常');
                log(`❌ 数据库连接测试失败: ${error.message}`);
            }
        }

        async function testLogin() {
            log('开始用户登录测试...');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showResult('auth-result', '请输入用户名和密码', true);
                log('❌ 用户名或密码为空');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                showResult('auth-result', data);

                if (data.success) {
                    authToken = data.token;
                    log(`✅ 用户登录成功: ${data.user.real_name} (${data.user.username})`);
                    log(`🔑 Token已保存: ${authToken.substring(0, 20)}...`);
                } else {
                    authToken = null;
                    log(`❌ 用户登录失败: ${data.error}`);
                }
            } catch (error) {
                showResult('auth-result', `登录错误: ${error.message}`, true);
                log(`❌ 登录请求失败: ${error.message}`);
            }
        }

        async function testUserInfo() {
            log('开始获取用户信息测试...');

            if (!authToken) {
                showResult('auth-result', '请先登录获取Token', true);
                log('❌ 未找到认证Token，请先登录');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/auth/me`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                showResult('auth-result', data);

                if (data.success) {
                    log(`✅ 获取用户信息成功: ${data.user.real_name}`);
                    log(`👤 用户角色: ${data.user.roles.map(r => r.name).join(', ')}`);
                } else {
                    log(`❌ 获取用户信息失败: ${data.error}`);
                }
            } catch (error) {
                showResult('auth-result', `获取用户信息错误: ${error.message}`, true);
                log(`❌ 获取用户信息失败: ${error.message}`);
            }
        }

        function testLogout() {
            log('执行退出登录...');
            authToken = null;
            showResult('auth-result', { message: '已退出登录', timestamp: new Date().toISOString() });
            log('✅ 已清除认证Token');
        }

        async function createCase() {
            log('开始创建案件测试...');

            if (!authToken) {
                showResult('case-result', '请先登录获取Token', true);
                log('❌ 未找到认证Token，请先登录');
                return;
            }

            const title = document.getElementById('case-title').value;
            const type = document.getElementById('case-type').value;
            const priority = document.getElementById('case-priority').value;
            const description = document.getElementById('case-description').value;
            const client_name = document.getElementById('client-name').value;

            if (!title || !type) {
                showResult('case-result', '请输入案件标题和类型', true);
                log('❌ 案件标题或类型为空');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/cases`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        title,
                        type,
                        priority,
                        description,
                        client_name
                    })
                });

                const data = await response.json();
                showResult('case-result', data);

                if (data.success) {
                    log(`✅ 案件创建成功: ${data.data.case_no} - ${data.data.title}`);
                } else {
                    log(`❌ 案件创建失败: ${data.error}`);
                }
            } catch (error) {
                showResult('case-result', `创建案件错误: ${error.message}`, true);
                log(`❌ 创建案件失败: ${error.message}`);
            }
        }

        async function getCases() {
            log('开始获取案件列表测试...');

            if (!authToken) {
                showResult('case-result', '请先登录获取Token', true);
                log('❌ 未找到认证Token，请先登录');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/cases`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success && data.data.length > 0) {
                    // 创建表格显示案件数据
                    let tableHtml = '<table class="data-table"><thead><tr><th>案件编号</th><th>标题</th><th>类型</th><th>状态</th><th>优先级</th><th>负责人</th><th>创建时间</th></tr></thead><tbody>';
                    data.data.forEach(caseItem => {
                        tableHtml += `<tr>
                            <td>${caseItem.case_no}</td>
                            <td>${caseItem.title}</td>
                            <td>${caseItem.type}</td>
                            <td>${caseItem.status}</td>
                            <td>${caseItem.priority}</td>
                            <td>${caseItem.owner_name || '未分配'}</td>
                            <td>${new Date(caseItem.created_at).toLocaleString()}</td>
                        </tr>`;
                    });
                    tableHtml += '</tbody></table>';

                    document.getElementById('case-result').innerHTML = tableHtml;
                    document.getElementById('case-result').className = 'result success';

                    log(`✅ 获取案件列表成功: ${data.data.length} 条记录`);
                } else {
                    showResult('case-result', data);
                    log(`⚠️ 获取案件列表: ${data.message || '无数据'}`);
                }
            } catch (error) {
                showResult('case-result', `获取案件列表错误: ${error.message}`, true);
                log(`❌ 获取案件列表失败: ${error.message}`);
            }
        }

        function clearCaseData() {
            document.getElementById('case-result').textContent = '';
            document.getElementById('case-result').className = 'result';
        }

        async function createTestData() {
            log('开始创建测试数据...');
            const message = document.getElementById('test-message').value || '默认测试消息';

            try {
                const response = await fetch(`${API_BASE}/api/test-data`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message })
                });

                const data = await response.json();
                showResult('data-result', data);

                if (data.success) {
                    log(`✅ 测试数据创建成功: ID ${data.data.id}`);
                } else {
                    log(`❌ 测试数据创建失败: ${data.message}`);
                }
            } catch (error) {
                showResult('data-result', `错误: ${error.message}`, true);
                log(`❌ 创建测试数据失败: ${error.message}`);
            }
        }

        async function getTestData() {
            log('开始获取测试数据...');
            try {
                const response = await fetch(`${API_BASE}/api/test-data`);
                const data = await response.json();

                if (data.success && data.data.length > 0) {
                    // 创建表格显示数据
                    let tableHtml = '<table class="data-table"><thead><tr><th>ID</th><th>消息</th><th>创建时间</th></tr></thead><tbody>';
                    data.data.forEach(row => {
                        tableHtml += `<tr><td>${row.id}</td><td>${row.message}</td><td>${new Date(row.created_at).toLocaleString()}</td></tr>`;
                    });
                    tableHtml += '</tbody></table>';

                    document.getElementById('data-result').innerHTML = tableHtml;
                    document.getElementById('data-result').className = 'result success';

                    log(`✅ 获取测试数据成功: ${data.count} 条记录`);
                } else {
                    showResult('data-result', data);
                    log(`⚠️ 获取测试数据: ${data.message || '无数据'}`);
                }
            } catch (error) {
                showResult('data-result', `错误: ${error.message}`, true);
                log(`❌ 获取测试数据失败: ${error.message}`);
            }
        }

        async function runFullIntegrationTest() {
            log('开始运行完整集成测试...');
            updateStatus('integration-status', 'testing', '测试中');

            let testResults = {
                health: false,
                database: false,
                auth: false,
                userInfo: false,
                createCase: false,
                getCases: false,
                createData: false,
                getData: false
            };

            try {
                // 1. 健康检查
                log('1/6 执行健康检查...');
                const healthResponse = await fetch(`${API_BASE}/health`);
                const healthData = await healthResponse.json();
                testResults.health = healthData.status === 'healthy';

                // 2. 数据库连接测试
                log('2/6 执行数据库连接测试...');
                const dbResponse = await fetch(`${API_BASE}/api/db-test`);
                const dbData = await dbResponse.json();
                testResults.database = dbData.success;

                // 3. 用户认证测试
                log('3/6 执行用户认证测试...');
                const authResponse = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });
                const authData = await authResponse.json();
                testResults.auth = authData.success;

                // 4. 用户信息测试
                if (authData.success) {
                    log('4/8 执行用户信息测试...');
                    const userResponse = await fetch(`${API_BASE}/api/auth/me`, {
                        headers: { 'Authorization': `Bearer ${authData.token}` }
                    });
                    const userData = await userResponse.json();
                    testResults.userInfo = userData.success;
                    authToken = authData.token; // 保存token供后续使用
                }

                // 5. 创建案件测试
                if (authToken) {
                    log('5/8 执行创建案件测试...');
                    const caseResponse = await fetch(`${API_BASE}/api/cases`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${authToken}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            title: '集成测试案件',
                            type: '合同纠纷',
                            priority: '中',
                            description: '这是集成测试创建的案件',
                            client_name: '测试客户'
                        })
                    });
                    const caseData = await caseResponse.json();
                    testResults.createCase = caseData.success;
                }

                // 6. 获取案件列表测试
                if (authToken) {
                    log('6/8 执行获取案件列表测试...');
                    const casesResponse = await fetch(`${API_BASE}/api/cases`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    });
                    const casesData = await casesResponse.json();
                    testResults.getCases = casesData.success;
                }

                // 7. 创建测试数据
                log('7/8 执行数据创建测试...');
                const createResponse = await fetch(`${API_BASE}/api/test-data`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: `集成测试 - ${new Date().toISOString()}` })
                });
                const createData = await createResponse.json();
                testResults.createData = createData.success;

                // 8. 获取测试数据
                log('8/8 执行数据获取测试...');
                const getResponse = await fetch(`${API_BASE}/api/test-data`);
                const getData = await getResponse.json();
                testResults.getData = getData.success;

                // 汇总结果
                const allPassed = Object.values(testResults).every(result => result);
                const passedCount = Object.values(testResults).filter(result => result).length;
                const totalTests = Object.keys(testResults).length;

                showResult('integration-result', {
                    summary: `集成测试完成: ${passedCount}/${totalTests} 项通过`,
                    results: testResults,
                    status: allPassed ? '全部通过' : '部分失败',
                    timestamp: new Date().toISOString()
                });

                if (allPassed) {
                    updateStatus('integration-status', 'success', '全部通过');
                    log('🎉 完整集成测试全部通过!');
                } else {
                    updateStatus('integration-status', 'error', '部分失败');
                    log(`⚠️ 集成测试完成，${passedCount}/${totalTests} 项通过`);
                }

            } catch (error) {
                showResult('integration-result', `集成测试失败: ${error.message}`, true);
                updateStatus('integration-status', 'error', '测试失败');
                log(`❌ 集成测试失败: ${error.message}`);
            }
        }

        async function runStressTest() {
            log('开始压力测试...');
            const testCount = 10;
            let successCount = 0;
            let errorCount = 0;

            try {
                const promises = [];
                for (let i = 0; i < testCount; i++) {
                    promises.push(
                        fetch(`${API_BASE}/api/test-data`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ message: `压力测试 ${i + 1}` })
                        })
                    );
                }

                const results = await Promise.allSettled(promises);

                for (const result of results) {
                    if (result.status === 'fulfilled' && result.value.ok) {
                        successCount++;
                    } else {
                        errorCount++;
                    }
                }

                showResult('integration-result', {
                    testType: '压力测试',
                    totalRequests: testCount,
                    successCount: successCount,
                    errorCount: errorCount,
                    successRate: `${(successCount / testCount * 100).toFixed(2)}%`,
                    timestamp: new Date().toISOString()
                });

                log(`✅ 压力测试完成: ${successCount}/${testCount} 成功`);

            } catch (error) {
                showResult('integration-result', `压力测试失败: ${error.message}`, true);
                log(`❌ 压力测试失败: ${error.message}`);
            }
        }

        function clearTestData() {
            document.getElementById('data-result').textContent = '';
            document.getElementById('data-result').className = 'result';
        }

        function clearLog() {
            document.getElementById('test-log').textContent = '';
        }

        function exportLog() {
            const logContent = document.getElementById('test-log').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `mvp-test-log-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 页面加载时自动运行基础测试
        window.onload = function () {
            log('页面加载完成，开始自动测试...');
            setTimeout(testHealth, 1000);
        };
    </script>
</body>

</html>