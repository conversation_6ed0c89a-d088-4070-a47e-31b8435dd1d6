import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Card,
  List,
  Button,
  Space,
  Tag,
  Badge,
  Empty,
  Spin,
  Row,
  Col,
  Statistic,
  Select,
  Input,
  Checkbox,
  Popconfirm,
  message,
  Avatar,
  Tooltip,
  Divider,
} from 'antd';
import {
  BellOutlined,
  CheckOutlined,
  DeleteOutlined,
  ReloadOutlined,
  SearchOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  MailOutlined,
} from '@ant-design/icons';
import { request } from '../../services/api';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const { Title } = Typography;
const { Option } = Select;

const Notifications = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [selectedItems, setSelectedItems] = useState([]);
  const [filters, setFilters] = useState({
    type: '',
    read: '',
    search: '',
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [statistics, setStatistics] = useState({
    total: 0,
    unread: 0,
    byType: {},
  });

  // 通知类型配置
  const NOTIFICATION_TYPES = {
    'info': { label: '信息', color: 'blue', icon: <InfoCircleOutlined /> },
    'warning': { label: '警告', color: 'orange', icon: <ExclamationCircleOutlined /> },
    'success': { label: '成功', color: 'green', icon: <CheckCircleOutlined /> },
    'error': { label: '错误', color: 'red', icon: <CloseCircleOutlined /> },
    'system': { label: '系统', color: 'purple', icon: <MailOutlined /> },
  };

  // 初始化加载
  useEffect(() => {
    fetchNotifications();
  }, [pagination.current, pagination.pageSize, filters]);

  // 获取通知列表
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        ...filters,
      };

      const response = await request.get('/notifications', { params });

      if (response.data) {
        setNotifications(response.data.data || []);
        setPagination(prev => ({
          ...prev,
          total: response.data.total || 0,
        }));

        // 计算统计信息
        const stats = {
          total: response.data.total || 0,
          unread: response.data.unread || 0,
          byType: {},
        };

        response.data.data?.forEach(notification => {
          const type = notification.type || 'info';
          stats.byType[type] = (stats.byType[type] || 0) + 1;
        });

        setStatistics(stats);
      }
    } catch (error) {
      console.error('获取通知列表失败:', error);
      message.error('获取通知列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 标记为已读
  const markAsRead = async (notificationIds) => {
    try {
      await request.post('/notifications/read', {
        notification_ids: Array.isArray(notificationIds) ? notificationIds : [notificationIds]
      });

      message.success('标记成功');
      fetchNotifications();
      setSelectedItems([]);
    } catch (error) {
      console.error('标记已读失败:', error);
      message.error('标记已读失败，请稍后重试');
    }
  };

  // 删除通知
  const deleteNotifications = async (notificationIds) => {
    try {
      const ids = Array.isArray(notificationIds) ? notificationIds : [notificationIds];

      await Promise.all(ids.map(id => request.delete(`/notifications/${id}`)));

      message.success('删除成功');
      fetchNotifications();
      setSelectedItems([]);
    } catch (error) {
      console.error('删除通知失败:', error);
      message.error('删除通知失败，请稍后重试');
    }
  };

  // 全选/取消全选
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedItems(notifications.map(item => item.id));
    } else {
      setSelectedItems([]);
    }
  };

  // 处理搜索
  const handleSearch = (value) => {
    setFilters(prev => ({ ...prev, search: value }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 获取通知图标
  const getNotificationIcon = (type) => {
    return NOTIFICATION_TYPES[type]?.icon || <InfoCircleOutlined />;
  };

  // 获取通知颜色
  const getNotificationColor = (type) => {
    return NOTIFICATION_TYPES[type]?.color || 'blue';
  };

  return (
    <div>
      <Title level={2}>
        <BellOutlined /> 通知消息
      </Title>

      {/* 统计信息 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card size="small">
            <Statistic
              title="总通知数"
              value={statistics.total}
              prefix={<BellOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card size="small">
            <Statistic
              title="未读通知"
              value={statistics.unread}
              prefix={<Badge dot />}
              valueStyle={{ color: statistics.unread > 0 ? '#ff4d4f' : undefined }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12}>
          <Card size="small" title="通知类型分布">
            <Space wrap>
              {Object.entries(statistics.byType).map(([type, count]) => (
                <Tag key={type} color={getNotificationColor(type)}>
                  {NOTIFICATION_TYPES[type]?.label || type}: {count}
                </Tag>
              ))}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 操作栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Input.Search
                placeholder="搜索通知内容..."
                allowClear
                style={{ width: 250 }}
                onSearch={handleSearch}
              />
              <Select
                placeholder="通知类型"
                allowClear
                style={{ width: 120 }}
                value={filters.type}
                onChange={(value) => setFilters(prev => ({ ...prev, type: value }))}
              >
                {Object.entries(NOTIFICATION_TYPES).map(([type, config]) => (
                  <Option key={type} value={type}>
                    <Space>
                      {config.icon}
                      {config.label}
                    </Space>
                  </Option>
                ))}
              </Select>
              <Select
                placeholder="读取状态"
                allowClear
                style={{ width: 120 }}
                value={filters.read}
                onChange={(value) => setFilters(prev => ({ ...prev, read: value }))}
              >
                <Option value="false">未读</Option>
                <Option value="true">已读</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchNotifications}
                loading={loading}
              >
                刷新
              </Button>
              {selectedItems.length > 0 && (
                <>
                  <Button
                    icon={<CheckOutlined />}
                    onClick={() => markAsRead(selectedItems)}
                  >
                    标记已读
                  </Button>
                  <Popconfirm
                    title="确定要删除选中的通知吗？"
                    onConfirm={() => deleteNotifications(selectedItems)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      danger
                      icon={<DeleteOutlined />}
                    >
                      删除选中
                    </Button>
                  </Popconfirm>
                </>
              )}
            </Space>
          </Col>
        </Row>

        {notifications.length > 0 && (
          <Row style={{ marginTop: 16 }}>
            <Col>
              <Checkbox
                indeterminate={selectedItems.length > 0 && selectedItems.length < notifications.length}
                checked={selectedItems.length === notifications.length}
                onChange={(e) => handleSelectAll(e.target.checked)}
              >
                全选 ({selectedItems.length}/{notifications.length})
              </Checkbox>
            </Col>
          </Row>
        )}
      </Card>

      {/* 通知列表 */}
      <Card>
        <Spin spinning={loading}>
          {notifications.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无通知消息"
            />
          ) : (
            <List
              itemLayout="horizontal"
              dataSource={notifications}
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: (page, pageSize) => {
                  setPagination(prev => ({ ...prev, current: page, pageSize }));
                },
              }}
              renderItem={(item) => (
                <List.Item
                  key={item.id}
                  style={{
                    backgroundColor: item.read ? '#fafafa' : '#fff',
                    border: item.read ? '1px solid #f0f0f0' : '1px solid #d9d9d9',
                    borderRadius: '6px',
                    marginBottom: '8px',
                    padding: '16px',
                  }}
                  actions={[
                    <Checkbox
                      key="select"
                      checked={selectedItems.includes(item.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedItems(prev => [...prev, item.id]);
                        } else {
                          setSelectedItems(prev => prev.filter(id => id !== item.id));
                        }
                      }}
                    />,
                    !item.read && (
                      <Tooltip title="标记为已读" key="read">
                        <Button
                          type="text"
                          icon={<CheckOutlined />}
                          onClick={() => markAsRead(item.id)}
                        />
                      </Tooltip>
                    ),
                    <Tooltip title="删除" key="delete">
                      <Popconfirm
                        title="确定要删除这条通知吗？"
                        onConfirm={() => deleteNotifications(item.id)}
                        okText="确定"
                        cancelText="取消"
                      >
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                        />
                      </Popconfirm>
                    </Tooltip>,
                  ].filter(Boolean)}
                >
                  <List.Item.Meta
                    avatar={
                      <Badge dot={!item.read}>
                        <Avatar
                          style={{
                            backgroundColor: getNotificationColor(item.type),
                          }}
                          icon={getNotificationIcon(item.type)}
                        />
                      </Badge>
                    }
                    title={
                      <Space>
                        <span style={{ fontWeight: item.read ? 'normal' : 'bold' }}>
                          {item.title}
                        </span>
                        <Tag color={getNotificationColor(item.type)}>
                          {NOTIFICATION_TYPES[item.type]?.label || item.type}
                        </Tag>
                        {!item.read && <Badge status="processing" text="未读" />}
                      </Space>
                    }
                    description={
                      <div>
                        <div style={{ marginBottom: 8 }}>
                          {item.message}
                        </div>
                        <Space split={<Divider type="vertical" />}>
                          <span style={{ color: '#999', fontSize: '12px' }}>
                            {dayjs(item.created_at).format('YYYY-MM-DD HH:mm:ss')}
                          </span>
                          <span style={{ color: '#999', fontSize: '12px' }}>
                            {dayjs(item.created_at).fromNow()}
                          </span>
                        </Space>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          )}
        </Spin>
      </Card>
    </div>
  );
};

export default Notifications;
