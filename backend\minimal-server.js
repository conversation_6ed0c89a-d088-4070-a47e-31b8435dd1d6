const express = require('express');
const cors = require('cors');
const path = require('path');

console.log('🔍 Starting minimal server...');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '.env') });

const app = express();
const PORT = process.env.PORT || 3000;

// 基础中间件
app.use(cors());
app.use(express.json());

// 基础路由
app.get('/', (req, res) => {
    res.json({
        message: '法务案件管理平台 API - Minimal Mode',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString()
    });
});

app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString()
    });
});

// 启动服务器
console.log('🚀 Starting minimal server...');
const server = app.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 Minimal server running on port ${PORT}`);
    console.log(`📍 Server URL: http://localhost:${PORT}`);
    console.log(`🏥 Health check: http://localhost:${PORT}/health`);
});

server.on('error', (error) => {
    console.error('❌ Server error:', error);
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('🔄 Shutting down...');
    server.close(() => process.exit(0));
});

process.on('SIGINT', () => {
    console.log('🔄 Shutting down...');
    server.close(() => process.exit(0));
});
