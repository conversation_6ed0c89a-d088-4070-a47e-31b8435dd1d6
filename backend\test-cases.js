const http = require('http');

function makeRequest(options, data) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    body: body
                });
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(data);
        }
        req.end();
    });
}

async function login() {
    console.log('🔐 登录获取token...');
    
    const loginData = JSON.stringify({
        username: 'admin',
        password: 'admin123'
    });

    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(loginData)
        }
    };

    try {
        const response = await makeRequest(options, loginData);
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 登录成功');
            return data.token;
        } else {
            console.log('❌ 登录失败');
            return null;
        }
    } catch (error) {
        console.error('❌ 登录错误:', error.message);
        return null;
    }
}

async function testCreateCase(token) {
    console.log('\n📝 测试创建案件...');
    
    const caseData = JSON.stringify({
        title: '测试合同纠纷案件',
        type: '合同纠纷',
        description: '这是一个测试案件，用于验证案件管理功能',
        priority: '高',
        amount: 100000,
        client_name: '测试客户公司',
        client_contact: '13800138000'
    });

    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/cases',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(caseData),
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options, caseData);
        console.log('状态码:', response.statusCode);
        
        if (response.statusCode === 201) {
            const data = JSON.parse(response.body);
            console.log('✅ 案件创建成功');
            console.log('案件编号:', data.case.case_no);
            console.log('案件ID:', data.case.id);
            return data.case.id;
        } else {
            console.log('❌ 案件创建失败');
            console.log('响应:', response.body);
            return null;
        }
    } catch (error) {
        console.error('❌ 创建案件错误:', error.message);
        return null;
    }
}

async function testGetCases(token) {
    console.log('\n📋 测试获取案件列表...');
    
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/cases?page=1&limit=5',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options);
        console.log('状态码:', response.statusCode);
        
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 获取案件列表成功');
            console.log('案件总数:', data.pagination.total);
            console.log('当前页案件数:', data.cases.length);
            if (data.cases.length > 0) {
                console.log('第一个案件:', data.cases[0].title);
            }
        } else {
            console.log('❌ 获取案件列表失败');
            console.log('响应:', response.body);
        }
    } catch (error) {
        console.error('❌ 获取案件列表错误:', error.message);
    }
}

async function testGetCaseDetail(token, caseId) {
    if (!caseId) return;
    
    console.log('\n🔍 测试获取案件详情...');
    
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: `/api/cases/${caseId}`,
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options);
        console.log('状态码:', response.statusCode);
        
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 获取案件详情成功');
            console.log('案件标题:', data.case.title);
            console.log('案件状态:', data.case.status);
            console.log('流转记录数:', data.case.flows.length);
        } else {
            console.log('❌ 获取案件详情失败');
            console.log('响应:', response.body);
        }
    } catch (error) {
        console.error('❌ 获取案件详情错误:', error.message);
    }
}

async function testUpdateCaseStatus(token, caseId) {
    if (!caseId) return;
    
    console.log('\n🔄 测试更新案件状态...');
    
    const statusData = JSON.stringify({
        status: '处理中',
        remark: '开始处理此案件'
    });

    const options = {
        hostname: 'localhost',
        port: 3000,
        path: `/api/cases/${caseId}/status`,
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(statusData),
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options, statusData);
        console.log('状态码:', response.statusCode);
        
        if (response.statusCode === 200) {
            const data = JSON.parse(response.body);
            console.log('✅ 案件状态更新成功');
            console.log('新状态:', data.case.status);
        } else {
            console.log('❌ 案件状态更新失败');
            console.log('响应:', response.body);
        }
    } catch (error) {
        console.error('❌ 更新案件状态错误:', error.message);
    }
}

async function main() {
    console.log('🧪 开始测试案件管理功能...\n');
    
    const token = await login();
    if (!token) {
        console.log('❌ 无法获取token，测试终止');
        return;
    }
    
    const caseId = await testCreateCase(token);
    await testGetCases(token);
    await testGetCaseDetail(token, caseId);
    await testUpdateCaseStatus(token, caseId);
    
    console.log('\n✅ 案件管理功能测试完成');
}

main();
