const net = require('net');

console.log('🔍 Testing port availability...');

function testPort(port) {
    return new Promise((resolve, reject) => {
        const server = net.createServer();
        
        server.listen(port, (err) => {
            if (err) {
                resolve({ port, available: false, error: err.message });
            } else {
                server.close(() => {
                    resolve({ port, available: true });
                });
            }
        });
        
        server.on('error', (err) => {
            resolve({ port, available: false, error: err.message });
        });
    });
}

async function testPorts() {
    const ports = [3000, 3001, 3002, 3003, 3004, 3005];
    
    console.log('Testing ports:', ports.join(', '));
    
    for (const port of ports) {
        try {
            const result = await testPort(port);
            if (result.available) {
                console.log(`✅ Port ${port}: Available`);
            } else {
                console.log(`❌ Port ${port}: ${result.error}`);
            }
        } catch (error) {
            console.log(`❌ Port ${port}: Test failed - ${error.message}`);
        }
    }
}

testPorts().then(() => {
    console.log('✅ Port testing complete');
    process.exit(0);
}).catch((error) => {
    console.error('❌ Port testing failed:', error);
    process.exit(1);
});
