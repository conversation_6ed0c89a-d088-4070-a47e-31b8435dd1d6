const express = require('express');
const cors = require('cors');

console.log('🔍 Starting simple test server...');

const app = express();
const PORT = 3001;

// 中间件
app.use(cors());
app.use(express.json());

// 基础路由
app.get('/', (req, res) => {
    console.log('📍 Root route accessed');
    res.json({
        message: '法务案件管理平台 API',
        version: '1.0.0',
        status: 'running',
        port: PORT
    });
});

// 健康检查
app.get('/health', (req, res) => {
    console.log('📍 Health check accessed');
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString()
    });
});

// 测试API路由
app.get('/api/test', (req, res) => {
    console.log('📍 Test API accessed');
    res.json({
        message: 'API is working',
        timestamp: new Date().toISOString()
    });
});

// 启动服务器
console.log('🚀 Starting server...');
const server = app.listen(PORT, '127.0.0.1', () => {
    console.log(`✅ Simple test server is running on port ${PORT}`);
    console.log(`📍 API URL: http://127.0.0.1:${PORT}`);
    console.log(`🏥 Health check: http://127.0.0.1:${PORT}/health`);
});

server.on('error', (error) => {
    console.error('❌ Server error:', error);
});

server.on('listening', () => {
    console.log('✅ Server is listening on', server.address());
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🔒 Shutting down server...');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});

process.on('SIGTERM', () => {
    console.log('\n🔒 Shutting down server...');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});
