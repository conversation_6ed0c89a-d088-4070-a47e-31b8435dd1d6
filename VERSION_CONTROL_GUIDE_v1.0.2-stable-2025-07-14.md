# 版本控制指南 - v1.0.2-stable-2025-07-14

**创建时间**: 2025年7月14日  
**版本标识**: v1.0.2-stable-2025-07-14  
**Git标签**: v1.0.2-stable-2025-07-14  
**提交哈希**: 5f65253

---

## 🎯 版本控制目标

本指南旨在为法务案件管理平台提供完整的版本控制和回溯方案，确保：
- 项目状态的可靠备份和恢复
- 开发过程中的安全回溯机制
- 版本间的清晰对比和管理
- 团队协作的版本同步

---

## 📋 当前版本信息

### 版本标识
```
版本号: v1.0.2-stable-2025-07-14
创建时间: 2025年7月14日
Git提交: 5f65253
分支: master
状态: 稳定版本
```

### 技术栈快照
```
前端技术栈:
- React: 19.1.0
- Vite: 7.0.0  
- Ant Design: 5.26.4
- React Router: 7.6.3
- Axios: 1.10.0
- dayjs: 1.11.13

后端技术栈:
- Node.js: 22.17.0
- Express: 4.21.2
- Sequelize: 6.37.7
- MySQL2: 3.14.1
- bcrypt: 5.1.1
- jsonwebtoken: 9.0.2
- multer: 2.0.1
```

### 端口配置
```
前端开发服务器: http://localhost:3001
后端API服务器: http://localhost:8001  
数据库服务器: localhost:3306
```

### 主要功能状态
```
✅ 用户认证系统: 100% (登录、注册、JWT认证、权限控制)
✅ 案件管理功能: 95% (CRUD操作、状态流转、分配管理)
✅ 文件管理系统: 90% (上传、下载、预览、批量操作)
✅ 通知消息系统: 85% (创建、读取、状态管理)
✅ 统计报表功能: 80% (数据统计、图表展示)
✅ 负责人管理: 95% (用户分配、权限管理)
```

---

## 🔄 版本回溯命令

### 1. 回溯到当前稳定版本
```bash
# 方法1: 使用标签回溯（推荐）
git checkout v1.0.2-stable-2025-07-14

# 方法2: 使用提交哈希回溯
git checkout 5f65253

# 方法3: 创建新分支基于此版本
git checkout -b hotfix-from-stable v1.0.2-stable-2025-07-14
```

### 2. 强制重置到稳定版本（谨慎使用）
```bash
# ⚠️ 警告：此操作会丢失所有未提交的更改
git reset --hard v1.0.2-stable-2025-07-14

# 如果需要重置远程分支（如果有远程仓库）
git push --force-with-lease origin master
```

### 3. 查看版本差异
```bash
# 查看当前状态与稳定版本的差异
git diff v1.0.2-stable-2025-07-14

# 查看文件变更列表
git diff --name-only v1.0.2-stable-2025-07-14

# 查看具体文件的差异
git diff v1.0.2-stable-2025-07-14 -- frontend/src/App.jsx
```

---

## 📊 版本管理工作流

### 开发阶段版本管理
```bash
# 1. 从稳定版本创建开发分支
git checkout -b feature/new-feature v1.0.2-stable-2025-07-14

# 2. 开发过程中定期提交
git add .
git commit -m "feat: 添加新功能"

# 3. 功能完成后合并到主分支
git checkout master
git merge feature/new-feature

# 4. 创建新的稳定版本标签
git tag -a v1.0.3-stable -m "新功能稳定版本"
```

### 紧急修复工作流
```bash
# 1. 从稳定版本创建修复分支
git checkout -b hotfix/urgent-fix v1.0.2-stable-2025-07-14

# 2. 进行紧急修复
git add .
git commit -m "fix: 紧急修复问题"

# 3. 合并修复到主分支
git checkout master
git merge hotfix/urgent-fix

# 4. 创建修复版本标签
git tag -a v1.0.2-hotfix-1 -m "紧急修复版本"
```

---

## 🛠️ 实用Git命令

### 版本查看命令
```bash
# 查看所有标签
git tag -l

# 查看标签详细信息
git show v1.0.2-stable-2025-07-14

# 查看提交历史
git log --oneline --graph --decorate

# 查看当前分支状态
git status
git branch -v
```

### 版本比较命令
```bash
# 比较两个版本
git diff v1.0.1-stable-enhanced..v1.0.2-stable-2025-07-14

# 查看版本间的提交记录
git log v1.0.1-stable-enhanced..v1.0.2-stable-2025-07-14 --oneline

# 查看特定文件的版本历史
git log --follow -- frontend/src/App.jsx
```

### 版本恢复命令
```bash
# 恢复特定文件到稳定版本
git checkout v1.0.2-stable-2025-07-14 -- frontend/src/App.jsx

# 恢复整个目录到稳定版本
git checkout v1.0.2-stable-2025-07-14 -- frontend/src/

# 撤销最近的提交（保留更改）
git reset --soft HEAD~1

# 撤销最近的提交（丢弃更改）
git reset --hard HEAD~1
```

---

## 🚨 应急恢复方案

### 场景1: 开发过程中出现严重问题
```bash
# 立即回溯到稳定版本
git stash  # 暂存当前更改（可选）
git checkout v1.0.2-stable-2025-07-14

# 验证系统功能正常后，分析问题
git log --oneline master..v1.0.2-stable-2025-07-14
```

### 场景2: 需要完全重置项目状态
```bash
# 备份当前状态（可选）
git branch backup-$(date +%Y%m%d-%H%M%S)

# 强制重置到稳定版本
git reset --hard v1.0.2-stable-2025-07-14

# 清理未跟踪的文件
git clean -fd
```

### 场景3: 恢复特定功能模块
```bash
# 只恢复前端代码
git checkout v1.0.2-stable-2025-07-14 -- frontend/

# 只恢复后端代码  
git checkout v1.0.2-stable-2025-07-14 -- backend/

# 只恢复配置文件
git checkout v1.0.2-stable-2025-07-14 -- *.json *.js *.md
```

---

## 📝 版本发布检查清单

### 发布前检查
- [x] 所有功能测试通过
- [x] 前端构建成功 (`npm run build`)
- [x] 后端服务启动正常
- [x] 数据库连接测试通过
- [x] API接口测试完成
- [x] 用户认证功能正常
- [x] 文件上传功能正常
- [x] 响应式设计验证

### 版本标签创建
```bash
# 创建带注释的标签
git tag -a v1.0.x-stable -m "版本描述"

# 推送标签到远程（如果有远程仓库）
git push origin v1.0.x-stable

# 推送所有标签
git push origin --tags
```

### 发布后验证
- [x] 标签创建成功
- [x] 版本文档更新
- [ ] 回溯命令测试
- [ ] 团队成员同步确认

---

## 🔍 故障排查指南

### Git操作问题
```bash
# 查看Git状态
git status
git log --oneline -10

# 检查分支状态
git branch -a
git remote -v

# 修复常见问题
git fsck  # 检查仓库完整性
git gc    # 清理仓库
```

### 版本回溯问题
```bash
# 如果回溯后服务无法启动
cd backend && npm install
cd frontend && npm install

# 检查环境配置
cat backend/.env
cat frontend/vite.config.js

# 重新启动服务
cd backend && npm start
cd frontend && npm run dev
```

---

## 📚 相关文档

- [项目状态快照](./PROJECT_SNAPSHOT_v1.0.2-stable-2025-07-14.md)
- [版本发布说明](./VERSION_RELEASE_NOTES_v1.0.2-stable-2025-07-14.md)
- [开发进度分析报告](./Sie_Dispute_Manager_全面开发进度分析报告_2025-07-10.md)

---

## 🎯 下一步计划

1. **建立远程仓库**: 配置GitHub/GitLab远程仓库
2. **自动化部署**: 设置CI/CD流水线
3. **版本管理规范**: 制定团队版本管理规范
4. **备份策略**: 建立定期备份机制

---

**最后更新**: 2025年7月14日  
**维护人员**: 开发团队  
**下次审查**: 2025年7月21日
