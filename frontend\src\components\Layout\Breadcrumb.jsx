import React from 'react';
import { Breadcrumb as AntBreadcrumb } from 'antd';
import { Link, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  FileTextOutlined,
  FolderOutlined,
  BellOutlined,
  BarChartOutlined,
  UserOutlined,
  PlusOutlined,
} from '@ant-design/icons';

// 路由配置映射
const routeConfig = {
  '/dashboard': {
    title: '仪表板',
    icon: <DashboardOutlined />,
  },
  '/cases': {
    title: '案件管理',
    icon: <FileTextOutlined />,
  },
  '/cases/create': {
    title: '创建案件',
    icon: <PlusOutlined />,
    parent: '/cases',
  },
  '/files': {
    title: '文件管理',
    icon: <FolderOutlined />,
  },
  '/notifications': {
    title: '通知消息',
    icon: <BellOutlined />,
  },
  '/statistics': {
    title: '统计报表',
    icon: <BarChartOutlined />,
  },
  '/admin': {
    title: '系统管理',
    icon: <UserOutlined />,
  },
  '/admin/responsibles': {
    title: '负责人管理',
    icon: <UserOutlined />,
    parent: '/admin',
  },
};

const Breadcrumb = () => {
  const location = useLocation();
  const pathname = location.pathname;

  // 生成面包屑项
  const generateBreadcrumbItems = () => {
    const items = [];

    // 首页
    items.push({
      title: (
        <Link to="/dashboard">
          <DashboardOutlined style={{ marginRight: 4 }} />
          首页
        </Link>
      ),
    });

    // 当前页面不是首页时才添加其他项
    if (pathname !== '/dashboard') {
      const currentRoute = routeConfig[pathname];
      
      if (currentRoute) {
        // 如果有父级路由，先添加父级
        if (currentRoute.parent) {
          const parentRoute = routeConfig[currentRoute.parent];
          if (parentRoute) {
            items.push({
              title: (
                <Link to={currentRoute.parent}>
                  {parentRoute.icon && React.cloneElement(parentRoute.icon, { style: { marginRight: 4 } })}
                  {parentRoute.title}
                </Link>
              ),
            });
          }
        }

        // 添加当前页面
        items.push({
          title: (
            <span>
              {currentRoute.icon && React.cloneElement(currentRoute.icon, { style: { marginRight: 4 } })}
              {currentRoute.title}
            </span>
          ),
        });
      } else {
        // 处理动态路由（如案件详情页）
        if (pathname.startsWith('/cases/') && pathname !== '/cases' && pathname !== '/cases/create') {
          // 案件详情页
          items.push({
            title: (
              <Link to="/cases">
                <FileTextOutlined style={{ marginRight: 4 }} />
                案件管理
              </Link>
            ),
          });
          
          items.push({
            title: (
              <span>
                <FileTextOutlined style={{ marginRight: 4 }} />
                案件详情
              </span>
            ),
          });
        } else {
          // 未知路由，显示路径
          items.push({
            title: pathname,
          });
        }
      }
    }

    return items;
  };

  const breadcrumbItems = generateBreadcrumbItems();

  // 如果只有首页一个项目，不显示面包屑
  if (breadcrumbItems.length <= 1) {
    return null;
  }

  return (
    <AntBreadcrumb
      items={breadcrumbItems}
      style={{
        marginBottom: 16,
        fontSize: '14px',
      }}
    />
  );
};

export default Breadcrumb;
