import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

// 页面组件
import Layout from './components/Layout/Layout';
import ErrorBoundary from './components/ErrorBoundary/ErrorBoundary';
import Login from './pages/Auth/Login';
import Register from './pages/Auth/Register';
import Dashboard from './pages/Dashboard/Dashboard';
import CaseList from './pages/Cases/CaseList';
import CaseDetail from './pages/Cases/CaseDetail';
import CaseCreate from './pages/Cases/CaseCreate';
import RecycleBin from './pages/Cases/RecycleBin';
import FileManagement from './pages/Files/FileManagement';
import Notifications from './pages/Notifications/Notifications';
import Statistics from './pages/Statistics/Statistics';
import ResponsibleManagement from './pages/Admin/ResponsibleManagement';
import UserPermissionManagement from './pages/Admin/UserPermissionManagement';

// 工具函数
import { getToken } from './utils/auth';

// 设置dayjs中文
dayjs.locale('zh-cn');

// 私有路由组件
const PrivateRoute = ({ children }) => {
  const token = getToken();
  return token ? children : <Navigate to="/login" replace />;
};

// 公共路由组件（已登录用户重定向到仪表板）
const PublicRoute = ({ children }) => {
  const token = getToken();
  return token ? <Navigate to="/dashboard" replace /> : children;
};

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <ErrorBoundary>
        <Router>
          <div className="App">
            <Routes>
              {/* 公共路由 */}
              <Route
                path="/login"
                element={
                  <PublicRoute>
                    <Login />
                  </PublicRoute>
                }
              />
              <Route
                path="/register"
                element={
                  <PublicRoute>
                    <Register />
                  </PublicRoute>
                }
              />

              {/* 私有路由 */}
              <Route
                path="/"
                element={
                  <PrivateRoute>
                    <Layout />
                  </PrivateRoute>
                }
              >
                {/* 默认重定向到仪表板 */}
                <Route index element={<Navigate to="/dashboard" replace />} />

                {/* 仪表板 */}
                <Route path="dashboard" element={<Dashboard />} />

                {/* 案件管理 */}
                <Route path="cases" element={<CaseList />} />
                <Route path="cases/create" element={<CaseCreate />} />
                <Route path="cases/recycle" element={<RecycleBin />} />
                <Route path="cases/:id" element={<CaseDetail />} />
                <Route path="cases/:id/edit" element={<CaseCreate />} />

                {/* 文件管理 */}
                <Route path="files" element={<FileManagement />} />

                {/* 通知消息 */}
                <Route path="notifications" element={<Notifications />} />

                {/* 统计报表 */}
                <Route path="statistics" element={<Statistics />} />

                {/* 管理员功能 */}
                <Route path="admin/responsibles" element={<ResponsibleManagement />} />
                <Route path="admin/permissions" element={<UserPermissionManagement />} />
              </Route>

              {/* 404 页面 */}
              <Route path="*" element={<Navigate to="/dashboard" replace />} />
            </Routes>
          </div>
        </Router>
      </ErrorBoundary>
    </ConfigProvider>
  );
}

export default App;
