const axios = require('axios');

console.log('🧪 开始全面功能测试验证...\n');

const baseURL = 'http://127.0.0.1:3001';

async function comprehensiveTest() {
    let testResults = {
        caseCreation: false,
        caseListDisplay: false,
        adminPermissions: false,
        positionFieldRemoved: false,
        responsibleSelection: false
    };

    try {
        console.log('='.repeat(60));
        console.log('🔐 1. 测试用户认证和权限');
        console.log('='.repeat(60));

        // 1. 管理员登录测试
        console.log('1️⃣ 测试管理员登录...');
        const adminLoginResponse = await axios.post(`${baseURL}/api/auth/login`, {
            username: 'admin',
            password: 'admin123'
        });

        if (!adminLoginResponse.data.success) {
            throw new Error('管理员登录失败');
        }

        const adminToken = adminLoginResponse.data.token;
        const adminHeaders = { 'Authorization': `Bearer ${adminToken}` };
        console.log('✅ 管理员登录成功');

        // 2. 普通用户登录测试
        console.log('2️⃣ 测试普通用户登录...');
        const userLoginResponse = await axios.post(`${baseURL}/api/auth/login`, {
            username: 'lawyer1',
            password: 'lawyer123'
        });

        if (!userLoginResponse.data.success) {
            throw new Error('普通用户登录失败');
        }

        const userToken = userLoginResponse.data.token;
        const userHeaders = { 'Authorization': `Bearer ${userToken}` };
        console.log('✅ 普通用户登录成功');

        console.log('\n' + '='.repeat(60));
        console.log('👥 2. 测试负责人管理功能（position字段移除验证）');
        console.log('='.repeat(60));

        // 3. 测试负责人列表（验证position字段已移除）
        console.log('3️⃣ 测试负责人列表（验证position字段移除）...');
        const responsiblesResponse = await axios.get(`${baseURL}/api/responsibles/active`, {
            headers: adminHeaders
        });

        if (responsiblesResponse.data.success) {
            const responsibles = responsiblesResponse.data.responsibles;
            console.log('✅ 获取负责人列表成功');
            console.log(`   负责人数量: ${responsibles.length}`);
            
            // 检查是否还有position字段
            const hasPosition = responsibles.some(r => r.position !== undefined);
            if (hasPosition) {
                console.log('❌ 警告: 负责人数据中仍包含position字段');
                testResults.positionFieldRemoved = false;
            } else {
                console.log('✅ 确认: position字段已成功移除');
                testResults.positionFieldRemoved = true;
                responsibles.forEach((r, index) => {
                    console.log(`   ${index + 1}. ${r.name} (${r.department || '无部门'})`);
                });
            }
        }

        console.log('\n' + '='.repeat(60));
        console.log('📋 3. 测试案件创建和管理功能');
        console.log('='.repeat(60));

        // 4. 测试案件创建功能
        console.log('4️⃣ 测试案件创建功能...');
        const caseData = {
            title: '全面测试案件',
            type: '合同纠纷',
            description: '这是一个用于全面测试的案件',
            priority: '高',
            owner_id: 1,
            client_name: '测试客户公司',
            client_contact: '13800138888',
            amount: 500000
        };

        const createResponse = await axios.post(`${baseURL}/api/cases`, caseData, {
            headers: adminHeaders
        });

        if (createResponse.data.success && createResponse.data.data.case) {
            console.log('✅ 案件创建成功');
            console.log('✅ 响应格式正确 (包含data.case)');
            testResults.caseCreation = true;
            
            const newCase = createResponse.data.data.case;
            console.log(`   案件ID: ${newCase.id}`);
            console.log(`   案件编号: ${newCase.case_no}`);
            console.log(`   案件标题: ${newCase.title}`);
            console.log(`   负责人ID: ${newCase.owner_id}`);
            console.log(`   客户名称: ${newCase.client_name}`);
            console.log(`   涉案金额: ${newCase.amount}`);
        } else {
            console.log('❌ 案件创建失败或响应格式错误');
            testResults.caseCreation = false;
        }

        // 5. 测试案件列表显示
        console.log('\n5️⃣ 测试案件列表显示...');
        const casesResponse = await axios.get(`${baseURL}/api/cases`, {
            headers: adminHeaders
        });

        if (casesResponse.data.success) {
            const cases = casesResponse.data.data.cases;
            console.log('✅ 获取案件列表成功');
            console.log(`   案件总数: ${cases.length}`);
            
            if (cases.length > 0) {
                console.log('✅ 案件列表不为空，创建的案件已显示');
                testResults.caseListDisplay = true;
                cases.forEach((c, index) => {
                    console.log(`   ${index + 1}. ${c.title} (${c.case_no}) - 状态: ${c.status} - 负责人ID: ${c.owner_id}`);
                });
            } else {
                console.log('❌ 案件列表为空，创建的案件未显示');
                testResults.caseListDisplay = false;
            }
        }

        console.log('\n' + '='.repeat(60));
        console.log('🔧 4. 测试管理员权限功能');
        console.log('='.repeat(60));

        // 6. 测试管理员修改案件负责人权限
        if (createResponse.data.success && createResponse.data.data.case) {
            console.log('6️⃣ 测试管理员修改案件负责人权限...');
            const caseId = createResponse.data.data.case.id;
            
            const assignResponse = await axios.post(`${baseURL}/api/cases/${caseId}/assign`, {
                owner_id: 2,
                remark: '管理员重新分配负责人'
            }, {
                headers: adminHeaders
            });

            if (assignResponse.data.success) {
                console.log('✅ 管理员成功修改案件负责人');
                console.log(`   原负责人ID: 1`);
                console.log(`   新负责人ID: ${assignResponse.data.data.case.owner_id}`);
                testResults.adminPermissions = true;
            } else {
                console.log('❌ 管理员修改案件负责人失败');
                testResults.adminPermissions = false;
            }
        }

        // 7. 测试普通用户权限
        console.log('\n7️⃣ 测试普通用户权限...');
        const userResponsiblesResponse = await axios.get(`${baseURL}/api/responsibles/active`, {
            headers: userHeaders
        });

        if (userResponsiblesResponse.data.success) {
            console.log('✅ 普通用户可以访问负责人列表（用于案件创建）');
            testResults.responsibleSelection = true;
        } else {
            console.log('❌ 普通用户无法访问负责人列表');
            testResults.responsibleSelection = false;
        }

        console.log('\n' + '='.repeat(60));
        console.log('📊 5. 测试结果汇总');
        console.log('='.repeat(60));

        const allTestsPassed = Object.values(testResults).every(result => result === true);
        
        console.log('测试项目详情:');
        console.log(`   ✅ 案件创建功能: ${testResults.caseCreation ? '通过' : '失败'}`);
        console.log(`   ✅ 案件列表显示: ${testResults.caseListDisplay ? '通过' : '失败'}`);
        console.log(`   ✅ 管理员权限: ${testResults.adminPermissions ? '通过' : '失败'}`);
        console.log(`   ✅ Position字段移除: ${testResults.positionFieldRemoved ? '通过' : '失败'}`);
        console.log(`   ✅ 负责人选择功能: ${testResults.responsibleSelection ? '通过' : '失败'}`);

        if (allTestsPassed) {
            console.log('\n🎉 所有测试通过！修复工作完成！');
        } else {
            console.log('\n⚠️  部分测试未通过，需要进一步检查');
        }

        return testResults;

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.response) {
            console.error('   响应状态:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
        return testResults;
    }
}

// 运行全面测试
comprehensiveTest()
    .then((results) => {
        console.log('\n📋 最终测试报告已生成');
        process.exit(0);
    })
    .catch((error) => {
        console.error('\n❌ 测试执行失败:', error);
        process.exit(1);
    });
