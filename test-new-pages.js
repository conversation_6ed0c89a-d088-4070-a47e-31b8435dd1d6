/**
 * 新开发页面功能测试脚本
 * 测试文件管理、通知消息、统计报表页面的API连接
 */

const axios = require('axios');

// 测试配置
const API_BASE = 'http://127.0.0.1:3001/api';
const FRONTEND_BASE = 'http://localhost:3002';

// 测试结果记录
const testResults = {
    backend: {},
    frontend: {},
    integration: {},
    summary: {}
};

// 颜色输出函数
const colors = {
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    blue: (text) => `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => `\x1b[36m${text}\x1b[0m`
};

// 记录测试结果
function recordResult(category, test, success, message) {
    if (!testResults[category]) testResults[category] = {};
    testResults[category][test] = { success, message };
    
    const color = success ? colors.green : colors.red;
    const icon = success ? '✅' : '❌';
    console.log(color(`${icon} ${test}: ${message}`));
}

// 1. 测试后端API连接
async function testBackendAPIs() {
    console.log(colors.cyan('\n🔍 1. 测试后端API连接'));
    
    // 测试健康检查
    try {
        const response = await axios.get(`${API_BASE.replace('/api', '')}/health`);
        if (response.status === 200) {
            recordResult('backend', '健康检查', true, '后端服务器运行正常');
        }
    } catch (error) {
        recordResult('backend', '健康检查', false, '后端服务器连接失败');
        return false;
    }

    // 测试登录获取token
    let token = null;
    try {
        const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
            username: 'admin',
            password: 'admin123'
        });
        
        if (loginResponse.data.success && loginResponse.data.token) {
            token = loginResponse.data.token;
            recordResult('backend', '用户登录', true, '登录成功，获取到token');
        } else {
            recordResult('backend', '用户登录', false, '登录失败');
            return false;
        }
    } catch (error) {
        recordResult('backend', '用户登录', false, `登录错误: ${error.message}`);
        return false;
    }

    // 测试统计API
    try {
        const statsResponse = await axios.get(`${API_BASE}/stats/overview`, {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        if (statsResponse.status === 200) {
            recordResult('backend', '统计API', true, '统计数据获取成功');
        }
    } catch (error) {
        recordResult('backend', '统计API', false, `统计API错误: ${error.message}`);
    }

    // 测试通知API
    try {
        const notificationResponse = await axios.get(`${API_BASE}/notifications`, {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        if (notificationResponse.status === 200) {
            recordResult('backend', '通知API', true, '通知数据获取成功');
        }
    } catch (error) {
        recordResult('backend', '通知API', false, `通知API错误: ${error.message}`);
    }

    // 测试案件API（用于文件管理）
    try {
        const casesResponse = await axios.get(`${API_BASE}/cases`, {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        if (casesResponse.status === 200) {
            recordResult('backend', '案件API', true, '案件数据获取成功');
        }
    } catch (error) {
        recordResult('backend', '案件API', false, `案件API错误: ${error.message}`);
    }

    return token;
}

// 2. 测试前端页面访问
async function testFrontendPages() {
    console.log(colors.cyan('\n🔍 2. 测试前端页面访问'));
    
    // 测试前端服务器
    try {
        const response = await axios.get(FRONTEND_BASE);
        if (response.status === 200) {
            recordResult('frontend', '前端服务器', true, '前端服务器运行正常');
        }
    } catch (error) {
        recordResult('frontend', '前端服务器', false, '前端服务器连接失败');
        return false;
    }

    // 检查前端页面组件
    const pages = [
        { name: '文件管理页面', path: 'src/pages/Files/FileManagement.jsx' },
        { name: '通知消息页面', path: 'src/pages/Notifications/Notifications.jsx' },
        { name: '统计报表页面', path: 'src/pages/Statistics/Statistics.jsx' }
    ];

    const fs = require('fs');
    const path = require('path');

    pages.forEach(page => {
        const filePath = path.join(__dirname, 'frontend', page.path);
        if (fs.existsSync(filePath)) {
            const content = fs.readFileSync(filePath, 'utf8');
            if (content.includes('export default') && content.length > 1000) {
                recordResult('frontend', page.name, true, '页面组件已实现');
            } else {
                recordResult('frontend', page.name, false, '页面组件不完整');
            }
        } else {
            recordResult('frontend', page.name, false, '页面文件不存在');
        }
    });

    return true;
}

// 3. 测试前后端集成
async function testIntegration(token) {
    console.log(colors.cyan('\n🔍 3. 测试前后端集成'));
    
    if (!token) {
        recordResult('integration', '集成测试', false, '缺少认证token');
        return;
    }

    // 测试CORS配置
    try {
        const response = await axios.get(`${API_BASE}/stats/overview`, {
            headers: { 
                Authorization: `Bearer ${token}`,
                Origin: FRONTEND_BASE
            }
        });
        
        if (response.status === 200) {
            recordResult('integration', 'CORS配置', true, '跨域请求正常');
        }
    } catch (error) {
        recordResult('integration', 'CORS配置', false, `跨域请求失败: ${error.message}`);
    }

    // 测试API响应格式
    try {
        const response = await axios.get(`${API_BASE}/notifications`, {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        if (response.data && typeof response.data === 'object') {
            recordResult('integration', 'API响应格式', true, 'API响应格式正确');
        } else {
            recordResult('integration', 'API响应格式', false, 'API响应格式异常');
        }
    } catch (error) {
        recordResult('integration', 'API响应格式', false, `API响应错误: ${error.message}`);
    }
}

// 4. 生成测试报告
function generateReport() {
    console.log(colors.cyan('\n📊 测试结果汇总'));
    
    let totalTests = 0;
    let passedTests = 0;
    
    Object.keys(testResults).forEach(category => {
        if (category === 'summary') return;
        
        console.log(colors.blue(`\n${category.toUpperCase()}:`));
        Object.keys(testResults[category]).forEach(test => {
            const result = testResults[category][test];
            totalTests++;
            if (result.success) passedTests++;
            
            const color = result.success ? colors.green : colors.red;
            console.log(`  ${color(result.success ? '✅' : '❌')} ${test}: ${result.message}`);
        });
    });
    
    const successRate = Math.round((passedTests / totalTests) * 100);
    console.log(colors.cyan(`\n📈 总体测试结果:`));
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过测试: ${colors.green(passedTests)}`);
    console.log(`失败测试: ${colors.red(totalTests - passedTests)}`);
    console.log(`成功率: ${successRate >= 80 ? colors.green(successRate + '%') : colors.yellow(successRate + '%')}`);
    
    if (successRate >= 80) {
        console.log(colors.green('\n🎉 新页面功能测试通过！系统运行良好。'));
    } else {
        console.log(colors.yellow('\n⚠️  部分功能存在问题，需要进一步检查。'));
    }
}

// 主测试函数
async function runTests() {
    console.log(colors.cyan('🚀 开始新页面功能测试\n'));
    
    try {
        const token = await testBackendAPIs();
        await testFrontendPages();
        await testIntegration(token);
        generateReport();
    } catch (error) {
        console.error(colors.red(`测试过程中发生错误: ${error.message}`));
    }
}

// 运行测试
runTests();
