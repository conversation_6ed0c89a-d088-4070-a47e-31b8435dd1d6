# 案件管理平台主要接口规范草案

## 用户与权限
- POST   /api/login           用户登录
- POST   /api/logout          用户登出
- GET    /api/user/profile    获取当前用户信息
- GET    /api/user/list       用户列表（管理员）
- POST   /api/user            新增用户
- PUT    /api/user/:id        编辑用户
- DELETE /api/user/:id        删除用户

## 案件管理
- GET    /api/case/list       案件列表（支持筛选、分页）
- POST   /api/case            新建案件
- GET    /api/case/:id        获取案件详情
- PUT    /api/case/:id        编辑案件
- DELETE /api/case/:id        删除案件
- POST   /api/case/:id/assign 分配负责人/协作人
- POST   /api/case/:id/flow   案件流转/状态变更

## 案件归档与文档
- POST   /api/case/:id/archive   案件归档
- GET    /api/case/:id/archive   获取归档信息
- POST   /api/case/:id/upload    上传归档文档
- GET    /api/case/:id/files     获取案件相关文档列表
- GET    /api/case/:id/file/:fid 下载案件文档

## 消息与通知
- GET    /api/notify/list        消息列表
- POST   /api/notify/read        标记消息为已读

## 统计与报表
- GET    /api/stat/overview      统计总览
- GET    /api/stat/case          案件相关统计

## 系统设置
- GET    /api/config/base        获取基础配置
- PUT    /api/config/base        修改基础配置

---
接口均采用RESTful风格，需鉴权（如JWT），支持分页、筛选、排序等常见参数。 