const express = require('express');
const cors = require('cors');
const path = require('path');

console.log('🔍 Starting working server...');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '.env') });

const app = express();
const PORT = 3001; // 使用固定端口避免环境变量问题

console.log('✅ Using PORT:', PORT);

// 中间件配置
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

console.log('✅ Middleware configured');

// 静态文件服务（用于文件上传）
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 基础路由
app.get('/', (req, res) => {
    console.log('📍 Root route accessed');
    res.json({
        message: '法务案件管理平台 API',
        version: '1.0.0',
        status: 'running',
        port: PORT
    });
});

// 健康检查
app.get('/health', async (req, res) => {
    console.log('📍 Health check accessed');
    try {
        // 尝试连接数据库
        const sequelize = require('./config/database');
        await sequelize.authenticate();
        res.json({
            status: 'healthy',
            database: 'connected',
            timestamp: new Date().toISOString(),
            port: PORT
        });
    } catch (error) {
        res.status(500).json({
            status: 'unhealthy',
            database: 'disconnected',
            error: error.message,
            timestamp: new Date().toISOString(),
            port: PORT
        });
    }
});

// API 路由 - 认证相关
app.post('/api/auth/login', (req, res) => {
    console.log('📍 Login attempt:', req.body);
    // 临时的登录响应
    res.json({
        success: true,
        message: '登录成功（临时响应）',
        token: 'temporary-token-' + Date.now(),
        user: {
            id: 1,
            username: req.body.username || 'testuser',
            name: '测试用户',
            role: 'admin'
        }
    });
});

app.post('/api/auth/logout', (req, res) => {
    console.log('📍 Logout request');
    res.json({
        success: true,
        message: '登出成功'
    });
});

// API 路由 - 案件相关
app.get('/api/cases', (req, res) => {
    console.log('📍 Get cases request');
    res.json({
        success: true,
        data: [],
        message: '案件列表（临时为空）'
    });
});

app.post('/api/cases', (req, res) => {
    console.log('📍 Create case request:', req.body);
    res.json({
        success: true,
        data: {
            id: Date.now(),
            ...req.body,
            created_at: new Date().toISOString()
        },
        message: '案件创建成功'
    });
});

// API 路由 - 文件相关
app.get('/api/files', (req, res) => {
    console.log('📍 Get files request');
    res.json({
        success: true,
        data: [],
        message: '文件列表（临时为空）'
    });
});

// API 路由 - 通知相关
app.get('/api/notifications', (req, res) => {
    console.log('📍 Get notifications request');
    res.json({
        success: true,
        data: [],
        message: '通知列表（临时为空）'
    });
});

// API 路由 - 统计相关
app.get('/api/stats', (req, res) => {
    console.log('📍 Get stats request');
    res.json({
        success: true,
        data: {
            totalCases: 0,
            activeCases: 0,
            closedCases: 0,
            pendingCases: 0
        },
        message: '统计数据（临时数据）'
    });
});

// 404 处理
app.use('*', (req, res) => {
    console.log('📍 404 route:', req.originalUrl);
    res.status(404).json({
        error: 'API endpoint not found',
        path: req.originalUrl,
        method: req.method
    });
});

// 全局错误处理
app.use((error, req, res, next) => {
    console.error('❌ Global error handler:', error);
    res.status(error.status || 500).json({
        error: error.message || 'Internal server error'
    });
});

// 启动服务器
console.log('🚀 Starting server...');
const server = app.listen(PORT, '127.0.0.1', () => {
    console.log(`🚀 Working server is running on port ${PORT}`);
    console.log(`📍 API URL: http://127.0.0.1:${PORT}`);
    console.log(`🏥 Health check: http://127.0.0.1:${PORT}/health`);
    console.log(`🔑 Login API: http://127.0.0.1:${PORT}/api/auth/login`);
});

server.on('error', (error) => {
    console.error('❌ Server error:', error);
});

server.on('listening', () => {
    console.log('✅ Server is listening on', server.address());
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('🔄 SIGTERM received');
    server.close(() => process.exit(0));
});

process.on('SIGINT', () => {
    console.log('🔄 SIGINT received');
    server.close(() => process.exit(0));
});

console.log('✅ Working server setup complete');
