const { Case } = require('../models');
const CaseOperationLog = require('../models/CaseOperationLog');
const { Op } = require('sequelize');

/**
 * 清理超过指定天数的已删除案件
 * @param {number} days - 保留天数，默认30天
 */
async function cleanupDeletedCases(days = 30) {
    try {
        console.log(`🗑️ 开始清理超过${days}天的已删除案件...`);

        // 计算截止日期
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);

        // 查找需要永久删除的案件
        const casesToDelete = await Case.findAll({
            where: {
                deleted_at: {
                    [Op.ne]: null,
                    [Op.lt]: cutoffDate
                }
            },
            attributes: ['id', 'case_no', 'title', 'deleted_at']
        });

        if (casesToDelete.length === 0) {
            console.log('✅ 没有需要清理的案件');
            return { cleaned: 0, errors: [] };
        }

        console.log(`📋 找到${casesToDelete.length}个需要清理的案件`);

        let cleaned = 0;
        const errors = [];

        for (const caseRecord of casesToDelete) {
            try {
                // 记录永久删除操作
                await CaseOperationLog.create({
                    case_id: caseRecord.id,
                    operation_type: '永久删除',
                    operator_id: null, // 系统自动操作
                    operation_detail: `系统自动清理：案件删除超过${days}天`,
                    old_data: {
                        title: caseRecord.title,
                        case_no: caseRecord.case_no,
                        deleted_at: caseRecord.deleted_at
                    },
                    ip_address: 'system',
                    user_agent: 'cleanup-task'
                });

                // 永久删除案件
                await caseRecord.destroy();

                console.log(`✅ 已清理案件: ${caseRecord.case_no} - ${caseRecord.title}`);
                cleaned++;

            } catch (error) {
                console.error(`❌ 清理案件失败 ${caseRecord.case_no}:`, error.message);
                errors.push({
                    case_no: caseRecord.case_no,
                    error: error.message
                });
            }
        }

        console.log(`🎉 清理完成: 成功清理${cleaned}个案件，失败${errors.length}个`);

        return { cleaned, errors };

    } catch (error) {
        console.error('❌ 清理任务执行失败:', error);
        throw error;
    }
}

/**
 * 清理超过指定天数的操作日志
 * @param {number} days - 保留天数，默认90天
 */
async function cleanupOperationLogs(days = 90) {
    try {
        console.log(`🗑️ 开始清理超过${days}天的操作日志...`);

        // 计算截止日期
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);

        // 删除超过指定天数的操作日志
        const result = await CaseOperationLog.destroy({
            where: {
                created_at: {
                    [Op.lt]: cutoffDate
                }
            }
        });

        console.log(`✅ 已清理${result}条操作日志`);

        return result;

    } catch (error) {
        console.error('❌ 清理操作日志失败:', error);
        throw error;
    }
}

/**
 * 执行完整的清理任务
 */
async function runCleanupTask() {
    try {
        console.log('🚀 开始执行清理任务...');

        // 清理已删除的案件（30天）
        const caseResult = await cleanupDeletedCases(30);

        // 清理操作日志（90天）
        const logResult = await cleanupOperationLogs(90);

        console.log('🎉 清理任务完成');

        return {
            cases: caseResult,
            logs: logResult,
            timestamp: new Date().toISOString()
        };

    } catch (error) {
        console.error('❌ 清理任务失败:', error);
        throw error;
    }
}

/**
 * 启动定时清理任务
 * @param {number} intervalHours - 执行间隔（小时），默认24小时
 */
function startCleanupSchedule(intervalHours = 24) {
    console.log(`⏰ 启动定时清理任务，每${intervalHours}小时执行一次`);

    // 延迟5秒后执行第一次清理任务，确保数据库迁移完成
    setTimeout(() => {
        runCleanupTask().catch(error => {
            console.error('初始清理任务失败:', error.message);
            // 如果是数据库结构问题，给出提示
            if (error.message.includes('Unknown column')) {
                console.log('💡 提示：请确保数据库迁移已完成，或手动执行: node utils/migrate-database.js');
            }
        });
    }, 5000);

    // 设置定时任务
    const interval = intervalHours * 60 * 60 * 1000; // 转换为毫秒
    setInterval(() => {
        runCleanupTask().catch(error => {
            console.error('定时清理任务失败:', error.message);
        });
    }, interval);
}

module.exports = {
    cleanupDeletedCases,
    cleanupOperationLogs,
    runCleanupTask,
    startCleanupSchedule
};
