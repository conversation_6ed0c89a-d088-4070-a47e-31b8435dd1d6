import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  Table,
  Tag,
  Button,
  Space,
  Alert,
  Spin,
} from 'antd';
import {
  FileTextOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

import { request } from '../../services/api';
import { casesAPI, CASE_CONSTANTS } from '../../services/cases';
import { getUser } from '../../utils/auth';

const { Title } = Typography;

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [statsData, setStatsData] = useState(null);
  const [recentCases, setRecentCases] = useState([]);
  const [user, setUser] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const userData = getUser();
    setUser(userData);
    loadDashboardData();
  }, []);

  // 加载仪表板数据
  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // 并行请求统计数据和最近案件
      const [statsResponse, casesResponse] = await Promise.all([
        request.get('/stats/overview'),
        casesAPI.getCases({ page: 1, limit: 5, sort_by: 'created_at', sort_order: 'DESC' })
      ]);

      setStatsData(statsResponse.data);
      // 后端返回的数据结构是 { success: true, data: [案件数组], pagination: {...} }
      setRecentCases(casesResponse.data.data || []);
    } catch (error) {
      console.error('加载仪表板数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 最近案件表格列配置
  const recentCasesColumns = [
    {
      title: '案件编号',
      dataIndex: 'case_no',
      key: 'case_no',
      width: 140,
      render: (text) => (
        <span style={{ fontFamily: 'monospace', fontSize: '12px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '案件标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type) => (
        <Tag color="blue">{type}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Tag color={CASE_CONSTANTS.STATUS_COLORS[status]}>
          {status}
        </Tag>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority) => (
        <Tag color={CASE_CONSTANTS.PRIORITY_COLORS[priority]}>
          {priority}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, record) => (
        <Button
          type="link"
          size="small"
          icon={<EyeOutlined />}
          onClick={() => navigate(`/cases/${record.id}`)}
        >
          查看
        </Button>
      ),
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          仪表板
        </Title>
        <p style={{ color: '#666', marginTop: 8 }}>
          欢迎回来，{user?.real_name || user?.username}！
        </p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总案件数"
              value={statsData?.overview?.totalCases || 0}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="进行中"
              value={statsData?.casesByStatus?.['处理中'] || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已完成"
              value={statsData?.casesByStatus?.['已结案'] || 0}
              prefix={<PlusOutlined />}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="待处理"
              value={statsData?.casesByStatus?.['待处理'] || 0}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 状态分布和快速操作 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        {/* 案件状态分布 */}
        <Col xs={24} lg={16}>
          <Card title="案件状态分布" size="small">
            <Row gutter={16}>
              {Object.entries(statsData?.casesByStatus || {}).map(([status, count]) => (
                <Col span={6} key={status}>
                  <Statistic
                    title={status}
                    value={count}
                    valueStyle={{
                      fontSize: '20px',
                      color: CASE_CONSTANTS.STATUS_COLORS[status]
                    }}
                  />
                </Col>
              ))}
            </Row>
          </Card>
        </Col>

        {/* 快速操作 */}
        <Col xs={24} lg={8}>
          <Card title="快速操作" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                type="primary"
                block
                icon={<PlusOutlined />}
                onClick={() => navigate('/cases/create')}
              >
                创建新案件
              </Button>

              <Button
                block
                onClick={() => navigate('/cases')}
              >
                查看所有案件
              </Button>

              <Button
                block
                onClick={() => navigate('/statistics')}
              >
                查看统计报表
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 最近案件 */}
      <Card
        title="最近案件"
        size="small"
        extra={
          <Button
            type="link"
            onClick={() => navigate('/cases')}
          >
            查看全部
          </Button>
        }
      >
        {recentCases.length > 0 ? (
          <Table
            columns={recentCasesColumns}
            dataSource={recentCases}
            rowKey="id"
            pagination={false}
            size="small"
          />
        ) : (
          <Alert
            message="暂无案件数据"
            description="点击上方「创建新案件」按钮开始使用系统"
            type="info"
            showIcon
          />
        )}
      </Card>
    </div>
  );
};

export default Dashboard;
