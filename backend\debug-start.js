const express = require('express');
const cors = require('cors');
const path = require('path');

console.log('🔍 Starting debug mode...');

// 加载环境变量
try {
    require('dotenv').config({ path: path.join(__dirname, '.env') });
    console.log('✅ Environment variables loaded');
    console.log('📊 Environment check:');
    console.log('  - DB_HOST:', process.env.DB_HOST);
    console.log('  - DB_PORT:', process.env.DB_PORT);
    console.log('  - DB_NAME:', process.env.DB_NAME);
    console.log('  - DB_USER:', process.env.DB_USER);
    console.log('  - PORT:', process.env.PORT);
} catch (error) {
    console.error('❌ Error loading environment variables:', error);
    process.exit(1);
}

// 测试数据库连接
console.log('🔍 Testing database connection...');
let sequelize;
try {
    sequelize = require('./config/database');
    console.log('✅ Database config loaded');
} catch (error) {
    console.error('❌ Error loading database config:', error);
    process.exit(1);
}

// 测试数据库连接
async function testDatabase() {
    try {
        await sequelize.authenticate();
        console.log('✅ Database connection successful');
        return true;
    } catch (error) {
        console.error('❌ Database connection failed:', error);
        return false;
    }
}

// 创建 Express 应用
console.log('🔍 Creating Express app...');
const app = express();
const PORT = process.env.PORT || 3000;

// 基础中间件
try {
    app.use(cors());
    app.use(express.json({ limit: '10mb' }));
    app.use(express.urlencoded({ extended: true, limit: '10mb' }));
    console.log('✅ Middleware configured');
} catch (error) {
    console.error('❌ Error configuring middleware:', error);
    process.exit(1);
}

// 基础路由
app.get('/', (req, res) => {
    res.json({
        message: '法务案件管理平台 API - Debug Mode',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString()
    });
});

app.get('/health', async (req, res) => {
    try {
        await sequelize.authenticate();
        res.json({
            status: 'healthy',
            database: 'connected',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            status: 'unhealthy',
            database: 'disconnected',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// 启动服务器
async function startServer() {
    try {
        console.log('🔍 Testing database connection...');
        const dbConnected = await testDatabase();
        
        if (!dbConnected) {
            console.log('⚠️  Database connection failed, but continuing...');
        }

        console.log('🔍 Starting HTTP server...');
        const server = app.listen(PORT, () => {
            console.log('🚀 Server started successfully!');
            console.log(`📍 Server URL: http://localhost:${PORT}`);
            console.log(`🏥 Health check: http://localhost:${PORT}/health`);
            console.log(`🔍 Test with: curl http://localhost:${PORT}/health`);
        });

        server.on('error', (error) => {
            console.error('❌ Server error:', error);
            if (error.code === 'EADDRINUSE') {
                console.error(`❌ Port ${PORT} is already in use`);
            }
        });

        // 优雅关闭处理
        process.on('SIGTERM', () => {
            console.log('🔄 SIGTERM received, shutting down gracefully...');
            server.close(() => {
                console.log('✅ Server closed');
                process.exit(0);
            });
        });

        process.on('SIGINT', () => {
            console.log('🔄 SIGINT received, shutting down gracefully...');
            server.close(() => {
                console.log('✅ Server closed');
                process.exit(0);
            });
        });

    } catch (error) {
        console.error('❌ Failed to start server:', error);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// 全局错误处理
process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

console.log('🚀 Starting server...');
startServer();
