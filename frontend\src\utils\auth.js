// Token 存储键名
const TOKEN_KEY = 'sie_dispute_token';
const USER_KEY = 'sie_dispute_user';

/**
 * 获取存储的 token
 * @returns {string|null} token
 */
export const getToken = () => {
  return localStorage.getItem(TOKEN_KEY);
};

/**
 * 设置 token
 * @param {string} token - JWT token
 */
export const setToken = (token) => {
  localStorage.setItem(TOKEN_KEY, token);
};

/**
 * 移除 token
 */
export const removeToken = () => {
  localStorage.removeItem(TOKEN_KEY);
};

/**
 * 获取存储的用户信息
 * @returns {Object|null} 用户信息
 */
export const getUser = () => {
  const userStr = localStorage.getItem(USER_KEY);
  try {
    return userStr ? JSON.parse(userStr) : null;
  } catch (error) {
    console.error('解析用户信息失败:', error);
    return null;
  }
};

/**
 * 设置用户信息
 * @param {Object} user - 用户信息对象
 */
export const setUser = (user) => {
  localStorage.setItem(USER_KEY, JSON.stringify(user));
};

/**
 * 移除用户信息
 */
export const removeUser = () => {
  localStorage.removeItem(USER_KEY);
};

/**
 * 清除所有认证信息
 */
export const clearAuth = () => {
  removeToken();
  removeUser();
};

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export const isAuthenticated = () => {
  const token = getToken();
  const user = getUser();
  return !!(token && user);
};

/**
 * 检查用户是否有指定角色
 * @param {string|Array} roles - 角色名称或角色数组
 * @returns {boolean} 是否有权限
 */
export const hasRole = (roles) => {
  const user = getUser();
  if (!user || !user.roles) {
    return false;
  }
  
  const userRoles = user.roles.map(role => role.name);
  const requiredRoles = Array.isArray(roles) ? roles : [roles];
  
  return requiredRoles.some(role => userRoles.includes(role));
};

/**
 * 检查用户是否为管理员
 * @returns {boolean} 是否为管理员
 */
export const isAdmin = () => {
  return hasRole('admin');
};

/**
 * 检查用户是否为法务人员
 * @returns {boolean} 是否为法务人员
 */
export const isLawyer = () => {
  return hasRole(['admin', 'lawyer']);
};

/**
 * 从 JWT token 中解析用户信息（不验证签名）
 * @param {string} token - JWT token
 * @returns {Object|null} 解析的载荷
 */
export const parseToken = (token) => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('解析 token 失败:', error);
    return null;
  }
};

/**
 * 检查 token 是否即将过期（1小时内）
 * @param {string} token - JWT token
 * @returns {boolean} 是否即将过期
 */
export const isTokenExpiringSoon = (token) => {
  const payload = parseToken(token);
  if (!payload || !payload.exp) {
    return true;
  }
  
  const expirationTime = payload.exp * 1000; // 转换为毫秒
  const currentTime = Date.now();
  const oneHour = 60 * 60 * 1000; // 1小时的毫秒数
  
  return (expirationTime - currentTime) < oneHour;
};
