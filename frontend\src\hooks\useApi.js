import { useState, useCallback, useRef, useEffect } from 'react';
import { message } from 'antd';
import errorHandler from '../utils/errorHandler';

/**
 * API请求状态管理Hook
 * 提供统一的API调用、状态管理、错误处理、缓存等功能
 */
export const useApi = (options = {}) => {
  const {
    immediate = false, // 是否立即执行
    cacheKey = null, // 缓存键
    cacheTTL = 5 * 60 * 1000, // 缓存时间（5分钟）
    retryable = true, // 是否可重试
    showErrorMessage = true,
    showSuccessMessage = false,
    onSuccess = null,
    onError = null,
  } = options;

  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastFetch, setLastFetch] = useState(null);
  
  const abortControllerRef = useRef(null);
  const cacheRef = useRef(new Map());

  // 检查缓存是否有效
  const isCacheValid = useCallback((cacheKey) => {
    if (!cacheKey) return false;
    
    const cached = cacheRef.current.get(cacheKey);
    if (!cached) return false;
    
    return Date.now() - cached.timestamp < cacheTTL;
  }, [cacheTTL]);

  // 获取缓存数据
  const getCachedData = useCallback((cacheKey) => {
    if (!cacheKey || !isCacheValid(cacheKey)) return null;
    return cacheRef.current.get(cacheKey)?.data;
  }, [isCacheValid]);

  // 设置缓存数据
  const setCachedData = useCallback((cacheKey, data) => {
    if (!cacheKey) return;
    
    cacheRef.current.set(cacheKey, {
      data,
      timestamp: Date.now(),
    });
  }, []);

  // 清除缓存
  const clearCache = useCallback((key = null) => {
    if (key) {
      cacheRef.current.delete(key);
    } else {
      cacheRef.current.clear();
    }
  }, []);

  // 取消请求
  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  // 执行API请求
  const execute = useCallback(async (apiFunction, ...args) => {
    // 检查缓存
    if (cacheKey) {
      const cachedData = getCachedData(cacheKey);
      if (cachedData) {
        setData(cachedData);
        setError(null);
        return cachedData;
      }
    }

    // 取消之前的请求
    cancel();

    // 创建新的AbortController
    abortControllerRef.current = new AbortController();

    setLoading(true);
    setError(null);

    try {
      const response = await apiFunction(...args, {
        signal: abortControllerRef.current.signal,
      });

      const responseData = response.data;
      setData(responseData);
      setLastFetch(Date.now());

      // 缓存数据
      if (cacheKey) {
        setCachedData(cacheKey, responseData);
      }

      if (showSuccessMessage) {
        message.success('操作成功');
      }

      if (onSuccess) {
        onSuccess(responseData);
      }

      return responseData;
    } catch (err) {
      // 忽略取消的请求
      if (err.name === 'AbortError') {
        return;
      }

      setError(err);

      // 使用全局错误处理器
      errorHandler.handleApiError(err, {
        showMessage: showErrorMessage,
        retryable,
        onRetry: () => execute(apiFunction, ...args),
        retryKey: `api_${cacheKey || Date.now()}`,
      });

      if (onError) {
        onError(err);
      }

      throw err;
    } finally {
      setLoading(false);
      abortControllerRef.current = null;
    }
  }, [
    cacheKey,
    getCachedData,
    setCachedData,
    cancel,
    showErrorMessage,
    showSuccessMessage,
    retryable,
    onSuccess,
    onError,
  ]);

  // 重新获取数据
  const refetch = useCallback((apiFunction, ...args) => {
    // 清除缓存
    if (cacheKey) {
      clearCache(cacheKey);
    }
    return execute(apiFunction, ...args);
  }, [execute, clearCache, cacheKey]);

  // 重置状态
  const reset = useCallback(() => {
    cancel();
    setData(null);
    setLoading(false);
    setError(null);
    setLastFetch(null);
  }, [cancel]);

  // 组件卸载时取消请求
  useEffect(() => {
    return () => {
      cancel();
    };
  }, [cancel]);

  return {
    data,
    loading,
    error,
    lastFetch,
    execute,
    refetch,
    reset,
    cancel,
    clearCache,
    
    // 状态检查
    isSuccess: !loading && !error && data !== null,
    isError: !loading && error !== null,
    isEmpty: !loading && !error && data === null,
  };
};

/**
 * 分页API Hook
 * 专门处理分页数据的API请求
 */
export const usePaginatedApi = (options = {}) => {
  const {
    initialPage = 1,
    initialPageSize = 10,
    ...apiOptions
  } = options;

  const [pagination, setPagination] = useState({
    current: initialPage,
    pageSize: initialPageSize,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  });

  const api = useApi(apiOptions);

  // 执行分页请求
  const execute = useCallback(async (apiFunction, params = {}) => {
    const queryParams = {
      page: pagination.current,
      limit: pagination.pageSize,
      ...params,
    };

    const result = await api.execute(apiFunction, queryParams);

    // 更新分页信息
    if (result && typeof result === 'object') {
      setPagination(prev => ({
        ...prev,
        total: result.total || 0,
        current: result.page || prev.current,
      }));
    }

    return result;
  }, [api, pagination.current, pagination.pageSize]);

  // 处理分页变化
  const handleTableChange = useCallback((paginationConfig, filters, sorter, apiFunction, extraParams = {}) => {
    const newPagination = {
      ...pagination,
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
    };

    setPagination(newPagination);

    const params = {
      page: newPagination.current,
      limit: newPagination.pageSize,
      ...extraParams,
    };

    // 处理排序
    if (sorter.field) {
      params.sort_by = sorter.field;
      params.sort_order = sorter.order === 'ascend' ? 'asc' : 'desc';
    }

    // 处理过滤器
    if (filters) {
      Object.keys(filters).forEach(key => {
        if (filters[key] && filters[key].length > 0) {
          params[key] = filters[key];
        }
      });
    }

    return api.execute(apiFunction, params);
  }, [api, pagination]);

  // 重置分页
  const resetPagination = useCallback(() => {
    setPagination(prev => ({
      ...prev,
      current: initialPage,
      pageSize: initialPageSize,
      total: 0,
    }));
  }, [initialPage, initialPageSize]);

  return {
    ...api,
    pagination,
    setPagination,
    execute,
    handleTableChange,
    resetPagination,
  };
};

/**
 * 表单API Hook
 * 专门处理表单提交的API请求
 */
export const useFormApi = (options = {}) => {
  const {
    resetOnSuccess = false,
    validateOnSubmit = true,
    ...apiOptions
  } = options;

  const api = useApi({
    showSuccessMessage: true,
    ...apiOptions,
  });

  // 提交表单
  const submit = useCallback(async (form, apiFunction, transformData = null) => {
    if (validateOnSubmit) {
      try {
        await form.validateFields();
      } catch (error) {
        message.error('请检查表单输入');
        return;
      }
    }

    const formData = form.getFieldsValue();
    const submitData = transformData ? transformData(formData) : formData;

    const result = await api.execute(apiFunction, submitData);

    if (resetOnSuccess && result) {
      form.resetFields();
    }

    return result;
  }, [api, validateOnSubmit, resetOnSuccess]);

  return {
    ...api,
    submit,
  };
};

export default useApi;
