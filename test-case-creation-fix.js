const axios = require('axios');

console.log('🧪 测试案件创建功能修复...\n');

const baseURL = 'http://127.0.0.1:3001';

async function testCaseCreationFix() {
    try {
        // 1. 测试登录
        console.log('1️⃣ 测试管理员登录...');
        const loginResponse = await axios.post(`${baseURL}/api/auth/login`, {
            username: 'admin',
            password: 'admin123'
        });

        if (!loginResponse.data.success) {
            throw new Error('登录失败');
        }

        const token = loginResponse.data.token;
        const authHeaders = { 'Authorization': `Bearer ${token}` };
        console.log('✅ 管理员登录成功');

        // 2. 测试获取负责人列表（检查是否移除了position字段）
        console.log('\n2️⃣ 测试负责人列表（检查position字段移除）...');
        const responsiblesResponse = await axios.get(`${baseURL}/api/responsibles/active`, {
            headers: authHeaders
        });

        if (responsiblesResponse.data.success) {
            const responsibles = responsiblesResponse.data.responsibles;
            console.log('✅ 获取负责人列表成功');
            console.log(`   负责人数量: ${responsibles.length}`);
            
            // 检查是否还有position字段
            const hasPosition = responsibles.some(r => r.position !== undefined);
            if (hasPosition) {
                console.log('⚠️  警告: 负责人数据中仍包含position字段');
                responsibles.forEach(r => {
                    if (r.position) {
                        console.log(`   - ${r.name}: ${r.position}`);
                    }
                });
            } else {
                console.log('✅ 确认: position字段已成功移除');
                responsibles.forEach(r => {
                    console.log(`   - ${r.name} (${r.department})`);
                });
            }
        }

        // 3. 测试案件创建（检查响应格式）
        console.log('\n3️⃣ 测试案件创建功能...');
        const caseData = {
            title: '测试案件 - 修复验证',
            type: '合同纠纷',
            description: '这是一个用于验证修复效果的测试案件',
            priority: '高',
            owner_id: 1,
            client_name: '测试客户',
            client_contact: '13800138000',
            amount: 100000
        };

        const createResponse = await axios.post(`${baseURL}/api/cases`, caseData, {
            headers: authHeaders
        });

        if (createResponse.data.success && createResponse.data.data.case) {
            console.log('✅ 案件创建成功');
            console.log('✅ 响应格式正确 (包含data.case)');
            const newCase = createResponse.data.data.case;
            console.log(`   案件ID: ${newCase.id}`);
            console.log(`   案件编号: ${newCase.case_no}`);
            console.log(`   案件标题: ${newCase.title}`);
            console.log(`   负责人ID: ${newCase.owner_id}`);
        } else {
            console.log('❌ 案件创建失败或响应格式错误');
            console.log('   响应数据:', JSON.stringify(createResponse.data, null, 2));
        }

        // 4. 测试案件列表（检查是否显示新创建的案件）
        console.log('\n4️⃣ 测试案件列表显示...');
        const casesResponse = await axios.get(`${baseURL}/api/cases`, {
            headers: authHeaders
        });

        if (casesResponse.data.success) {
            const cases = casesResponse.data.data.cases;
            console.log('✅ 获取案件列表成功');
            console.log(`   案件总数: ${cases.length}`);
            
            if (cases.length > 0) {
                console.log('✅ 案件列表不为空，创建的案件已显示');
                cases.forEach((c, index) => {
                    console.log(`   ${index + 1}. ${c.title} (${c.case_no}) - 状态: ${c.status}`);
                });
            } else {
                console.log('❌ 案件列表为空，创建的案件未显示');
            }
        }

        // 5. 测试案件负责人分配（admin权限）
        if (createResponse.data.success && createResponse.data.data.case) {
            console.log('\n5️⃣ 测试管理员修改案件负责人权限...');
            const caseId = createResponse.data.data.case.id;
            
            const assignResponse = await axios.post(`${baseURL}/api/cases/${caseId}/assign`, {
                owner_id: 2,
                remark: '重新分配负责人'
            }, {
                headers: authHeaders
            });

            if (assignResponse.data.success) {
                console.log('✅ 管理员成功修改案件负责人');
                console.log(`   新负责人ID: ${assignResponse.data.data.case.owner_id}`);
            } else {
                console.log('❌ 管理员修改案件负责人失败');
                console.log('   错误信息:', assignResponse.data.error);
            }
        }

        // 6. 测试普通用户权限
        console.log('\n6️⃣ 测试普通用户权限...');
        const lawyerLoginResponse = await axios.post(`${baseURL}/api/auth/login`, {
            username: 'lawyer1',
            password: 'lawyer123'
        });

        if (lawyerLoginResponse.data.success) {
            const lawyerToken = lawyerLoginResponse.data.token;
            const lawyerHeaders = { 'Authorization': `Bearer ${lawyerToken}` };
            
            // 测试普通用户是否可以访问负责人列表
            const lawyerResponsiblesResponse = await axios.get(`${baseURL}/api/responsibles/active`, {
                headers: lawyerHeaders
            });

            if (lawyerResponsiblesResponse.data.success) {
                console.log('✅ 普通用户可以访问负责人列表');
            } else {
                console.log('❌ 普通用户无法访问负责人列表');
            }
        }

        console.log('\n🎉 案件创建功能修复测试完成！');

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.response) {
            console.error('   响应状态:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
    }
}

// 运行测试
testCaseCreationFix();
