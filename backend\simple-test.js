const http = require('http');

function makeRequest(options, data) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    body: body
                });
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(data);
        }
        req.end();
    });
}

async function testLogin() {
    console.log('🧪 测试登录功能...');
    
    const loginData = JSON.stringify({
        username: 'admin',
        password: 'admin123'
    });

    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/auth/login',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(loginData)
        }
    };

    try {
        const response = await makeRequest(options, loginData);
        console.log('状态码:', response.statusCode);
        console.log('响应:', response.body);
        
        if (response.statusCode === 200) {
            console.log('✅ 登录测试成功！');
            const data = JSON.parse(response.body);
            return data.token;
        } else {
            console.log('❌ 登录测试失败');
            return null;
        }
    } catch (error) {
        console.error('❌ 请求错误:', error.message);
        return null;
    }
}

async function testProfile(token) {
    console.log('\n🧪 测试获取用户信息...');
    
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/auth/profile',
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    };

    try {
        const response = await makeRequest(options);
        console.log('状态码:', response.statusCode);
        console.log('响应:', response.body);
        
        if (response.statusCode === 200) {
            console.log('✅ 获取用户信息成功！');
        } else {
            console.log('❌ 获取用户信息失败');
        }
    } catch (error) {
        console.error('❌ 请求错误:', error.message);
    }
}

async function main() {
    console.log('🚀 开始API测试...\n');
    
    const token = await testLogin();
    if (token) {
        await testProfile(token);
    }
    
    console.log('\n✅ 测试完成');
}

main();
