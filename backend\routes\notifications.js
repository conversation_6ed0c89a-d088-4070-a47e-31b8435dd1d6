const express = require('express');
const router = express.Router();
const { Notify, User, Case, Log } = require('../models');
const { authenticate, requireAdmin } = require('../middleware/auth');
const { Op } = require('sequelize');

/**
 * 获取当前用户的通知列表
 * GET /api/notifications
 */
router.get('/', authenticate, async (req, res) => {
    try {
        const {
            page = 1,
            limit = 20,
            status,
            type,
            unread_only = false
        } = req.query;

        const offset = (page - 1) * limit;
        const where = { user_id: req.user.id };

        // 状态过滤
        if (status !== undefined) {
            where.status = parseInt(status);
        }

        // 类型过滤
        if (type) {
            where.type = type;
        }

        // 只显示未读
        if (unread_only === 'true') {
            where.status = 0;
        }

        const { count, rows } = await Notify.findAndCountAll({
            where,
            include: [
                {
                    model: User,
                    as: 'sender',
                    attributes: ['id', 'username', 'real_name']
                }
            ],
            order: [['created_at', 'DESC']],
            limit: parseInt(limit),
            offset: parseInt(offset)
        });

        res.json({
            notifications: rows,
            pagination: {
                total: count,
                page: parseInt(page),
                limit: parseInt(limit),
                pages: Math.ceil(count / limit)
            },
            unread_count: await Notify.count({
                where: {
                    user_id: req.user.id,
                    status: 0
                }
            })
        });

    } catch (error) {
        console.error('Get notifications error:', error);
        res.status(500).json({
            error: 'Failed to get notifications',
            code: 'GET_NOTIFICATIONS_ERROR'
        });
    }
});

/**
 * 标记通知为已读
 * POST /api/notifications/:id/read
 */
router.post('/:id/read', authenticate, async (req, res) => {
    try {
        const notification = await Notify.findOne({
            where: {
                id: req.params.id,
                user_id: req.user.id
            }
        });

        if (!notification) {
            return res.status(404).json({
                error: 'Notification not found',
                code: 'NOTIFICATION_NOT_FOUND'
            });
        }

        if (notification.status === 0) {
            await notification.update({
                status: 1,
                read_at: new Date()
            });
        }

        res.json({
            message: 'Notification marked as read',
            notification: {
                id: notification.id,
                status: notification.status,
                read_at: notification.read_at
            }
        });

    } catch (error) {
        console.error('Mark notification as read error:', error);
        res.status(500).json({
            error: 'Failed to mark notification as read',
            code: 'MARK_READ_ERROR'
        });
    }
});

/**
 * 批量标记通知为已读
 * POST /api/notifications/read-all
 */
router.post('/read-all', authenticate, async (req, res) => {
    try {
        const { notification_ids } = req.body;

        let where = {
            user_id: req.user.id,
            status: 0
        };

        // 如果指定了通知ID列表，只标记这些通知
        if (notification_ids && Array.isArray(notification_ids)) {
            where.id = { [Op.in]: notification_ids };
        }

        const [updatedCount] = await Notify.update(
            {
                status: 1,
                read_at: new Date()
            },
            { where }
        );

        res.json({
            message: 'Notifications marked as read',
            updated_count: updatedCount
        });

    } catch (error) {
        console.error('Mark all notifications as read error:', error);
        res.status(500).json({
            error: 'Failed to mark notifications as read',
            code: 'MARK_ALL_READ_ERROR'
        });
    }
});

/**
 * 创建通知 (管理员功能)
 * POST /api/notifications
 */
router.post('/', authenticate, requireAdmin, async (req, res) => {
    try {
        const {
            user_id,
            user_ids,
            title,
            content,
            type = '系统通知',
            related_id,
            related_type
        } = req.body;

        if (!title || !content) {
            return res.status(400).json({
                error: 'Title and content are required',
                code: 'MISSING_FIELDS'
            });
        }

        const notifications = [];
        const targetUserIds = [];

        // 确定目标用户
        if (user_id) {
            targetUserIds.push(user_id);
        } else if (user_ids && Array.isArray(user_ids)) {
            targetUserIds.push(...user_ids);
        } else {
            // 如果没有指定用户，发送给所有用户
            const allUsers = await User.findAll({
                where: { status: 1 },
                attributes: ['id']
            });
            targetUserIds.push(...allUsers.map(user => user.id));
        }

        // 创建通知
        for (const userId of targetUserIds) {
            const notification = await Notify.create({
                user_id: userId,
                title,
                content,
                type,
                related_id,
                related_type,
                sender_id: req.user.id
            });
            notifications.push(notification);
        }

        // 记录操作日志
        await Log.create({
            user_id: req.user.id,
            action: '创建通知',
            module: '通知管理',
            detail: JSON.stringify({
                title,
                type,
                target_users_count: targetUserIds.length,
                related_id,
                related_type
            }),
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            status: 'success'
        });

        res.status(201).json({
            message: 'Notifications created successfully',
            notifications_count: notifications.length,
            target_users: targetUserIds.length
        });

    } catch (error) {
        console.error('Create notification error:', error);
        console.error('Error stack:', error.stack);
        res.status(500).json({
            error: 'Failed to create notification',
            code: 'CREATE_NOTIFICATION_ERROR',
            details: error.message
        });
    }
});

/**
 * 删除通知
 * DELETE /api/notifications/:id
 */
router.delete('/:id', authenticate, async (req, res) => {
    try {
        const notification = await Notify.findOne({
            where: {
                id: req.params.id,
                user_id: req.user.id
            }
        });

        if (!notification) {
            return res.status(404).json({
                error: 'Notification not found',
                code: 'NOTIFICATION_NOT_FOUND'
            });
        }

        await notification.destroy();

        res.json({
            message: 'Notification deleted successfully'
        });

    } catch (error) {
        console.error('Delete notification error:', error);
        res.status(500).json({
            error: 'Failed to delete notification',
            code: 'DELETE_NOTIFICATION_ERROR'
        });
    }
});

/**
 * 获取通知统计信息
 * GET /api/notifications/stats
 */
router.get('/stats', authenticate, async (req, res) => {
    try {
        const userId = req.user.id;

        const stats = {
            total: await Notify.count({ where: { user_id: userId } }),
            unread: await Notify.count({ where: { user_id: userId, status: 0 } }),
            read: await Notify.count({ where: { user_id: userId, status: 1 } }),
            by_type: {}
        };

        // 按类型统计
        const typeStats = await Notify.findAll({
            where: { user_id: userId },
            attributes: [
                'type',
                [Notify.sequelize.fn('COUNT', Notify.sequelize.col('id')), 'count']
            ],
            group: ['type'],
            raw: true
        });

        typeStats.forEach(stat => {
            stats.by_type[stat.type] = parseInt(stat.count);
        });

        res.json(stats);

    } catch (error) {
        console.error('Get notification stats error:', error);
        res.status(500).json({
            error: 'Failed to get notification stats',
            code: 'GET_STATS_ERROR'
        });
    }
});

module.exports = router;
