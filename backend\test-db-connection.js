const { Sequelize } = require('sequelize');
const path = require('path');

console.log('🔍 Testing database connection...');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '.env') });

console.log('Environment variables:');
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_PASS:', process.env.DB_PASS ? '***' : 'undefined');

const sequelize = new Sequelize(
    process.env.DB_NAME,
    process.env.DB_USER,
    process.env.DB_PASS,
    {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        dialect: 'mysql',
        logging: console.log, // 启用日志
        define: {
            freezeTableName: true,
        },
    }
);

async function testConnection() {
    try {
        console.log('🔗 Attempting to connect to database...');
        await sequelize.authenticate();
        console.log('✅ Database connection successful!');
        
        // 测试查询
        const [results] = await sequelize.query('SELECT 1 as test');
        console.log('✅ Test query successful:', results);
        
        // 检查数据库是否存在
        const [databases] = await sequelize.query('SHOW DATABASES');
        console.log('📋 Available databases:', databases.map(db => db.Database));
        
        // 检查目标数据库是否存在
        const targetDbExists = databases.some(db => db.Database === process.env.DB_NAME);
        if (targetDbExists) {
            console.log(`✅ Target database '${process.env.DB_NAME}' exists`);
            
            // 检查表
            const [tables] = await sequelize.query(`SHOW TABLES FROM ${process.env.DB_NAME}`);
            console.log('📋 Tables in database:', tables);
        } else {
            console.log(`❌ Target database '${process.env.DB_NAME}' does not exist`);
        }
        
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        console.error('Error details:', error);
    } finally {
        await sequelize.close();
        console.log('🔒 Database connection closed');
        process.exit(0);
    }
}

testConnection();
