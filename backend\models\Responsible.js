const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Responsible = sequelize.define('Responsible', {
    id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
    },
    name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '负责人姓名',
        validate: {
            notEmpty: true,
            len: [1, 100]
        }
    },
    email: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: '负责人邮箱',
        validate: {
            isEmail: true,
        }
    },
    phone: {
        type: DataTypes.STRING(20),
        allowNull: true,
        comment: '负责人电话',
        validate: {
            len: [0, 20]
        }
    },
    department: {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: '所属部门',
        validate: {
            len: [0, 100]
        }
    },

    status: {
        type: DataTypes.TINYINT,
        allowNull: false,
        defaultValue: 1,
        comment: '状态：0-禁用，1-启用',
        validate: {
            isIn: [[0, 1]],
        }
    },
    sort_order: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '排序顺序，数字越小越靠前',
    },
    created_by: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: '创建人ID',
    },
    updated_by: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: '更新人ID',
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
    },
    updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '更新时间',
    },
}, {
    tableName: 'responsibles',
    timestamps: false,
    indexes: [
        {
            fields: ['status']
        },
        {
            fields: ['sort_order']
        },
        {
            fields: ['name']
        },
        {
            unique: true,
            fields: ['email'],
            where: {
                email: {
                    [sequelize.Sequelize.Op.ne]: null
                }
            }
        }
    ],
    hooks: {
        beforeUpdate: (responsible) => {
            responsible.updated_at = new Date();
        }
    }
});

module.exports = Responsible;
