<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案件管理系统修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #1890ff;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .loading {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button:disabled {
            background-color: #d9d9d9;
            cursor: not-allowed;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 案件管理系统修复测试</h1>
    
    <div class="test-section">
        <h2 class="test-title">🔴 高优先级问题测试</h2>
        
        <h3>1. 负责人选择器无限循环测试</h3>
        <button onclick="testResponsiblesAPI()">测试负责人API</button>
        <div id="responsibles-result" class="test-result loading">等待测试...</div>
        
        <h3>2. 案件创建和负责人选择测试</h3>
        <button onclick="testCaseCreation()">测试案件创建</button>
        <div id="case-creation-result" class="test-result loading">等待测试...</div>
        
        <h3>3. 案件列表负责人显示测试</h3>
        <button onclick="testCaseList()">测试案件列表</button>
        <div id="case-list-result" class="test-result loading">等待测试...</div>
    </div>

    <div class="test-section">
        <h2 class="test-title">📊 测试结果汇总</h2>
        <div id="summary" class="test-result loading">请先运行测试...</div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8001/api';
        let testResults = {};

        // 模拟登录获取token
        async function getAuthToken() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                const data = await response.json();
                return data.data?.token;
            } catch (error) {
                console.error('登录失败:', error);
                return null;
            }
        }

        // 测试负责人API
        async function testResponsiblesAPI() {
            const resultDiv = document.getElementById('responsibles-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在测试负责人API...';

            try {
                const token = await getAuthToken();
                if (!token) {
                    throw new Error('无法获取认证token');
                }

                const response = await fetch(`${API_BASE}/responsibles/active`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.responsibles && Array.isArray(data.responsibles)) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 负责人API测试成功<br>
                        获取到 ${data.responsibles.length} 个负责人<br>
                        <pre>${JSON.stringify(data.responsibles, null, 2)}</pre>
                    `;
                    testResults.responsibles = true;
                } else {
                    throw new Error('返回数据格式不正确');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 负责人API测试失败: ${error.message}`;
                testResults.responsibles = false;
            }
            updateSummary();
        }

        // 测试案件创建
        async function testCaseCreation() {
            const resultDiv = document.getElementById('case-creation-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在测试案件创建...';

            try {
                const token = await getAuthToken();
                if (!token) {
                    throw new Error('无法获取认证token');
                }

                const testCase = {
                    title: '测试案件 - 负责人选择修复验证',
                    type: '合同纠纷',
                    description: '这是一个用于测试负责人选择功能的测试案件',
                    priority: '中',
                    owner_id: 1, // 指定负责人ID
                    client_name: '测试客户',
                    client_contact: '13800138000'
                };

                const response = await fetch(`${API_BASE}/cases`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(testCase)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data?.case) {
                    const createdCase = data.data.case;
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 案件创建测试成功<br>
                        案件ID: ${createdCase.id}<br>
                        案件编号: ${createdCase.case_no}<br>
                        负责人ID: ${createdCase.owner_id}<br>
                        负责人信息: ${createdCase.owner ? createdCase.owner.real_name : '未设置'}<br>
                        <pre>${JSON.stringify(createdCase, null, 2)}</pre>
                    `;
                    testResults.caseCreation = true;
                } else {
                    throw new Error('返回数据格式不正确');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 案件创建测试失败: ${error.message}`;
                testResults.caseCreation = false;
            }
            updateSummary();
        }

        // 测试案件列表
        async function testCaseList() {
            const resultDiv = document.getElementById('case-list-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在测试案件列表...';

            try {
                const token = await getAuthToken();
                if (!token) {
                    throw new Error('无法获取认证token');
                }

                const response = await fetch(`${API_BASE}/cases?page=1&limit=5`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && Array.isArray(data.data)) {
                    const cases = data.data;
                    const casesWithOwner = cases.filter(c => c.owner && c.owner.real_name);
                    
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 案件列表测试成功<br>
                        总案件数: ${cases.length}<br>
                        包含负责人信息的案件: ${casesWithOwner.length}<br>
                        <pre>${JSON.stringify(cases.slice(0, 2), null, 2)}</pre>
                    `;
                    testResults.caseList = true;
                } else {
                    throw new Error('返回数据格式不正确');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 案件列表测试失败: ${error.message}`;
                testResults.caseList = false;
            }
            updateSummary();
        }

        // 更新测试汇总
        function updateSummary() {
            const summaryDiv = document.getElementById('summary');
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(result => result === true).length;
            
            if (totalTests === 0) {
                summaryDiv.className = 'test-result loading';
                summaryDiv.innerHTML = '请先运行测试...';
                return;
            }

            if (passedTests === totalTests) {
                summaryDiv.className = 'test-result success';
                summaryDiv.innerHTML = `🎉 所有测试通过! (${passedTests}/${totalTests})`;
            } else {
                summaryDiv.className = 'test-result error';
                summaryDiv.innerHTML = `⚠️ 部分测试失败 (${passedTests}/${totalTests})`;
            }
        }

        // 页面加载完成后自动运行所有测试
        window.onload = function() {
            setTimeout(() => {
                testResponsiblesAPI();
                setTimeout(() => testCaseCreation(), 1000);
                setTimeout(() => testCaseList(), 2000);
            }, 500);
        };
    </script>
</body>
</html>
