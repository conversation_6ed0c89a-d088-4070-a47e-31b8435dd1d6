const http = require('http');

console.log('🔍 Testing frontend and backend connection...');

// 测试后端健康检查
function testBackend() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: '127.0.0.1',
            port: 3001,
            path: '/health',
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                if (res.statusCode === 200) {
                    console.log('✅ Backend health check passed');
                    console.log('📊 Backend response:', JSON.parse(data));
                    resolve(true);
                } else {
                    console.log('❌ Backend health check failed:', res.statusCode);
                    reject(false);
                }
            });
        });

        req.on('error', (e) => {
            console.error('❌ Backend connection error:', e.message);
            reject(false);
        });

        req.end();
    });
}

// 测试后端 API 路由
function testBackendAPI() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: '127.0.0.1',
            port: 3001,
            path: '/api/stats',
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                if (res.statusCode === 200) {
                    console.log('✅ Backend API test passed');
                    console.log('📊 API response:', JSON.parse(data));
                    resolve(true);
                } else {
                    console.log('❌ Backend API test failed:', res.statusCode);
                    reject(false);
                }
            });
        });

        req.on('error', (e) => {
            console.error('❌ Backend API connection error:', e.message);
            reject(false);
        });

        req.end();
    });
}

// 测试前端服务器
function testFrontend() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 5173,
            path: '/',
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            if (res.statusCode === 200) {
                console.log('✅ Frontend server is accessible');
                resolve(true);
            } else {
                console.log('❌ Frontend server test failed:', res.statusCode);
                reject(false);
            }
        });

        req.on('error', (e) => {
            console.error('❌ Frontend connection error:', e.message);
            reject(false);
        });

        req.end();
    });
}

// 运行所有测试
async function runTests() {
    try {
        console.log('🚀 Starting connection tests...\n');
        
        await testBackend();
        await testBackendAPI();
        await testFrontend();
        
        console.log('\n🎉 All tests passed! Your application is ready to use.');
        console.log('📍 Frontend URL: http://localhost:5173');
        console.log('📍 Backend URL: http://127.0.0.1:3001');
        console.log('🏥 Backend Health: http://127.0.0.1:3001/health');
        
    } catch (error) {
        console.log('\n❌ Some tests failed. Please check the server status.');
    }
}

runTests();
