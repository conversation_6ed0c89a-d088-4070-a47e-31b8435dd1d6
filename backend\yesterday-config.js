const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3000;

console.log('🔍 Starting with yesterday config...');

// 最基础的中间件
app.use(cors());
app.use(express.json());

// 基础路由
app.get('/', (req, res) => {
    res.json({ message: 'Server running', port: PORT });
});

app.get('/health', (req, res) => {
    res.json({ status: 'healthy' });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`✅ Server running on port ${PORT}`);
});

console.log('✅ Setup complete');
