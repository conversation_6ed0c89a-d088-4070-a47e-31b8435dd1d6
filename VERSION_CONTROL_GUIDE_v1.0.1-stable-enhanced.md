# 🔄 版本控制指南 - v1.0.1-stable-enhanced

**创建时间**: 2025年7月10日  
**适用版本**: v1.0.1-stable-enhanced  
**Git提交**: 0300128  
**目标**: 确保项目版本的完整备份和可靠回溯

---

## 📋 版本信息总览

### 当前稳定版本
- **版本号**: v1.0.1-stable-enhanced
- **Git标签**: v1.0.1-stable-enhanced
- **提交哈希**: 0300128
- **分支**: master
- **创建时间**: 2025-07-10 10:00

### 版本特征
- **前端端口**: 3001
- **后端端口**: 8001
- **数据库端口**: 3306
- **主要功能**: 文件管理优化、批量操作、文件预览
- **测试状态**: ✅ 全部通过
- **部署状态**: ✅ 生产就绪

---

## 🎯 快速回溯命令

### 立即回溯到此版本
```bash
# 方法1: 使用标签回溯
git checkout v1.0.1-stable-enhanced

# 方法2: 使用提交哈希回溯
git checkout 0300128

# 方法3: 重置到此版本(谨慎使用)
git reset --hard v1.0.1-stable-enhanced
```

### 创建基于此版本的新分支
```bash
# 创建新分支
git checkout -b feature-branch v1.0.1-stable-enhanced

# 或者从当前位置创建
git checkout v1.0.1-stable-enhanced
git checkout -b new-feature
```

---

## 📁 关键文件备份清单

### 前端关键文件
```
frontend/
├── package.json                           # 依赖配置
├── vite.config.js                        # 构建配置
├── src/
│   ├── App.jsx                           # 主应用组件
│   ├── pages/Files/FileManagement.jsx    # 文件管理页面(已优化)
│   ├── components/Layout/Layout.jsx      # 布局组件
│   ├── services/api.js                   # API服务
│   └── hooks/                            # 自定义Hooks
│       ├── useApi.js
│       ├── useAuth.js
│       └── useLoading.js
```

### 后端关键文件
```
backend/
├── package.json                          # 依赖配置
├── .env                                  # 环境配置(端口8001)
├── app.js                                # 主应用入口
├── config/database.js                   # 数据库配置
├── models/                               # 数据模型
├── routes/                               # API路由
└── middleware/                           # 中间件
```

### 配置文件快照
```bash
# 后端环境配置(.env)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=case_manager
DB_USER=root
DB_PASS=sie_huangshutian2025
JWT_SECRET=sie_SuperKey2025
PORT=8001

# 前端代理配置(vite.config.js)
server: {
  port: 3001,
  proxy: {
    '/api': {
      target: 'http://localhost:8001',
      changeOrigin: true
    }
  }
}
```

---

## 🔧 完整恢复步骤

### 步骤1: 环境准备
```bash
# 确保Git仓库干净
git status
git stash  # 如有未提交更改

# 检查可用标签
git tag -l
```

### 步骤2: 版本回溯
```bash
# 回溯到稳定版本
git checkout v1.0.1-stable-enhanced

# 验证版本
git describe --tags
git log --oneline -5
```

### 步骤3: 依赖安装
```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 步骤4: 配置验证
```bash
# 检查后端配置
cat backend/.env

# 检查前端配置
cat frontend/vite.config.js

# 检查端口占用
netstat -ano | findstr :8001
netstat -ano | findstr :3001
```

### 步骤5: 服务启动
```bash
# 启动后端服务
cd backend
npm start

# 新终端启动前端服务
cd frontend
npm run dev
```

### 步骤6: 功能验证
```bash
# 测试后端健康检查
curl http://localhost:8001/health

# 测试登录API
curl -X POST http://localhost:8001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'

# 访问前端应用
# 浏览器打开: http://localhost:3001
```

---

## 🛡️ 安全备份策略

### 创建备份标签
```bash
# 在进行重大更改前创建备份
git tag backup-$(date +%Y%m%d-%H%M%S)

# 示例
git tag backup-20250710-1000
```

### 导出版本快照
```bash
# 导出完整项目快照
git archive --format=zip --output=sie-dispute-manager-v1.0.1-stable-enhanced.zip v1.0.1-stable-enhanced

# 导出特定目录
git archive --format=zip --output=frontend-v1.0.1.zip v1.0.1-stable-enhanced:frontend/
git archive --format=zip --output=backend-v1.0.1.zip v1.0.1-stable-enhanced:backend/
```

### 数据库备份
```bash
# 备份数据库结构和数据
mysqldump -u root -p case_manager > case_manager_backup_v1.0.1.sql

# 仅备份结构
mysqldump -u root -p --no-data case_manager > case_manager_structure_v1.0.1.sql
```

---

## 🔍 版本对比和差异

### 与上一版本对比
```bash
# 查看与v1.0.0的差异
git diff v1.0.0..v1.0.1-stable-enhanced

# 查看文件变更统计
git diff --stat v1.0.0..v1.0.1-stable-enhanced

# 查看特定文件的变更
git diff v1.0.0..v1.0.1-stable-enhanced -- frontend/src/pages/Files/FileManagement.jsx
```

### 提交历史查看
```bash
# 查看版本间的提交历史
git log v1.0.0..v1.0.1-stable-enhanced --oneline

# 查看详细提交信息
git log v1.0.0..v1.0.1-stable-enhanced --stat
```

---

## 🚨 紧急回滚程序

### 场景1: 发现严重Bug需要立即回滚
```bash
# 立即回滚到稳定版本
git checkout v1.0.1-stable-enhanced

# 强制重置(谨慎使用)
git reset --hard v1.0.1-stable-enhanced

# 重新启动服务
cd backend && npm start &
cd frontend && npm run dev
```

### 场景2: 配置文件损坏
```bash
# 恢复后端配置
git checkout v1.0.1-stable-enhanced -- backend/.env

# 恢复前端配置
git checkout v1.0.1-stable-enhanced -- frontend/vite.config.js

# 恢复依赖配置
git checkout v1.0.1-stable-enhanced -- frontend/package.json
git checkout v1.0.1-stable-enhanced -- backend/package.json
```

### 场景3: 代码文件损坏
```bash
# 恢复特定文件
git checkout v1.0.1-stable-enhanced -- frontend/src/pages/Files/FileManagement.jsx

# 恢复整个目录
git checkout v1.0.1-stable-enhanced -- frontend/src/

# 恢复所有文件
git checkout v1.0.1-stable-enhanced -- .
```

---

## 📊 版本验证清单

### 环境验证
- [ ] Node.js版本 >= 14.0.0
- [ ] MySQL服务正常运行
- [ ] 端口8001和3001可用
- [ ] Git版本控制正常

### 代码验证
- [ ] 前端代码编译无错误
- [ ] 后端服务启动成功
- [ ] 数据库连接正常
- [ ] API接口响应正常

### 功能验证
- [ ] 用户登录功能正常
- [ ] 文件管理页面加载正常
- [ ] 文件预览功能工作
- [ ] 批量操作功能正常
- [ ] 响应式设计正常

### 性能验证
- [ ] 页面加载时间 < 3秒
- [ ] API响应时间 < 200ms
- [ ] 内存使用正常
- [ ] 无明显性能问题

---

## 📞 故障排除

### 常见问题及解决方案

#### 问题1: 端口冲突
```bash
# 检查端口占用
netstat -ano | findstr :8001
netstat -ano | findstr :3001

# 杀死占用进程
taskkill /PID <PID> /F
```

#### 问题2: 依赖安装失败
```bash
# 清理缓存重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

#### 问题3: 数据库连接失败
```bash
# 检查MySQL服务
net start mysql

# 测试连接
mysql -u root -p -e "SELECT 1"
```

#### 问题4: Git操作失败
```bash
# 检查Git状态
git status

# 强制清理
git clean -fd
git reset --hard HEAD
```

---

## 📝 维护记录

### 版本创建记录
- **创建人**: Augment Agent
- **创建时间**: 2025-07-10 10:00
- **创建原因**: 文件管理功能优化完成，系统稳定性提升
- **测试状态**: 全面测试通过
- **部署状态**: 生产环境就绪

### 后续维护计划
- **下次检查**: 2025-07-17
- **版本更新**: 根据用户反馈和功能需求
- **安全更新**: 定期检查依赖包安全性
- **性能优化**: 持续监控和优化

---

**文档维护**: Augment Agent  
**最后更新**: 2025年7月10日  
**版本**: v1.0.1-stable-enhanced  
**状态**: ✅ 已验证
