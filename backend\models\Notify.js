const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Notify = sequelize.define('Notify', {
    id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
    },
    user_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '接收用户ID',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    title: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: '通知标题',
        validate: {
            notEmpty: true,
        }
    },
    content: {
        type: DataTypes.TEXT,
        allowNull: false,
        comment: '通知内容',
        validate: {
            notEmpty: true,
        }
    },
    type: {
        type: DataTypes.STRING(50),
        allowNull: false,
        defaultValue: '系统通知',
        comment: '通知类型',
        validate: {
            isIn: [['系统通知', '案件提醒', '任务分配', '状态变更', '截止提醒']]
        }
    },
    status: {
        type: DataTypes.TINYINT,
        allowNull: false,
        defaultValue: 0,
        comment: '状态：0-未读，1-已读',
        validate: {
            isIn: [[0, 1]]
        }
    },
    related_id: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: '关联ID（如案件ID）',
    },
    related_type: {
        type: DataTypes.STRING(50),
        allowNull: true,
        comment: '关联类型（如case）',
    },
    sender_id: {
        type: DataTypes.BIGINT,
        allowNull: true,
        comment: '发送人ID',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
    },
    read_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: '阅读时间',
    },
}, {
    tableName: 'notifications',
    timestamps: false,
    indexes: [
        {
            fields: ['user_id']
        },
        {
            fields: ['status']
        },
        {
            fields: ['type']
        },
        {
            fields: ['created_at']
        },
        {
            fields: ['related_id', 'related_type']
        }
    ]
});

module.exports = Notify;
