// 简单的前后端连接测试脚本
import axios from 'axios';

async function testFrontendBackendConnection() {
    console.log('🧪 测试前后端连接...\n');

    try {
        // 1. 测试后端健康检查
        console.log('1️⃣ 测试后端健康检查...');
        const healthResponse = await axios.get('http://localhost:3000/health');
        console.log('✅ 后端健康检查成功:', healthResponse.status);
        console.log('响应:', healthResponse.data);

        // 2. 测试登录API
        console.log('\n2️⃣ 测试登录API...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin123'
        });
        console.log('✅ 登录API成功:', loginResponse.status);
        const token = loginResponse.data.token;
        console.log('Token获取成功:', token.substring(0, 50) + '...');

        // 3. 测试统计API（需要认证）
        console.log('\n3️⃣ 测试统计API...');
        const statsResponse = await axios.get('http://localhost:3000/api/stats/overview', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        console.log('✅ 统计API成功:', statsResponse.status);
        console.log('统计数据:', {
            totalCases: statsResponse.data.overview.totalCases,
            newCasesThisMonth: statsResponse.data.overview.newCasesThisMonth
        });

        console.log('\n🎉 前后端连接测试全部通过！');
        return true;

    } catch (error) {
        console.error('❌ 前后端连接测试失败:', error.message);
        if (error.response) {
            console.error('错误状态:', error.response.status);
            console.error('错误数据:', error.response.data);
        }
        return false;
    }
}

// 运行测试
testFrontendBackendConnection();
