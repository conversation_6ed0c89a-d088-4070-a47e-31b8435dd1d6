// 前端页面功能测试脚本
import axios from 'axios';

const API_BASE = 'http://127.0.0.1:3001/api';

class FrontendPageTest {
    constructor() {
        this.token = null;
        this.testResults = {
            auth: { passed: 0, failed: 0, tests: [] },
            cases: { passed: 0, failed: 0, tests: [] },
            users: { passed: 0, failed: 0, tests: [] }
        };
    }

    async runTest(category, testName, testFn) {
        try {
            console.log(`🧪 ${testName}...`);
            await testFn();
            this.testResults[category].passed++;
            this.testResults[category].tests.push({ name: testName, status: 'PASSED' });
            console.log(`✅ ${testName} - 通过`);
        } catch (error) {
            this.testResults[category].failed++;
            this.testResults[category].tests.push({ name: testName, status: 'FAILED', error: error.message });
            console.log(`❌ ${testName} - 失败: ${error.message}`);
        }
    }

    async testAuthentication() {
        console.log('\n🔐 === 认证系统测试 ===');

        // 1. 登录测试
        await this.runTest('auth', '用户登录', async () => {
            const response = await axios.post(`${API_BASE}/auth/login`, {
                username: 'admin',
                password: 'admin123'
            });
            
            if (response.status !== 200) throw new Error('登录状态码错误');
            if (!response.data.token) throw new Error('未返回token');
            if (!response.data.user) throw new Error('未返回用户信息');
            
            this.token = response.data.token;
        });

        // 2. 获取用户信息测试
        await this.runTest('auth', '获取用户信息', async () => {
            const response = await axios.get(`${API_BASE}/auth/profile`, {
                headers: { Authorization: `Bearer ${this.token}` }
            });
            
            if (response.status !== 200) throw new Error('获取用户信息状态码错误');
            if (!response.data.user) throw new Error('未返回用户信息');
        });
    }

    async testUsersAPI() {
        console.log('\n👥 === 用户管理API测试 ===');

        // 1. 获取用户列表
        await this.runTest('users', '获取用户列表', async () => {
            const response = await axios.get(`${API_BASE}/users`, {
                headers: { Authorization: `Bearer ${this.token}` }
            });
            
            if (response.status !== 200) throw new Error('获取用户列表状态码错误');
            if (!response.data.users) throw new Error('未返回用户列表');
            if (!Array.isArray(response.data.users)) throw new Error('用户列表格式错误');
        });
    }

    async testCasesAPI() {
        console.log('\n📋 === 案件管理API测试 ===');
        let caseId = null;

        // 1. 获取案件列表
        await this.runTest('cases', '获取案件列表', async () => {
            const response = await axios.get(`${API_BASE}/cases?page=1&limit=10`, {
                headers: { Authorization: `Bearer ${this.token}` }
            });
            
            if (response.status !== 200) throw new Error('获取案件列表状态码错误');
            if (!response.data.cases) throw new Error('未返回案件列表');
            if (!Array.isArray(response.data.cases)) throw new Error('案件列表格式错误');
        });

        // 2. 创建案件
        await this.runTest('cases', '创建案件', async () => {
            const response = await axios.post(`${API_BASE}/cases`, {
                title: '前端页面测试案件',
                type: '合同纠纷',
                description: '这是一个前端页面功能测试案件',
                priority: '中',
                client_name: '测试客户',
                client_contact: '13800138000'
            }, {
                headers: { Authorization: `Bearer ${this.token}` }
            });
            
            if (response.status !== 201) throw new Error('创建案件状态码错误');
            if (!response.data.case) throw new Error('未返回案件信息');
            
            caseId = response.data.case.id;
        });

        // 3. 获取案件详情
        if (caseId) {
            await this.runTest('cases', '获取案件详情', async () => {
                const response = await axios.get(`${API_BASE}/cases/${caseId}`, {
                    headers: { Authorization: `Bearer ${this.token}` }
                });
                
                if (response.status !== 200) throw new Error('获取案件详情状态码错误');
                if (!response.data.case) throw new Error('未返回案件详情');
            });

            // 4. 更新案件状态
            await this.runTest('cases', '更新案件状态', async () => {
                const response = await axios.post(`${API_BASE}/cases/${caseId}/status`, {
                    status: '处理中',
                    remark: '开始处理前端测试案件'
                }, {
                    headers: { Authorization: `Bearer ${this.token}` }
                });
                
                if (response.status !== 200) throw new Error('更新案件状态码错误');
            });
        }
    }

    printResults() {
        console.log('\n📊 === 测试结果汇总 ===');
        
        let totalPassed = 0;
        let totalFailed = 0;
        
        Object.entries(this.testResults).forEach(([category, results]) => {
            console.log(`\n${category.toUpperCase()}:`);
            console.log(`  通过: ${results.passed}`);
            console.log(`  失败: ${results.failed}`);
            
            totalPassed += results.passed;
            totalFailed += results.failed;
            
            if (results.failed > 0) {
                console.log('  失败的测试:');
                results.tests.filter(t => t.status === 'FAILED').forEach(test => {
                    console.log(`    - ${test.name}: ${test.error}`);
                });
            }
        });
        
        console.log(`\n总计:`);
        console.log(`  通过: ${totalPassed}`);
        console.log(`  失败: ${totalFailed}`);
        console.log(`  成功率: ${totalPassed + totalFailed > 0 ? ((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1) : 0}%`);
        
        if (totalFailed === 0) {
            console.log('\n🎉 所有测试通过！前端页面API集成正常！');
        } else {
            console.log('\n⚠️  部分测试失败，请检查相关功能。');
        }
    }

    async runAllTests() {
        console.log('🚀 开始前端页面功能测试...\n');
        
        try {
            await this.testAuthentication();
            await this.testUsersAPI();
            await this.testCasesAPI();
        } catch (error) {
            console.error('测试过程中发生错误:', error);
        }
        
        this.printResults();
    }
}

// 运行测试
const tester = new FrontendPageTest();
tester.runAllTests().catch(console.error);
