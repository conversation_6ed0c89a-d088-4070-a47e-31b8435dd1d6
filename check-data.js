const sequelize = require('./backend/config/database');
const Case = require('./backend/models/Case');

async function checkData() {
  try {
    await sequelize.authenticate();
    console.log('数据库连接成功');

    const totalCases = await Case.count();
    console.log('总案件数:', totalCases);

    if (totalCases > 0) {
      const cases = await Case.findAll({
        attributes: ['id', 'title', 'status', 'type', 'priority', 'created_at'],
        limit: 5
      });
      console.log('案件样本:');
      cases.forEach(c => {
        console.log(`- ID: ${c.id}, 标题: ${c.title}, 状态: ${c.status}, 类型: ${c.type}`);
      });

      // 按状态统计
      const statusStats = await Case.findAll({
        attributes: [
          'status',
          [Case.sequelize.fn('COUNT', Case.sequelize.col('id')), 'count']
        ],
        group: ['status'],
        raw: true
      });
      console.log('状态统计:', statusStats);
    }

    process.exit(0);
  } catch (error) {
    console.error('检查数据失败:', error);
    process.exit(1);
  }
}

checkData();
