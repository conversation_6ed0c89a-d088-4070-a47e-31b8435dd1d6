<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案件详情调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .test-result.success {
            background-color: #f6ffed;
            border-color: #52c41a;
            color: #389e0d;
        }
        .test-result.error {
            background-color: #fff2f0;
            border-color: #ff4d4f;
            color: #cf1322;
        }
        .test-result.info {
            background-color: #e6f7ff;
            border-color: #1890ff;
            color: #0050b3;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        input {
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            margin: 5px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
        .summary {
            background-color: #fafafa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 案件详情调试测试</h1>
        <p>专门用于调试案件详情页面显示问题</p>
        
        <div>
            <h3>测试配置</h3>
            <label>API基础地址: </label>
            <input type="text" id="apiBase" value="http://localhost:8001/api" style="width: 300px;">
            <br>
            <label>认证Token: </label>
            <input type="text" id="authToken" placeholder="Bearer token" style="width: 400px;">
            <br>
            <label>案件ID: </label>
            <input type="number" id="caseId" value="1" min="1">
            <button onclick="testCaseDetail()">测试案件详情</button>
            <button onclick="testCaseList()">测试案件列表</button>
            <button onclick="testAuth()">测试认证状态</button>
        </div>
    </div>

    <div class="container">
        <h3>🔐 认证状态测试</h3>
        <div id="authResult" class="test-result info">点击"测试认证状态"按钮开始测试</div>
    </div>

    <div class="container">
        <h3>📋 案件列表测试</h3>
        <div id="caseListResult" class="test-result info">点击"测试案件列表"按钮开始测试</div>
    </div>

    <div class="container">
        <h3>📄 案件详情测试</h3>
        <div id="caseDetailResult" class="test-result info">点击"测试案件详情"按钮开始测试</div>
    </div>

    <div class="container">
        <h3>📊 测试总结</h3>
        <div id="summary" class="summary">
            <p>等待测试结果...</p>
        </div>
    </div>

    <script>
        const API_BASE = document.getElementById('apiBase').value;
        let authToken = '';

        // 测试认证状态
        async function testAuth() {
            const resultDiv = document.getElementById('authResult');
            authToken = document.getElementById('authToken').value;

            if (!authToken) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 请先输入认证Token';
                return;
            }

            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = '正在测试认证状态...';

            try {
                const response = await fetch(`${API_BASE}/auth/profile`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 认证成功<br>
                        用户ID: ${data.data.user.id}<br>
                        用户名: ${data.data.user.username}<br>
                        真实姓名: ${data.data.user.real_name || '未设置'}<br>
                        角色: ${data.data.user.roles?.map(r => r.name).join(', ') || '无角色'}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    throw new Error(`认证失败: ${data.error || response.statusText}`);
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 认证测试失败: ${error.message}`;
            }
        }

        // 测试案件列表
        async function testCaseList() {
            const resultDiv = document.getElementById('caseListResult');
            authToken = document.getElementById('authToken').value;

            if (!authToken) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 请先输入认证Token';
                return;
            }

            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = '正在获取案件列表...';

            try {
                const response = await fetch(`${API_BASE}/cases?page=1&limit=5`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 案件列表获取成功<br>
                        总数: ${data.data.total}<br>
                        当前页案件数: ${data.data.cases.length}<br>
                        案件ID列表: ${data.data.cases.map(c => c.id).join(', ')}<br>
                        <pre>${JSON.stringify(data.data.cases.slice(0, 2), null, 2)}</pre>
                    `;
                } else {
                    throw new Error(`获取失败: ${data.error || response.statusText}`);
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 案件列表测试失败: ${error.message}`;
            }
        }

        // 测试案件详情
        async function testCaseDetail() {
            const resultDiv = document.getElementById('caseDetailResult');
            const caseId = document.getElementById('caseId').value;
            authToken = document.getElementById('authToken').value;

            if (!authToken) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 请先输入认证Token';
                return;
            }

            if (!caseId) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = '❌ 请输入案件ID';
                return;
            }

            resultDiv.className = 'test-result info';
            resultDiv.innerHTML = '正在获取案件详情...';

            try {
                console.log(`发送请求到: ${API_BASE}/cases/${caseId}`);
                console.log(`使用Token: ${authToken}`);

                const response = await fetch(`${API_BASE}/cases/${caseId}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                console.log(`响应状态: ${response.status}`);
                console.log(`响应头:`, response.headers);

                const data = await response.json();
                console.log('响应数据:', data);
                
                if (response.ok && data.success) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 案件详情获取成功<br>
                        案件ID: ${data.data.case.id}<br>
                        案件标题: ${data.data.case.title}<br>
                        案件状态: ${data.data.case.status}<br>
                        负责人: ${data.data.case.owner?.real_name || data.data.case.owner_name || '未设置'}<br>
                        流转记录数: ${data.data.case.flows?.length || 0}<br>
                        <pre>${JSON.stringify(data.data.case, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `
                        ❌ 案件详情获取失败<br>
                        HTTP状态: ${response.status}<br>
                        错误代码: ${data.code || '无'}<br>
                        错误信息: ${data.error || data.message || '未知错误'}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 请求失败: ${error.message}`;
                console.error('请求错误:', error);
            }
        }

        // 页面加载时设置API基础地址
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('apiBase').value = 'http://localhost:8001/api';
        });
    </script>
</body>
</html>
