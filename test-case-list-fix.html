<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案件列表修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .case-item {
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #eee;
            border-radius: 3px;
            background-color: #f9f9f9;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #results {
            margin-top: 20px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 案件列表显示问题修复测试</h1>
        
        <div class="test-section">
            <h3>📋 问题描述</h3>
            <p>案件列表页面无法显示任何案件数据，但数据库中确实存在案件记录。</p>
            <p><strong>根本原因</strong>：前后端数据格式不匹配</p>
            <ul>
                <li>后端返回：<code>{ success: true, data: [...], pagination: {...} }</code></li>
                <li>前端期望：<code>{ data: { cases: [...], total: ..., page: ... } }</code></li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 API测试</h3>
            <button onclick="testLogin()">1. 测试登录</button>
            <button onclick="testCasesList()">2. 测试案件列表API</button>
            <button onclick="testFrontendAPI()">3. 测试前端API调用</button>
            <button onclick="clearResults()">清空结果</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        let authToken = null;

        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testLogin() {
            log('🔐 开始测试登录...', 'info');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.token) {
                    authToken = data.token;
                    log('✅ 登录成功！', 'success');
                    log(`用户：${data.user.real_name} (${data.user.username})`, 'info');
                } else {
                    log('❌ 登录失败：' + (data.error || '未知错误'), 'error');
                }
            } catch (error) {
                log('❌ 登录请求失败：' + error.message, 'error');
            }
        }

        async function testCasesList() {
            if (!authToken) {
                log('⚠️ 请先登录获取认证token', 'error');
                return;
            }

            log('📋 开始测试案件列表API...', 'info');
            
            try {
                const response = await fetch('/api/cases?page=1&limit=5', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    log('✅ 案件列表API调用成功！', 'success');
                    log(`数据格式：success=${data.success}, data数组长度=${data.data?.length || 0}`, 'info');
                    log(`分页信息：total=${data.pagination?.total}, page=${data.pagination?.page}`, 'info');
                    
                    // 显示前几个案件
                    if (data.data && data.data.length > 0) {
                        log('📄 案件示例：', 'info');
                        data.data.slice(0, 3).forEach((caseItem, index) => {
                            log(`${index + 1}. ${caseItem.title} (${caseItem.case_no}) - ${caseItem.status}`, 'info');
                        });
                    } else {
                        log('⚠️ 返回的案件数据为空', 'error');
                    }
                    
                    // 显示完整响应结构
                    const pre = document.createElement('pre');
                    pre.textContent = JSON.stringify(data, null, 2);
                    document.getElementById('results').appendChild(pre);
                } else {
                    log('❌ 案件列表API调用失败：' + (data.error || '未知错误'), 'error');
                }
            } catch (error) {
                log('❌ 案件列表API请求失败：' + error.message, 'error');
            }
        }

        async function testFrontendAPI() {
            log('🔧 测试前端修复后的数据处理...', 'info');
            
            if (!authToken) {
                log('⚠️ 请先登录获取认证token', 'error');
                return;
            }

            try {
                // 模拟前端的API调用
                const response = await fetch('/api/cases?page=1&limit=10', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const apiResponse = await response.json();
                
                if (response.ok) {
                    // 模拟修复后的前端数据处理逻辑
                    let cases = [];
                    let pagination = { current: 1, pageSize: 10, total: 0 };
                    
                    if (apiResponse.data) {
                        // 修复后的逻辑：直接使用 response.data 作为案件数组
                        cases = apiResponse.data || [];
                        if (apiResponse.pagination) {
                            pagination = {
                                ...pagination,
                                total: apiResponse.pagination.total || 0,
                                current: apiResponse.pagination.page || 1,
                            };
                        }
                    }
                    
                    log('✅ 前端数据处理成功！', 'success');
                    log(`处理结果：cases数组长度=${cases.length}, 总数=${pagination.total}`, 'success');
                    
                    if (cases.length > 0) {
                        log('📋 处理后的案件列表：', 'info');
                        cases.slice(0, 5).forEach((caseItem, index) => {
                            log(`${index + 1}. ${caseItem.title} - ${caseItem.status} (负责人: ${caseItem.owner?.real_name})`, 'info');
                        });
                    }
                } else {
                    log('❌ API调用失败：' + (apiResponse.error || '未知错误'), 'error');
                }
            } catch (error) {
                log('❌ 前端测试失败：' + error.message, 'error');
            }
        }

        // 页面加载时的提示
        window.onload = function() {
            log('🚀 案件列表修复测试页面已加载', 'info');
            log('请按顺序点击测试按钮：1. 登录 → 2. 测试API → 3. 测试前端处理', 'info');
        };
    </script>
</body>
</html>
