const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

// 详细日志功能
const createLogger = (prefix) => {
    return {
        info: (message, data = null) => {
            console.log(`🔵 [${prefix}] ${new Date().toISOString()} - ${message}`);
            if (data) console.log('   📊 数据:', JSON.stringify(data, null, 2));
        },
        error: (message, error = null) => {
            console.error(`🔴 [${prefix}] ${new Date().toISOString()} - ${message}`);
            if (error) {
                console.error('   ❌ 错误详情:', error.message);
                console.error('   📍 错误堆栈:', error.stack);
            }
        },
        warn: (message, data = null) => {
            console.warn(`🟡 [${prefix}] ${new Date().toISOString()} - ${message}`);
            if (data) console.warn('   ⚠️  数据:', JSON.stringify(data, null, 2));
        },
        debug: (message, data = null) => {
            console.log(`🟢 [${prefix}] ${new Date().toISOString()} - ${message}`);
            if (data) console.log('   🔍 调试:', JSON.stringify(data, null, 2));
        }
    };
};

const logger = createLogger('APP');
const dbLogger = createLogger('DATABASE');
const apiLogger = createLogger('API');

logger.info('🚀 启动法务案件管理平台后端服务器...');

// 加载依赖模块
let sequelize, models;
try {
    logger.info('📦 加载数据库配置...');
    sequelize = require('./config/database');
    logger.info('✅ 数据库配置加载成功');

    logger.info('📦 加载数据模型...');
    models = require('./models');
    logger.info('✅ 数据模型加载成功');

    logger.info('📦 加载工具模块...');
    const { startCleanupSchedule } = require('./utils/cleanup');
    const { runMigration, checkMigrationNeeded } = require('./utils/migrate-database');
    logger.info('✅ 工具模块加载成功');
} catch (error) {
    logger.error('❌ 模块加载失败', error);
    process.exit(1);
}

const app = express();
const PORT = process.env.PORT || 8001;

logger.info(`🔧 配置服务器端口: ${PORT}`);

// 中间件配置
logger.info('🔧 配置中间件...');
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 请求追踪中间件
app.use((req, res, next) => {
    const requestId = Date.now().toString(36) + Math.random().toString(36).substr(2);
    req.requestId = requestId;

    apiLogger.info(`📥 请求开始 [${requestId}]`, {
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        headers: req.headers,
        body: req.body,
        params: req.params,
        query: req.query
    });

    // 记录响应
    const originalSend = res.send;
    res.send = function (data) {
        apiLogger.info(`📤 请求完成 [${requestId}]`, {
            statusCode: res.statusCode,
            responseData: typeof data === 'string' ? data.substring(0, 500) : data
        });
        originalSend.call(this, data);
    };

    next();
});

// 静态文件服务（用于文件上传）
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
logger.info('✅ 中间件配置完成');

// 基础路由
app.get('/', (req, res) => {
    apiLogger.info(`📍 根路由访问 [${req.requestId}]`);
    res.json({
        message: '法务案件管理平台 API',
        version: '1.0.0',
        status: 'running',
        port: PORT,
        timestamp: new Date().toISOString()
    });
});

// 健康检查
app.get('/health', async (req, res) => {
    apiLogger.info(`📍 健康检查请求 [${req.requestId}]`);
    try {
        dbLogger.info('🔍 测试数据库连接...');
        await sequelize.authenticate();
        dbLogger.info('✅ 数据库连接正常');

        res.json({
            status: 'healthy',
            database: 'connected',
            timestamp: new Date().toISOString(),
            port: PORT
        });
    } catch (error) {
        dbLogger.error('❌ 数据库连接失败', error);
        res.status(500).json({
            status: 'unhealthy',
            database: 'disconnected',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// 导入认证中间件和模型
logger.info('📦 加载认证中间件和数据模型...');
let authenticate, Case, User, CaseFlow, CaseFile, CaseArchive;

try {
    const authModule = require('./middleware/auth');
    authenticate = authModule.authenticate;
    logger.info('✅ 认证中间件加载成功');

    const modelsModule = require('./models');
    Case = modelsModule.Case;
    User = modelsModule.User;
    CaseFlow = modelsModule.CaseFlow;
    CaseFile = modelsModule.CaseFile;
    CaseArchive = modelsModule.CaseArchive;
    logger.info('✅ 数据模型加载成功');
} catch (error) {
    logger.error('❌ 认证中间件或数据模型加载失败', error);
    // 创建模拟认证中间件以便测试
    authenticate = (req, res, next) => {
        logger.warn('⚠️  使用模拟认证中间件');
        req.user = {
            id: 1,
            username: 'testuser',
            roles: [{ name: 'admin' }]
        };
        next();
    };
}



// 测试路由 - 用于验证不存在的案件
app.get('/api/test/case-not-found', authenticate, async (req, res) => {
    const requestId = req.requestId;
    apiLogger.info(`📍 测试案件不存在路由 [${requestId}]`);

    res.status(404).json({
        success: false,
        error: '案件不存在或已被删除',
        code: 'CASE_NOT_FOUND',
        testMessage: '这是测试路由，用于验证错误消息格式'
    });
});



// API 路由
app.use('/api/auth', require('./routes/auth'));
app.use('/api/users', require('./routes/users'));
app.use('/api/roles', require('./routes/roles'));
app.use('/api/cases', require('./routes/cases')); // 重新启用案件路由
app.use('/api/files', require('./routes/files'));
app.use('/api/notifications', require('./routes/notifications'));
app.use('/api/stats', require('./routes/stats'));
app.use('/api/responsibles', require('./routes/responsibles'));

// 404 处理
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'API endpoint not found',
        path: req.originalUrl,
        method: req.method
    });
});

// 全局错误处理
app.use((error, req, res, next) => {
    console.error('Global error handler:', error);

    res.status(error.status || 500).json({
        error: error.message || 'Internal server error',
        ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
    });
});

// 数据库同步和服务器启动
async function startServer() {
    try {
        logger.info('🔄 开始启动服务器...');

        // 测试数据库连接
        if (sequelize) {
            dbLogger.info('🔍 测试数据库连接...');
            await sequelize.authenticate();
            dbLogger.info('✅ 数据库连接建立成功');

            // 检查并执行数据库迁移
            try {
                const { runMigration, checkMigrationNeeded } = require('./utils/migrate-database');
                const needsMigration = await checkMigrationNeeded();
                if (needsMigration) {
                    dbLogger.info('📋 检测到需要数据库迁移，开始执行...');
                    await runMigration();
                    dbLogger.info('✅ 数据库迁移完成');
                }
            } catch (migrationError) {
                dbLogger.warn('⚠️  数据库迁移检查失败，跳过', migrationError);
            }

            // 同步数据库模型（开发环境）
            if (process.env.NODE_ENV === 'development') {
                dbLogger.info('🔄 同步数据库模型（开发环境）...');
                await sequelize.sync({ alter: true });
                dbLogger.info('✅ 数据库模型同步完成');
            }
        } else {
            dbLogger.warn('⚠️  数据库连接未初始化，使用模拟模式');
        }

        // 启动服务器
        app.listen(PORT, '127.0.0.1', () => {
            logger.info(`🚀 服务器启动成功！`);
            logger.info(`📍 API URL: http://127.0.0.1:${PORT}`);
            logger.info(`🏥 健康检查: http://127.0.0.1:${PORT}/health`);
            logger.info(`📋 测试案例:`);
            logger.info(`   - 正常案件: http://127.0.0.1:${PORT}/api/cases/1`);
            logger.info(`   - 不存在案件: http://127.0.0.1:${PORT}/api/cases/999`);
            logger.info(`   - 测试路由: http://127.0.0.1:${PORT}/api/test/case-not-found`);
            logger.info(`   - 案件列表: http://127.0.0.1:${PORT}/api/cases`);

            // 启动清理任务（每24小时执行一次）
            try {
                const { startCleanupSchedule } = require('./utils/cleanup');
                startCleanupSchedule(24);
                logger.info('✅ 清理任务调度启动成功');
            } catch (cleanupError) {
                logger.warn('⚠️  清理任务启动失败，跳过', cleanupError);
            }
        });

    } catch (error) {
        logger.error('❌ 服务器启动失败', error);
        process.exit(1);
    }
}

// 优雅关闭
process.on('SIGTERM', async () => {
    console.log('🔄 SIGTERM received, shutting down gracefully...');
    await sequelize.close();
    process.exit(0);
});

process.on('SIGINT', async () => {
    console.log('🔄 SIGINT received, shutting down gracefully...');
    await sequelize.close();
    process.exit(0);
});

// 启动服务器
startServer();

module.exports = app;
