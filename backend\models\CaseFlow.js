const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const CaseFlow = sequelize.define('CaseFlow', {
    id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
    },
    case_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '案件ID',
        references: {
            model: 'cases',
            key: 'id'
        }
    },
    action: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: '操作类型',
        validate: {
            isIn: [['创建案件', '编辑案件', '删除案件', '恢复案件', '分配负责人', '状态变更', '添加备注', '上传文件', '案件归档', '案件撤销']]
        }
    },
    operator_id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        comment: '操作人ID',
        references: {
            model: 'users',
            key: 'id'
        }
    },
    remark: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '备注说明',
    },
    old_value: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '变更前的值（JSON格式）',
    },
    new_value: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '变更后的值（JSON格式）',
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: '创建时间',
    },
}, {
    tableName: 'case_flows',
    timestamps: false,
    indexes: [
        {
            fields: ['case_id']
        },
        {
            fields: ['operator_id']
        },
        {
            fields: ['action']
        },
        {
            fields: ['created_at']
        }
    ]
});

module.exports = CaseFlow;
