import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { message } from 'antd';
import { 
  getToken, 
  getUser, 
  setToken, 
  setUser, 
  clearAuth, 
  isTokenExpiringSoon,
  parseToken 
} from '../utils/auth';
import { authAPI } from '../services/auth';

/**
 * 认证状态管理Hook
 */
export const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUserState] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // 检查认证状态
  const checkAuth = useCallback(() => {
    const token = getToken();
    const userData = getUser();
    
    if (token && userData) {
      setIsAuthenticated(true);
      setUserState(userData);
    } else {
      setIsAuthenticated(false);
      setUserState(null);
    }
    setLoading(false);
  }, []);

  // 登录
  const login = useCallback(async (credentials) => {
    try {
      const response = await authAPI.login(credentials);
      const { token, user } = response.data;

      setToken(token);
      setUser(user);
      setIsAuthenticated(true);
      setUserState(user);

      message.success('登录成功！');
      return { success: true };
    } catch (error) {
      console.error('登录失败:', error);
      return { success: false, error };
    }
  }, []);

  // 登出
  const logout = useCallback(async () => {
    try {
      // 调用后端登出接口（如果需要）
      await authAPI.logout?.();
    } catch (error) {
      console.error('登出请求失败:', error);
    } finally {
      // 清除本地认证信息
      clearAuth();
      setIsAuthenticated(false);
      setUserState(null);
      message.success('已安全退出');
      navigate('/login');
    }
  }, [navigate]);

  // 刷新Token
  const refreshToken = useCallback(async () => {
    try {
      const currentToken = getToken();
      if (!currentToken) {
        throw new Error('No token available');
      }

      const response = await authAPI.refreshToken();
      const { token, user } = response.data;

      setToken(token);
      setUser(user);
      setUserState(user);

      console.log('Token刷新成功');
      return true;
    } catch (error) {
      console.error('Token刷新失败:', error);
      // Token刷新失败，强制登出
      logout();
      return false;
    }
  }, [logout]);

  // 检查并自动刷新Token
  const checkAndRefreshToken = useCallback(async () => {
    const token = getToken();
    if (!token) return false;

    if (isTokenExpiringSoon(token)) {
      console.log('Token即将过期，尝试刷新...');
      return await refreshToken();
    }

    return true;
  }, [refreshToken]);

  // 获取用户信息
  const fetchUserProfile = useCallback(async () => {
    try {
      const response = await authAPI.getProfile();
      const userData = response.data;
      
      setUser(userData);
      setUserState(userData);
      
      return userData;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }, []);

  // 初始化认证状态
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  // 定期检查Token状态
  useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(() => {
      checkAndRefreshToken();
    }, 5 * 60 * 1000); // 每5分钟检查一次

    return () => clearInterval(interval);
  }, [isAuthenticated, checkAndRefreshToken]);

  // 页面可见性变化时检查Token
  useEffect(() => {
    if (!isAuthenticated) return;

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        checkAndRefreshToken();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [isAuthenticated, checkAndRefreshToken]);

  return {
    isAuthenticated,
    user,
    loading,
    login,
    logout,
    refreshToken,
    fetchUserProfile,
    checkAuth,
  };
};

/**
 * 权限检查Hook
 */
export const usePermission = () => {
  const { user } = useAuth();

  const hasRole = useCallback((roles) => {
    if (!user || !user.roles) return false;
    
    const userRoles = user.roles.map(role => role.name);
    const requiredRoles = Array.isArray(roles) ? roles : [roles];
    
    return requiredRoles.some(role => userRoles.includes(role));
  }, [user]);

  const isAdmin = useCallback(() => {
    return hasRole('admin');
  }, [hasRole]);

  const isLawyer = useCallback(() => {
    return hasRole(['admin', 'lawyer']);
  }, [hasRole]);

  return {
    hasRole,
    isAdmin,
    isLawyer,
  };
};

export default useAuth;
