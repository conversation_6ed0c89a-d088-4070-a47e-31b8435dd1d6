const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const CaseFieldDef = sequelize.define('CaseFieldDef', {
    id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
    },
    name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        comment: '字段英文名',
    },
    label: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: '字段显示名',
    },
    type: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: '字段类型',
    },
    required: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: '是否必填',
    },
    options: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '选项（如下拉）',
    },
    order: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        comment: '显示顺序',
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
}, {
    tableName: 'case_field_def',
    timestamps: false,
});

module.exports = CaseFieldDef; 