import { request } from './api';

/**
 * 案件管理相关 API
 */
export const casesAPI = {
  /**
   * 获取案件列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @param {string} params.status - 状态过滤
   * @param {string} params.type - 类型过滤
   * @param {string} params.priority - 优先级过滤
   * @param {string} params.search - 搜索关键词
   * @param {string} params.sort_by - 排序字段
   * @param {string} params.sort_order - 排序方向
   * @returns {Promise} 案件列表响应
   */
  getCases: (params = {}) => {
    return request.get('/cases', { params });
  },

  /**
   * 获取案件详情
   * @param {number} id - 案件ID
   * @returns {Promise} 案件详情响应
   */
  getCaseDetail: (id) => {
    return request.get(`/cases/${id}`);
  },

  /**
   * 创建新案件
   * @param {Object} caseData - 案件数据
   * @param {string} caseData.title - 案件标题
   * @param {string} caseData.type - 案件类型
   * @param {string} caseData.description - 案件描述
   * @param {string} caseData.priority - 优先级
   * @param {string} caseData.deadline - 截止日期
   * @param {number} caseData.amount - 涉案金额
   * @param {string} caseData.client_name - 客户名称
   * @param {string} caseData.client_contact - 客户联系方式
   * @param {number} caseData.owner_id - 负责人ID
   * @returns {Promise} 创建响应
   */
  createCase: (caseData) => {
    return request.post('/cases', caseData);
  },

  /**
   * 更新案件信息
   * @param {number} id - 案件ID
   * @param {Object} caseData - 更新的案件数据
   * @returns {Promise} 更新响应
   */
  updateCase: (id, caseData) => {
    return request.put(`/cases/${id}`, caseData);
  },

  /**
   * 更新案件状态
   * @param {number} id - 案件ID
   * @param {Object} statusData - 状态数据
   * @param {string} statusData.status - 新状态
   * @param {string} statusData.remark - 备注
   * @returns {Promise} 状态更新响应
   */
  updateCaseStatus: (id, statusData) => {
    return request.patch(`/cases/${id}/status`, statusData);
  },

  /**
   * 分配案件负责人
   * @param {number} id - 案件ID
   * @param {Object} assignData - 分配数据
   * @param {number} assignData.owner_id - 新负责人ID
   * @param {string} assignData.remark - 备注
   * @returns {Promise} 分配响应
   */
  assignCase: (id, assignData) => {
    return request.post(`/cases/${id}/assign`, assignData);
  },

  /**
   * 删除案件
   * @param {number} id - 案件ID
   * @returns {Promise} 删除响应
   */
  deleteCase: (id) => {
    return request.delete(`/cases/${id}`);
  },
};

/**
 * 案件常量
 */
export const CASE_CONSTANTS = {
  // 案件状态
  STATUS: {
    PENDING: '待处理',
    IN_PROGRESS: '处理中',
    COMPLETED: '已结案',
    ARCHIVED: '已归档',
    CANCELLED: '已撤销',
  },

  // 案件类型
  TYPES: {
    CONTRACT_DISPUTE: '合同纠纷',
    LABOR_DISPUTE: '劳动争议',
    INTELLECTUAL_PROPERTY: '知识产权',
    CORPORATE_LAW: '公司法务',
    OTHER: '其他',
  },

  // 优先级
  PRIORITIES: {
    LOW: '低',
    MEDIUM: '中',
    HIGH: '高',
    URGENT: '紧急',
  },

  // 状态颜色映射
  STATUS_COLORS: {
    '待处理': 'orange',
    '处理中': 'blue',
    '已结案': 'green',
    '已归档': 'gray',
    '已撤销': 'red',
    '已删除': 'red',
  },

  // 优先级颜色映射
  PRIORITY_COLORS: {
    '低': 'green',
    '中': 'blue',
    '高': 'orange',
    '紧急': 'red',
  },
};

/**
 * 用户管理相关 API
 */
export const usersAPI = {
  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 用户列表响应
   */
  getUsers: (params = {}) => {
    return request.get('/users', { params });
  },
};
