import { request } from './api';

/**
 * 认证相关 API
 */
export const authAPI = {
  /**
   * 用户登录
   * @param {Object} credentials - 登录凭据
   * @param {string} credentials.username - 用户名
   * @param {string} credentials.password - 密码
   * @returns {Promise} 登录响应
   */
  login: (credentials) => {
    return request.post('/auth/login', credentials);
  },

  /**
   * 用户注册
   * @param {Object} userData - 用户数据
   * @param {string} userData.username - 用户名
   * @param {string} userData.password - 密码
   * @param {string} userData.real_name - 真实姓名
   * @param {string} userData.email - 邮箱
   * @returns {Promise} 注册响应
   */
  register: (userData) => {
    return request.post('/auth/register', userData);
  },

  /**
   * 获取当前用户信息
   * @returns {Promise} 用户信息响应
   */
  getProfile: () => {
    return request.get('/auth/me');
  },

  /**
   * 用户登出
   * @returns {Promise} 登出响应
   */
  logout: () => {
    return request.post('/auth/logout');
  },

  /**
   * 刷新Token
   * @returns {Promise} 刷新响应
   */
  refreshToken: () => {
    return request.post('/auth/refresh');
  },

  /**
   * 刷新 token
   * @returns {Promise} 刷新响应
   */
  refreshToken: () => {
    return request.post('/auth/refresh');
  },

  /**
   * 用户登出
   * @returns {Promise} 登出响应
   */
  logout: () => {
    return request.post('/auth/logout');
  },
};
