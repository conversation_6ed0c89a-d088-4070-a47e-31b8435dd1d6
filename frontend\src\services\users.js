import api from './api';

/**
 * 用户管理API服务
 */
export const usersAPI = {
  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getUsers: (params = {}) => {
    return api.get('/users', { params });
  },

  /**
   * 获取用户详情
   * @param {number} id - 用户ID
   * @returns {Promise} API响应
   */
  getUser: (id) => {
    return api.get(`/users/${id}`);
  },

  /**
   * 创建用户
   * @param {Object} data - 用户数据
   * @returns {Promise} API响应
   */
  createUser: (data) => {
    return api.post('/users', data);
  },

  /**
   * 更新用户信息
   * @param {number} id - 用户ID
   * @param {Object} data - 更新数据
   * @returns {Promise} API响应
   */
  updateUser: (id, data) => {
    return api.put(`/users/${id}`, data);
  },

  /**
   * 删除用户
   * @param {number} id - 用户ID
   * @returns {Promise} API响应
   */
  deleteUser: (id) => {
    return api.delete(`/users/${id}`);
  },

  /**
   * 更新用户权限（角色）
   * @param {number} userId - 用户ID
   * @param {Array} roleIds - 角色ID数组
   * @returns {Promise} API响应
   */
  updateUserRoles: (userId, roleIds) => {
    return api.put(`/users/${userId}`, { role_ids: roleIds });
  }
};
