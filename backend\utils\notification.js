const { Notify, User, Case } = require('../models');

class NotificationService {
    /**
     * 发送通知给指定用户
     * @param {number|Array} userIds - 用户ID或用户ID数组
     * @param {string} title - 通知标题
     * @param {string} content - 通知内容
     * @param {string} type - 通知类型
     * @param {number} senderId - 发送者ID
     * @param {number} relatedId - 关联ID
     * @param {string} relatedType - 关联类型
     */
    static async sendNotification(userIds, title, content, type = '系统通知', senderId = null, relatedId = null, relatedType = null) {
        try {
            const targetUserIds = Array.isArray(userIds) ? userIds : [userIds];
            const notifications = [];

            for (const userId of targetUserIds) {
                const notification = await Notify.create({
                    user_id: userId,
                    title,
                    content,
                    type,
                    sender_id: senderId,
                    related_id: relatedId,
                    related_type: relatedType
                });
                notifications.push(notification);
            }

            console.log(`✅ 通知发送成功: ${notifications.length} 条通知已发送`);
            return notifications;
        } catch (error) {
            console.error('❌ 发送通知失败:', error);
            throw error;
        }
    }

    /**
     * 发送案件相关通知
     * @param {number} caseId - 案件ID
     * @param {string} action - 操作类型
     * @param {number} operatorId - 操作者ID
     * @param {string} remark - 备注
     */
    static async sendCaseNotification(caseId, action, operatorId, remark = '') {
        try {
            const caseRecord = await Case.findByPk(caseId, {
                include: [{
                    model: User,
                    as: 'owner',
                    attributes: ['id', 'real_name']
                }]
            });

            if (!caseRecord) {
                throw new Error('案件不存在');
            }

            const operator = await User.findByPk(operatorId, {
                attributes: ['real_name']
            });

            let title, content, type;

            switch (action) {
                case '案件分配':
                    title = '案件分配通知';
                    content = `您有新的案件被分配：${caseRecord.title} (${caseRecord.case_no})`;
                    type = '任务分配';
                    break;
                case '状态变更':
                    title = '案件状态变更';
                    content = `案件 ${caseRecord.title} (${caseRecord.case_no}) 的状态已变更为：${caseRecord.status}`;
                    if (remark) content += `\n备注：${remark}`;
                    type = '状态变更';
                    break;
                case '文件上传':
                    title = '案件文件更新';
                    content = `案件 ${caseRecord.title} (${caseRecord.case_no}) 有新文件上传`;
                    if (remark) content += `\n说明：${remark}`;
                    type = '案件提醒';
                    break;
                case '案件归档':
                    title = '案件归档通知';
                    content = `案件 ${caseRecord.title} (${caseRecord.case_no}) 已归档`;
                    if (remark) content += `\n归档说明：${remark}`;
                    type = '案件提醒';
                    break;
                default:
                    title = '案件更新通知';
                    content = `案件 ${caseRecord.title} (${caseRecord.case_no}) 有更新`;
                    if (remark) content += `\n说明：${remark}`;
                    type = '案件提醒';
            }

            // 发送给案件负责人
            await this.sendNotification(
                caseRecord.owner_id,
                title,
                content,
                type,
                operatorId,
                caseId,
                'case'
            );

            console.log(`✅ 案件通知发送成功: ${action} - ${caseRecord.case_no}`);
        } catch (error) {
            console.error('❌ 发送案件通知失败:', error);
            throw error;
        }
    }

    /**
     * 发送截止日期提醒
     * @param {number} days - 提前天数
     */
    static async sendDeadlineReminders(days = 3) {
        try {
            const reminderDate = new Date();
            reminderDate.setDate(reminderDate.getDate() + days);

            const casesNearDeadline = await Case.findAll({
                where: {
                    deadline: {
                        [require('sequelize').Op.lte]: reminderDate
                    },
                    status: {
                        [require('sequelize').Op.notIn]: ['已结案', '已归档', '已撤销']
                    }
                },
                include: [{
                    model: User,
                    as: 'owner',
                    attributes: ['id', 'real_name']
                }]
            });

            for (const caseRecord of casesNearDeadline) {
                const daysLeft = Math.ceil((new Date(caseRecord.deadline) - new Date()) / (1000 * 60 * 60 * 24));
                
                let title, content;
                if (daysLeft <= 0) {
                    title = '案件截止日期已到';
                    content = `案件 ${caseRecord.title} (${caseRecord.case_no}) 已到截止日期，请及时处理！`;
                } else {
                    title = '案件截止日期提醒';
                    content = `案件 ${caseRecord.title} (${caseRecord.case_no}) 将在 ${daysLeft} 天后到期，请注意及时处理。`;
                }

                await this.sendNotification(
                    caseRecord.owner_id,
                    title,
                    content,
                    '截止提醒',
                    null,
                    caseRecord.id,
                    'case'
                );
            }

            console.log(`✅ 截止日期提醒发送完成: ${casesNearDeadline.length} 条提醒`);
            return casesNearDeadline.length;
        } catch (error) {
            console.error('❌ 发送截止日期提醒失败:', error);
            throw error;
        }
    }

    /**
     * 发送系统公告
     * @param {string} title - 公告标题
     * @param {string} content - 公告内容
     * @param {number} senderId - 发送者ID
     */
    static async sendSystemAnnouncement(title, content, senderId) {
        try {
            // 获取所有活跃用户
            const activeUsers = await User.findAll({
                where: { status: 1 },
                attributes: ['id']
            });

            const userIds = activeUsers.map(user => user.id);

            await this.sendNotification(
                userIds,
                title,
                content,
                '系统通知',
                senderId
            );

            console.log(`✅ 系统公告发送完成: ${userIds.length} 个用户`);
            return userIds.length;
        } catch (error) {
            console.error('❌ 发送系统公告失败:', error);
            throw error;
        }
    }

    /**
     * 清理过期通知
     * @param {number} days - 保留天数，默认30天
     */
    static async cleanupOldNotifications(days = 30) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - days);

            const deletedCount = await Notify.destroy({
                where: {
                    created_at: {
                        [require('sequelize').Op.lt]: cutoffDate
                    },
                    status: 1 // 只删除已读的通知
                }
            });

            console.log(`✅ 通知清理完成: 删除了 ${deletedCount} 条过期通知`);
            return deletedCount;
        } catch (error) {
            console.error('❌ 清理通知失败:', error);
            throw error;
        }
    }
}

module.exports = NotificationService;
