const http = require('http');
const url = require('url');

console.log('🔍 Starting pure HTTP server...');

const PORT = 3001;

const server = http.createServer((req, res) => {
    console.log(`📍 Request: ${req.method} ${req.url}`);
    
    // 设置 CORS 头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Content-Type', 'application/json');
    
    const parsedUrl = url.parse(req.url, true);
    const path = parsedUrl.pathname;
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    if (path === '/') {
        res.writeHead(200);
        res.end(JSON.stringify({
            message: '纯 HTTP 服务器',
            version: '1.0.0',
            status: 'running',
            port: PORT
        }));
    } else if (path === '/health') {
        res.writeHead(200);
        res.end(JSON.stringify({
            status: 'healthy',
            timestamp: new Date().toISOString(),
            port: PORT
        }));
    } else {
        res.writeHead(404);
        res.end(JSON.stringify({
            error: 'Not found',
            path: path
        }));
    }
});

server.on('error', (error) => {
    console.error('❌ Server error:', error);
});

server.on('listening', () => {
    console.log('✅ Server is listening on', server.address());
});

server.listen(PORT, '127.0.0.1', () => {
    console.log(`🚀 Pure HTTP server running on port ${PORT}`);
    console.log(`📍 Server URL: http://127.0.0.1:${PORT}`);
    console.log(`🏥 Health check: http://127.0.0.1:${PORT}/health`);
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('🔄 SIGTERM received');
    server.close(() => process.exit(0));
});

process.on('SIGINT', () => {
    console.log('🔄 SIGINT received');
    server.close(() => process.exit(0));
});

console.log('✅ Pure server setup complete');
