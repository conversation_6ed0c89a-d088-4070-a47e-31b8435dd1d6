<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回收站功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #1890ff;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .loading {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button:disabled {
            background-color: #d9d9d9;
            cursor: not-allowed;
        }
        button.danger {
            background-color: #ff4d4f;
        }
        button.danger:hover {
            background-color: #ff7875;
        }
        button.success {
            background-color: #52c41a;
        }
        button.success:hover {
            background-color: #73d13d;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .function-group {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .function-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .case-item {
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            background-color: #fafafa;
        }
        .case-title {
            font-weight: bold;
            color: #333;
        }
        .case-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>🗑️ 案件回收站功能测试</h1>
    
    <div class="test-section">
        <h2 class="test-title">🎯 测试目标</h2>
        <p>验证案件删除与恢复机制的完整功能：</p>
        <ul>
            <li>✅ 软删除机制：删除案件时标记为已删除，不物理删除</li>
            <li>✅ 回收站功能：查看已删除案件，支持恢复</li>
            <li>✅ 永久删除：从回收站彻底删除案件</li>
            <li>✅ 操作日志：记录所有删除和恢复操作</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">📋 测试流程</h2>
        
        <div class="function-group">
            <div class="function-title">1. 创建测试案件</div>
            <button onclick="createTestCase()">创建测试案件</button>
            <div id="create-result" class="test-result loading">等待创建...</div>
        </div>

        <div class="function-group">
            <div class="function-title">2. 软删除案件</div>
            <button onclick="deleteCase()" id="delete-btn" disabled class="danger">软删除案件</button>
            <div id="delete-result" class="test-result loading">等待删除...</div>
        </div>

        <div class="function-group">
            <div class="function-title">3. 查看回收站</div>
            <button onclick="getRecycleBin()">查看回收站</button>
            <div id="recycle-result" class="test-result loading">等待查看...</div>
            <div id="recycle-cases"></div>
        </div>

        <div class="function-group">
            <div class="function-title">4. 恢复案件</div>
            <button onclick="restoreCase()" id="restore-btn" disabled class="success">恢复案件</button>
            <div id="restore-result" class="test-result loading">等待恢复...</div>
        </div>

        <div class="function-group">
            <div class="function-title">5. 再次删除并永久删除</div>
            <button onclick="deleteAgain()" id="delete-again-btn" disabled class="danger">再次删除</button>
            <button onclick="permanentDelete()" id="permanent-btn" disabled class="danger">永久删除</button>
            <div id="permanent-result" class="test-result loading">等待永久删除...</div>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">📊 测试结果汇总</h2>
        <div id="summary" class="test-result loading">请先运行测试...</div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8001/api';
        let testCaseId = null;
        let authToken = null;
        let testResults = {};

        // 获取认证token
        async function getAuthToken() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                const data = await response.json();
                return data.token;
            } catch (error) {
                console.error('登录失败:', error);
                return null;
            }
        }

        // 创建测试案件
        async function createTestCase() {
            const resultDiv = document.getElementById('create-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在创建测试案件...';

            try {
                authToken = await getAuthToken();
                if (!authToken) {
                    throw new Error('无法获取认证token');
                }

                const testCase = {
                    title: '回收站功能测试案件',
                    type: '合同纠纷',
                    description: '这是一个用于测试回收站功能的测试案件',
                    priority: '中',
                    client_name: '测试客户',
                    client_contact: '13800138000'
                };

                const response = await fetch(`${API_BASE}/cases`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(testCase)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data?.case) {
                    testCaseId = data.data.case.id;
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 测试案件创建成功<br>
                        案件ID: ${testCaseId}<br>
                        案件编号: ${data.data.case.case_no}<br>
                    `;
                    testResults.create = true;
                    
                    // 启用删除按钮
                    document.getElementById('delete-btn').disabled = false;
                } else {
                    throw new Error('返回数据格式不正确');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 创建测试案件失败: ${error.message}`;
                testResults.create = false;
            }
            updateSummary();
        }

        // 软删除案件
        async function deleteCase() {
            const resultDiv = document.getElementById('delete-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在软删除案件...';

            try {
                const response = await fetch(`${API_BASE}/cases/${testCaseId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.message) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 案件软删除成功<br>
                        案件已移入回收站<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    testResults.delete = true;
                    
                    // 启用恢复按钮
                    document.getElementById('restore-btn').disabled = false;
                } else {
                    throw new Error('返回数据格式不正确');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 软删除案件失败: ${error.message}`;
                testResults.delete = false;
            }
            updateSummary();
        }

        // 查看回收站
        async function getRecycleBin() {
            const resultDiv = document.getElementById('recycle-result');
            const casesDiv = document.getElementById('recycle-cases');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在查看回收站...';

            try {
                const response = await fetch(`${API_BASE}/cases/recycle?page=1&limit=10`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && Array.isArray(data.data)) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 回收站查看成功<br>
                        已删除案件数量: ${data.data.length}<br>
                    `;
                    
                    // 显示回收站中的案件
                    casesDiv.innerHTML = '';
                    data.data.forEach(caseItem => {
                        const caseDiv = document.createElement('div');
                        caseDiv.className = 'case-item';
                        caseDiv.innerHTML = `
                            <div class="case-title">${caseItem.title}</div>
                            <div class="case-info">
                                案件编号: ${caseItem.case_no} | 
                                删除时间: ${new Date(caseItem.deleted_at).toLocaleString()} |
                                负责人: ${caseItem.owner?.real_name || '未知'}
                            </div>
                        `;
                        casesDiv.appendChild(caseDiv);
                    });
                    
                    testResults.recycle = true;
                } else {
                    throw new Error('返回数据格式不正确');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 查看回收站失败: ${error.message}`;
                testResults.recycle = false;
            }
            updateSummary();
        }

        // 恢复案件
        async function restoreCase() {
            const resultDiv = document.getElementById('restore-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在恢复案件...';

            try {
                const response = await fetch(`${API_BASE}/cases/${testCaseId}/restore`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.message) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 案件恢复成功<br>
                        案件已从回收站恢复<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    testResults.restore = true;
                    
                    // 启用再次删除按钮
                    document.getElementById('delete-again-btn').disabled = false;
                } else {
                    throw new Error('返回数据格式不正确');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 恢复案件失败: ${error.message}`;
                testResults.restore = false;
            }
            updateSummary();
        }

        // 再次删除案件
        async function deleteAgain() {
            try {
                const response = await fetch(`${API_BASE}/cases/${testCaseId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    // 启用永久删除按钮
                    document.getElementById('permanent-btn').disabled = false;
                    alert('案件已再次删除，现在可以进行永久删除测试');
                }
            } catch (error) {
                alert('再次删除失败: ' + error.message);
            }
        }

        // 永久删除案件
        async function permanentDelete() {
            const resultDiv = document.getElementById('permanent-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在永久删除案件...';

            try {
                const response = await fetch(`${API_BASE}/cases/${testCaseId}/permanent`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.message) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 案件永久删除成功<br>
                        案件已从数据库中彻底删除<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    testResults.permanent = true;
                } else {
                    throw new Error('返回数据格式不正确');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 永久删除案件失败: ${error.message}`;
                testResults.permanent = false;
            }
            updateSummary();
        }

        // 更新测试汇总
        function updateSummary() {
            const summaryDiv = document.getElementById('summary');
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(result => result === true).length;
            
            if (totalTests === 0) {
                summaryDiv.className = 'test-result loading';
                summaryDiv.innerHTML = '请先运行测试...';
                return;
            }

            const testNames = {
                create: '创建案件',
                delete: '软删除',
                recycle: '查看回收站',
                restore: '恢复案件',
                permanent: '永久删除'
            };

            let details = '<br>测试详情:<br>';
            for (const [key, result] of Object.entries(testResults)) {
                const icon = result ? '✅' : '❌';
                details += `${icon} ${testNames[key] || key}<br>`;
            }

            if (passedTests === totalTests) {
                summaryDiv.className = 'test-result success';
                summaryDiv.innerHTML = `🎉 所有回收站功能测试通过! (${passedTests}/${totalTests})${details}`;
            } else {
                summaryDiv.className = 'test-result error';
                summaryDiv.innerHTML = `⚠️ 部分功能测试失败 (${passedTests}/${totalTests})${details}`;
            }
        }
    </script>
</body>
</html>
