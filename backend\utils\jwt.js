const jwt = require('jsonwebtoken');
const { promisify } = require('util');

const JWT_SECRET = process.env.JWT_SECRET || 'sie_SuperKey2025';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

class JWTUtils {
    /**
     * 生成JWT token
     * @param {Object} payload - 载荷数据
     * @param {string} expiresIn - 过期时间
     * @returns {string} JWT token
     */
    static generateToken(payload, expiresIn = JWT_EXPIRES_IN) {
        return jwt.sign(payload, JWT_SECRET, { expiresIn });
    }

    /**
     * 验证JWT token
     * @param {string} token - JWT token
     * @returns {Object} 解码后的载荷
     */
    static async verifyToken(token) {
        try {
            const verify = promisify(jwt.verify);
            return await verify(token, JWT_SECRET);
        } catch (error) {
            throw new Error('Invalid token');
        }
    }

    /**
     * 解码JWT token（不验证签名）
     * @param {string} token - JWT token
     * @returns {Object} 解码后的载荷
     */
    static decodeToken(token) {
        return jwt.decode(token);
    }

    /**
     * 从请求头中提取token
     * @param {Object} req - Express请求对象
     * @returns {string|null} JWT token
     */
    static extractTokenFromHeader(req) {
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            return authHeader.substring(7);
        }
        return null;
    }

    /**
     * 生成用户token载荷
     * @param {Object} user - 用户对象
     * @returns {Object} token载荷
     */
    static createUserPayload(user) {
        return {
            id: user.id,
            username: user.username,
            real_name: user.real_name,
            email: user.email,
            roles: user.roles ? user.roles.map(role => role.name) : [],
            iat: Math.floor(Date.now() / 1000)
        };
    }

    /**
     * 刷新token
     * @param {string} token - 旧token
     * @returns {string} 新token
     */
    static async refreshToken(token) {
        try {
            const decoded = await this.verifyToken(token);
            // 移除过期时间相关字段
            delete decoded.iat;
            delete decoded.exp;
            
            return this.generateToken(decoded);
        } catch (error) {
            throw new Error('Cannot refresh invalid token');
        }
    }
}

module.exports = JWTUtils;
