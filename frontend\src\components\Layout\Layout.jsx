import React, { useState, useEffect } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Layout as AntLayout,
  Menu,
  Avatar,
  Dropdown,
  Badge,
  Button,
  message,
  Drawer,
} from 'antd';
import {
  DashboardOutlined,
  FileTextOutlined,
  FolderOutlined,
  BellOutlined,
  Bar<PERSON>hartOutlined,
  UserOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';

import { getUser, clearAuth, isAdmin } from '../../utils/auth';
import { authAPI } from '../../services/auth';
import Breadcrumb from './Breadcrumb';
import './Layout.css';

const { Header, Sider, Content } = AntLayout;

const Layout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [user, setUser] = useState(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // 获取用户信息
    const userData = getUser();
    setUser(userData);
  }, []);

  // 响应式处理
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (mobile) {
        setCollapsed(true);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 菜单项配置
  const getMenuItems = () => {
    const baseItems = [
      {
        key: '/dashboard',
        icon: <DashboardOutlined />,
        label: '仪表板',
      },
      {
        key: '/cases',
        icon: <FileTextOutlined />,
        label: '案件管理',
        children: [
          {
            key: '/cases',
            label: '案件列表',
          },
          {
            key: '/cases/create',
            label: '创建案件',
          },
        ],
      },
      {
        key: '/files',
        icon: <FolderOutlined />,
        label: '文件管理',
      },
      {
        key: '/notifications',
        icon: <BellOutlined />,
        label: '通知消息',
      },
      {
        key: '/statistics',
        icon: <BarChartOutlined />,
        label: '统计报表',
      },
    ];

    // 管理员菜单
    if (isAdmin()) {
      baseItems.push({
        key: '/admin',
        icon: <UserOutlined />,
        label: '系统管理',
        children: [
          {
            key: '/admin/responsibles',
            label: '负责人管理',
          },
          {
            key: '/admin/permissions',
            label: '权限分配',
          },
        ],
      });
    }

    return baseItems;
  };

  const menuItems = getMenuItems();

  // 处理菜单点击
  const handleMenuClick = ({ key }) => {
    navigate(key);
    // 移动端点击菜单后关闭抽屉
    if (isMobile) {
      setMobileMenuVisible(false);
    }
  };

  // 处理登出
  const handleLogout = async () => {
    try {
      await authAPI.logout();
      message.success('登出成功');
    } catch (error) {
      console.error('登出失败:', error);
    } finally {
      clearAuth();
      navigate('/login');
    }
  };

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
      onClick: () => {
        message.info('个人信息功能开发中...');
      },
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const pathname = location.pathname;

    // 精确匹配
    if (pathname === '/cases/create') {
      return ['/cases/create'];
    }

    // 管理员页面
    if (pathname.startsWith('/admin/')) {
      return [pathname];
    }

    // 案件详情页面
    if (pathname.startsWith('/cases/') && pathname !== '/cases') {
      return ['/cases'];
    }

    // 其他页面
    return [pathname];
  };

  // 获取展开的菜单项
  const getOpenKeys = () => {
    const pathname = location.pathname;

    if (pathname.startsWith('/cases')) {
      return ['/cases'];
    }

    if (pathname.startsWith('/admin')) {
      return ['/admin'];
    }

    return [];
  };

  // 渲染菜单内容
  const renderMenuContent = () => (
    <>
      {/* Logo */}
      <div className="layout-logo">
        <h2 style={{
          color: 'white',
          textAlign: 'center',
          margin: '16px 0',
          fontSize: collapsed ? '16px' : '18px'
        }}>
          {collapsed ? 'SDM' : '案件管理平台'}
        </h2>
      </div>

      {/* 菜单 */}
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={getSelectedKeys()}
        defaultOpenKeys={getOpenKeys()}
        items={menuItems}
        onClick={handleMenuClick}
        style={{
          flex: 1,
          overflowY: 'auto',
          overflowX: 'hidden',
          border: 'none'
        }}
      />
    </>
  );

  return (
    <AntLayout style={{ height: '100vh', overflow: 'hidden' }}>
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          theme="dark"
          width={256}
          style={{
            height: '100vh',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          {renderMenuContent()}
        </Sider>
      )}

      {/* 移动端抽屉菜单 */}
      <Drawer
        title="菜单"
        placement="left"
        onClose={() => setMobileMenuVisible(false)}
        open={mobileMenuVisible}
        bodyStyle={{ padding: 0 }}
        width={256}
        style={{ display: isMobile ? 'block' : 'none' }}
      >
        <div style={{
          background: '#001529',
          height: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}>
          {renderMenuContent()}
        </div>
      </Drawer>

      {/* 主内容区 */}
      <AntLayout>
        {/* 顶部导航 */}
        <Header style={{
          padding: '0 24px',
          background: '#fff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 1px 4px rgba(0,21,41,.08)'
        }}>
          {/* 折叠按钮 */}
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => {
              if (isMobile) {
                setMobileMenuVisible(!mobileMenuVisible);
              } else {
                setCollapsed(!collapsed);
              }
            }}
            style={{ fontSize: '16px', width: 64, height: 64 }}
          />

          {/* 右侧用户信息 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            {/* 通知图标 */}
            <Badge count={0} size="small">
              <Button
                type="text"
                icon={<BellOutlined style={{ fontSize: '18px' }} />}
                onClick={() => navigate('/notifications')}
              />
            </Badge>

            {/* 用户信息 */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <div style={{
                display: 'flex',
                alignItems: 'center',
                cursor: 'pointer',
                padding: '8px 12px',
                borderRadius: '6px',
                transition: 'background-color 0.3s'
              }}>
                <Avatar
                  size="small"
                  icon={<UserOutlined />}
                  style={{ marginRight: '8px' }}
                />
                <span style={{ fontSize: '14px' }}>
                  {user?.real_name || user?.username || '用户'}
                </span>
              </div>
            </Dropdown>
          </div>
        </Header>

        {/* 内容区域 */}
        <Content style={{
          height: 'calc(100vh - 64px)',
          overflowY: 'auto',
          overflowX: 'hidden',
          padding: isMobile ? '16px' : '24px',
          background: '#f5f5f5'
        }}>
          <div style={{
            background: '#fff',
            borderRadius: '12px',
            padding: isMobile ? '16px' : '32px',
            minHeight: 'calc(100vh - 112px)',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)'
          }}>
            {/* 面包屑导航 */}
            <Breadcrumb />

            {/* 页面内容 */}
            <Outlet />
          </div>
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
