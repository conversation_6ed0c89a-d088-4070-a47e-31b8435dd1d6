const express = require('express');
const router = express.Router();
const { Role } = require('../models');
const { authenticate, requireAdmin } = require('../middleware/auth');

/**
 * 获取所有角色列表
 * GET /api/roles
 */
router.get('/', authenticate, requireAdmin, async (req, res) => {
    try {
        const roles = await Role.findAll({
            attributes: ['id', 'name', 'description', 'created_at'],
            order: [['created_at', 'ASC']]
        });

        res.json({
            success: true,
            data: {
                roles
            },
            roles  // 为了兼容性，同时提供这个字段
        });

    } catch (error) {
        console.error('Get roles error:', error);
        res.status(500).json({
            error: 'Failed to get roles',
            code: 'GET_ROLES_ERROR'
        });
    }
});

/**
 * 获取角色详情
 * GET /api/roles/:id
 */
router.get('/:id', authenticate, requireAdmin, async (req, res) => {
    try {
        const role = await Role.findByPk(req.params.id, {
            attributes: ['id', 'name', 'description', 'created_at']
        });

        if (!role) {
            return res.status(404).json({
                error: 'Role not found',
                code: 'ROLE_NOT_FOUND'
            });
        }

        res.json({
            success: true,
            data: {
                role
            }
        });

    } catch (error) {
        console.error('Get role error:', error);
        res.status(500).json({
            error: 'Failed to get role',
            code: 'GET_ROLE_ERROR'
        });
    }
});

module.exports = router;
