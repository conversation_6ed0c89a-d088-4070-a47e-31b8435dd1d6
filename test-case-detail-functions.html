<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案件详情功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #1890ff;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .loading {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #40a9ff;
        }
        button:disabled {
            background-color: #d9d9d9;
            cursor: not-allowed;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .function-group {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .function-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>🔧 案件详情功能完整测试</h1>
    
    <div class="test-section">
        <h2 class="test-title">🎯 测试目标</h2>
        <p>验证案件详情页面的所有功能是否正常工作：</p>
        <ul>
            <li>✅ 编辑案件信息</li>
            <li>✅ 更新案件状态</li>
            <li>✅ 重新分配负责人</li>
            <li>✅ 删除案件操作</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">📋 测试步骤</h2>
        
        <div class="function-group">
            <div class="function-title">1. 创建测试案件</div>
            <button onclick="createTestCase()">创建测试案件</button>
            <div id="create-result" class="test-result loading">等待创建...</div>
        </div>

        <div class="function-group">
            <div class="function-title">2. 获取案件详情</div>
            <button onclick="getCaseDetail()" id="detail-btn" disabled>获取案件详情</button>
            <div id="detail-result" class="test-result loading">等待获取...</div>
        </div>

        <div class="function-group">
            <div class="function-title">3. 编辑案件信息</div>
            <button onclick="editCase()" id="edit-btn" disabled>编辑案件</button>
            <div id="edit-result" class="test-result loading">等待编辑...</div>
        </div>

        <div class="function-group">
            <div class="function-title">4. 更新案件状态</div>
            <button onclick="updateCaseStatus()" id="status-btn" disabled>更新状态</button>
            <div id="status-result" class="test-result loading">等待更新...</div>
        </div>

        <div class="function-group">
            <div class="function-title">5. 分配负责人</div>
            <button onclick="assignCase()" id="assign-btn" disabled>分配负责人</button>
            <div id="assign-result" class="test-result loading">等待分配...</div>
        </div>

        <div class="function-group">
            <div class="function-title">6. 删除案件</div>
            <button onclick="deleteCase()" id="delete-btn" disabled style="background-color: #ff4d4f;">删除案件</button>
            <div id="delete-result" class="test-result loading">等待删除...</div>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">📊 测试结果汇总</h2>
        <div id="summary" class="test-result loading">请先运行测试...</div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8001/api';
        let testCaseId = null;
        let authToken = null;
        let testResults = {};

        // 获取认证token
        async function getAuthToken() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                const data = await response.json();
                return data.token;
            } catch (error) {
                console.error('登录失败:', error);
                return null;
            }
        }

        // 创建测试案件
        async function createTestCase() {
            const resultDiv = document.getElementById('create-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在创建测试案件...';

            try {
                authToken = await getAuthToken();
                if (!authToken) {
                    throw new Error('无法获取认证token');
                }

                const testCase = {
                    title: '案件详情功能测试案件',
                    type: '合同纠纷',
                    description: '这是一个用于测试案件详情页面所有功能的测试案件',
                    priority: '高',
                    client_name: '测试客户公司',
                    client_contact: '13800138000',
                    amount: 100000
                };

                const response = await fetch(`${API_BASE}/cases`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(testCase)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data?.case) {
                    testCaseId = data.data.case.id;
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 测试案件创建成功<br>
                        案件ID: ${testCaseId}<br>
                        案件编号: ${data.data.case.case_no}<br>
                        <pre>${JSON.stringify(data.data.case, null, 2)}</pre>
                    `;
                    testResults.create = true;
                    
                    // 启用其他按钮
                    document.getElementById('detail-btn').disabled = false;
                    document.getElementById('edit-btn').disabled = false;
                    document.getElementById('status-btn').disabled = false;
                    document.getElementById('assign-btn').disabled = false;
                    document.getElementById('delete-btn').disabled = false;
                } else {
                    throw new Error('返回数据格式不正确');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 创建测试案件失败: ${error.message}`;
                testResults.create = false;
            }
            updateSummary();
        }

        // 获取案件详情
        async function getCaseDetail() {
            const resultDiv = document.getElementById('detail-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在获取案件详情...';

            try {
                const response = await fetch(`${API_BASE}/cases/${testCaseId}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.success && data.data?.case) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 案件详情获取成功<br>
                        包含负责人信息: ${data.data.case.owner ? '是' : '否'}<br>
                        包含流转记录: ${data.data.case.flows ? '是' : '否'}<br>
                        <pre>${JSON.stringify(data.data.case, null, 2)}</pre>
                    `;
                    testResults.detail = true;
                } else {
                    throw new Error('返回数据格式不正确');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 获取案件详情失败: ${error.message}`;
                testResults.detail = false;
            }
            updateSummary();
        }

        // 编辑案件
        async function editCase() {
            const resultDiv = document.getElementById('edit-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在编辑案件...';

            try {
                const updateData = {
                    title: '案件详情功能测试案件 - 已编辑',
                    description: '这是一个已经被编辑过的测试案件',
                    priority: '紧急',
                    amount: 150000
                };

                const response = await fetch(`${API_BASE}/cases/${testCaseId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(updateData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.message) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 案件编辑成功<br>
                        更新的字段: 标题、描述、优先级、金额<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    testResults.edit = true;
                } else {
                    throw new Error('返回数据格式不正确');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 编辑案件失败: ${error.message}`;
                testResults.edit = false;
            }
            updateSummary();
        }

        // 更新案件状态
        async function updateCaseStatus() {
            const resultDiv = document.getElementById('status-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在更新案件状态...';

            try {
                const statusData = {
                    status: '处理中',
                    remark: '开始处理测试案件'
                };

                const response = await fetch(`${API_BASE}/cases/${testCaseId}/status`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(statusData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.message) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 案件状态更新成功<br>
                        新状态: ${data.case?.status || '处理中'}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    testResults.status = true;
                } else {
                    throw new Error('返回数据格式不正确');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 更新案件状态失败: ${error.message}`;
                testResults.status = false;
            }
            updateSummary();
        }

        // 分配负责人
        async function assignCase() {
            const resultDiv = document.getElementById('assign-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在分配负责人...';

            try {
                const assignData = {
                    owner_id: 1, // 假设分配给用户ID为1的用户
                    remark: '重新分配负责人进行测试'
                };

                const response = await fetch(`${API_BASE}/cases/${testCaseId}/assign`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(assignData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.message) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 负责人分配成功<br>
                        新负责人: ${data.case?.owner_name || '未知'}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    testResults.assign = true;
                } else {
                    throw new Error('返回数据格式不正确');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 分配负责人失败: ${error.message}`;
                testResults.assign = false;
            }
            updateSummary();
        }

        // 删除案件
        async function deleteCase() {
            const resultDiv = document.getElementById('delete-result');
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = '正在删除案件...';

            try {
                const response = await fetch(`${API_BASE}/cases/${testCaseId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                if (data.message) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        ✅ 案件删除成功<br>
                        删除的案件ID: ${data.case_id}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    testResults.delete = true;
                } else {
                    throw new Error('返回数据格式不正确');
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ 删除案件失败: ${error.message}`;
                testResults.delete = false;
            }
            updateSummary();
        }

        // 更新测试汇总
        function updateSummary() {
            const summaryDiv = document.getElementById('summary');
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(result => result === true).length;
            
            if (totalTests === 0) {
                summaryDiv.className = 'test-result loading';
                summaryDiv.innerHTML = '请先运行测试...';
                return;
            }

            const testNames = {
                create: '创建案件',
                detail: '获取详情',
                edit: '编辑案件',
                status: '更新状态',
                assign: '分配负责人',
                delete: '删除案件'
            };

            let details = '<br>测试详情:<br>';
            for (const [key, result] of Object.entries(testResults)) {
                const icon = result ? '✅' : '❌';
                details += `${icon} ${testNames[key] || key}<br>`;
            }

            if (passedTests === totalTests) {
                summaryDiv.className = 'test-result success';
                summaryDiv.innerHTML = `🎉 所有功能测试通过! (${passedTests}/${totalTests})${details}`;
            } else {
                summaryDiv.className = 'test-result error';
                summaryDiv.innerHTML = `⚠️ 部分功能测试失败 (${passedTests}/${totalTests})${details}`;
            }
        }
    </script>
</body>
</html>
