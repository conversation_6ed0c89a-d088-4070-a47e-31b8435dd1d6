import React from 'react';
import { message, notification } from 'antd';
import { ExclamationCircleOutlined, WifiOutlined, CloseCircleOutlined } from '@ant-design/icons';

/**
 * 全局错误处理工具
 */
class ErrorHandler {
  constructor() {
    this.retryCount = new Map(); // 记录重试次数
    this.maxRetries = 3; // 最大重试次数
    this.retryDelay = 1000; // 重试延迟（毫秒）
  }

  /**
   * 处理API错误
   * @param {Error} error - 错误对象
   * @param {Object} options - 处理选项
   */
  handleApiError(error, options = {}) {
    const {
      showMessage = true,
      showNotification = false,
      customMessage = null,
      retryable = false,
      onRetry = null,
      retryKey = null, // 用于标识重试的唯一键
    } = options;

    // 解析错误信息
    const errorInfo = this.parseError(error);

    // 记录错误
    this.logError(error, 'API_ERROR');

    // 处理重试逻辑
    if (retryable && onRetry && retryKey) {
      const currentRetries = this.retryCount.get(retryKey) || 0;
      if (currentRetries < this.maxRetries) {
        this.retryCount.set(retryKey, currentRetries + 1);

        // 显示重试提示
        if (showMessage) {
          message.warning(`操作失败，正在重试... (${currentRetries + 1}/${this.maxRetries})`);
        }

        // 延迟重试
        setTimeout(() => {
          onRetry();
        }, this.retryDelay * (currentRetries + 1)); // 递增延迟

        return errorInfo;
      } else {
        // 重试次数用完，清除计数
        this.retryCount.delete(retryKey);
      }
    }

    // 显示用户友好的错误信息
    if (showMessage) {
      this.showErrorMessage(errorInfo, customMessage);
    }

    if (showNotification) {
      this.showErrorNotification(errorInfo, retryable, onRetry);
    }

    return errorInfo;
  }

  /**
   * 解析错误信息
   * @param {Error} error - 错误对象
   * @returns {Object} 解析后的错误信息
   */
  parseError(error) {
    let errorInfo = {
      type: 'UNKNOWN_ERROR',
      message: '发生未知错误',
      code: null,
      status: null,
      retryable: false,
    };

    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response;
      errorInfo.status = status;
      errorInfo.code = data?.code || status;

      switch (status) {
        case 400:
          errorInfo.type = 'BAD_REQUEST';
          errorInfo.message = data?.error || data?.message || '请求参数错误';
          break;
        case 401:
          errorInfo.type = 'UNAUTHORIZED';
          errorInfo.message = data?.error || data?.message || '登录已过期，请重新登录';
          this.handleAuthError();
          break;
        case 403:
          errorInfo.type = 'FORBIDDEN';
          errorInfo.message = data?.error || data?.message || '没有权限执行此操作';
          break;
        case 404:
          errorInfo.type = 'NOT_FOUND';
          errorInfo.message = data?.error || data?.message || '请求的资源不存在';
          break;
        case 422:
          errorInfo.type = 'VALIDATION_ERROR';
          errorInfo.message = data?.message || '数据验证失败';
          break;
        case 429:
          errorInfo.type = 'RATE_LIMIT';
          errorInfo.message = '请求过于频繁，请稍后重试';
          errorInfo.retryable = true;
          break;
        case 500:
          errorInfo.type = 'SERVER_ERROR';
          errorInfo.message = '服务器内部错误，请稍后重试';
          errorInfo.retryable = true;
          break;
        case 502:
        case 503:
        case 504:
          errorInfo.type = 'SERVICE_UNAVAILABLE';
          errorInfo.message = '服务暂时不可用，请稍后重试';
          errorInfo.retryable = true;
          break;
        default:
          errorInfo.message = data?.message || `服务器错误 (${status})`;
          errorInfo.retryable = status >= 500;
      }
    } else if (error.request) {
      // 网络错误
      errorInfo.type = 'NETWORK_ERROR';
      errorInfo.message = '网络连接失败，请检查网络设置';
      errorInfo.retryable = true;
    } else {
      // 其他错误
      errorInfo.message = error.message || '发生未知错误';
    }

    return errorInfo;
  }

  /**
   * 显示错误消息
   * @param {Object} errorInfo - 错误信息
   * @param {string} customMessage - 自定义消息
   */
  showErrorMessage(errorInfo, customMessage) {
    const messageText = customMessage || errorInfo.message;

    switch (errorInfo.type) {
      case 'NETWORK_ERROR':
        message.error({
          content: messageText,
          icon: <WifiOutlined />,
          duration: 5,
        });
        break;
      case 'UNAUTHORIZED':
        message.warning({
          content: messageText,
          duration: 5,
        });
        break;
      default:
        message.error({
          content: messageText,
          duration: 4,
        });
    }
  }

  /**
   * 显示错误通知
   * @param {Object} errorInfo - 错误信息
   * @param {boolean} retryable - 是否可重试
   * @param {Function} onRetry - 重试回调
   */
  showErrorNotification(errorInfo, retryable, onRetry) {
    const config = {
      message: '操作失败',
      description: errorInfo.message,
      icon: <CloseCircleOutlined style={{ color: '#ff4d4f' }} />,
      duration: 6,
    };

    if (retryable && onRetry && errorInfo.retryable) {
      config.btn = (
        <button
          className="ant-btn ant-btn-primary ant-btn-sm"
          onClick={() => {
            notification.close(config.key);
            this.handleRetry(onRetry);
          }}
        >
          重试
        </button>
      );
      config.key = `error_${Date.now()}`;
    }

    notification.error(config);
  }

  /**
   * 处理认证错误
   */
  handleAuthError() {
    // 清除本地存储的认证信息
    localStorage.removeItem('token');
    localStorage.removeItem('user');

    // 延迟跳转到登录页面，给用户时间看到错误信息
    setTimeout(() => {
      window.location.href = '/login';
    }, 2000);
  }

  /**
   * 处理重试
   * @param {Function} retryFn - 重试函数
   */
  async handleRetry(retryFn) {
    const retryKey = retryFn.toString();
    const currentRetries = this.retryCount.get(retryKey) || 0;

    if (currentRetries >= this.maxRetries) {
      message.error('重试次数已达上限，请稍后再试');
      this.retryCount.delete(retryKey);
      return;
    }

    this.retryCount.set(retryKey, currentRetries + 1);

    try {
      // 延迟重试
      await new Promise(resolve => setTimeout(resolve, this.retryDelay * (currentRetries + 1)));

      const result = await retryFn();

      // 重试成功，清除重试计数
      this.retryCount.delete(retryKey);
      message.success('操作成功');

      return result;
    } catch (error) {
      // 重试失败，继续处理错误
      this.handleApiError(error, {
        showMessage: true,
        retryable: true,
        onRetry: retryFn,
      });
    }
  }

  /**
   * 记录错误日志
   * @param {Error} error - 错误对象
   * @param {string} type - 错误类型
   */
  logError(error, type = 'GENERAL_ERROR') {
    const errorLog = {
      type,
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
    };

    // 只在开发环境下输出到控制台，不发送到后端
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 ${type}`);
      console.error('Error:', error);
      console.error('Error Info:', errorLog);
      console.groupEnd();
    }

    // 可选：存储到本地存储用于调试
    try {
      const localErrors = JSON.parse(localStorage.getItem('frontend_errors') || '[]');
      localErrors.push(errorLog);

      // 只保留最近10个错误
      if (localErrors.length > 10) {
        localErrors.splice(0, localErrors.length - 10);
      }

      localStorage.setItem('frontend_errors', JSON.stringify(localErrors));
    } catch (storageError) {
      console.warn('Failed to store error log locally:', storageError);
    }
  }

  /**
   * 检查网络连接状态
   * @returns {boolean} 是否在线
   */
  isOnline() {
    return navigator.onLine;
  }

  /**
   * 监听网络状态变化
   */
  setupNetworkMonitoring() {
    window.addEventListener('online', () => {
      message.success('网络连接已恢复');
    });

    window.addEventListener('offline', () => {
      message.warning({
        content: '网络连接已断开',
        icon: <WifiOutlined />,
        duration: 0, // 不自动关闭
      });
    });
  }
}

// 创建全局错误处理器实例
const errorHandler = new ErrorHandler();

// 设置网络监控
errorHandler.setupNetworkMonitoring();

export default errorHandler;
