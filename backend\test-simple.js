const express = require('express');
const cors = require('cors');
const path = require('path');

console.log('Starting enhanced test server...');

// 加载环境变量
require('dotenv').config({ path: path.join(__dirname, '.env') });

const app = express();
const PORT = 3000; // 使用3000端口匹配前端配置

// 中间件
app.use(cors());
app.use(express.json());

// 测试路由
app.get('/', (req, res) => {
    console.log('Root accessed');
    res.json({
        message: '法务案件管理平台 API',
        version: '1.0.0',
        status: 'running'
    });
});

app.get('/health', (req, res) => {
    console.log('Health check');
    res.json({
        status: 'healthy',
        database: 'connected',
        timestamp: new Date().toISOString()
    });
});

// 完整登录API（使用数据库）
app.post('/api/auth/login', async (req, res) => {
    console.log('Login attempt:', req.body);
    const { username, password } = req.body;

    try {
        // 导入数据库模型
        const { User, Role } = require('./models');
        const JWTUtils = require('./utils/jwt');

        // 验证输入
        if (!username || !password) {
            return res.status(400).json({
                error: 'Username and password are required',
                code: 'MISSING_CREDENTIALS'
            });
        }

        // 查找用户（包含角色信息）
        const user = await User.findOne({
            where: { username },
            include: [{
                model: Role,
                as: 'roles',
                attributes: ['id', 'name', 'description']
            }]
        });

        if (!user) {
            return res.status(401).json({
                error: 'Invalid username or password',
                code: 'INVALID_CREDENTIALS'
            });
        }

        // 检查用户状态
        if (user.status !== 1) {
            return res.status(401).json({
                error: 'User account is disabled',
                code: 'USER_DISABLED'
            });
        }

        // 验证密码
        const isPasswordValid = await user.validatePassword(password);
        if (!isPasswordValid) {
            return res.status(401).json({
                error: 'Invalid username or password',
                code: 'INVALID_CREDENTIALS'
            });
        }

        // 生成JWT token
        const payload = JWTUtils.createUserPayload(user);
        const token = JWTUtils.generateToken(payload);

        // 返回成功响应
        res.json({
            success: true,
            data: {
                token,
                user: {
                    id: user.id,
                    username: user.username,
                    real_name: user.real_name,
                    email: user.email,
                    roles: user.roles
                }
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            error: 'Internal server error',
            code: 'SERVER_ERROR'
        });
    }
});

// 简单的案件列表API
app.get('/api/cases', (req, res) => {
    console.log('Get cases request');
    res.json({
        success: true,
        data: {
            cases: [],
            total: 0,
            page: 1,
            limit: 10
        }
    });
});

// 简单的统计API
app.get('/api/stats/overview', (req, res) => {
    console.log('Get stats request');
    res.json({
        success: true,
        data: {
            overview: {
                totalCases: 1,
                newCasesThisMonth: 1,
                upcomingCases: 0,
                unreadNotifications: 0
            },
            casesByStatus: {
                '待处理': 1,
                '处理中': 0,
                '已结案': 0,
                '已归档': 0
            }
        }
    });
});

// 启动服务器
app.listen(PORT, '127.0.0.1', () => {
    console.log(`Enhanced test server running on http://127.0.0.1:${PORT}`);
    console.log(`📍 API URL: http://127.0.0.1:${PORT}`);
    console.log(`🏥 Health check: http://127.0.0.1:${PORT}/health`);
    console.log(`🔑 Login API: http://127.0.0.1:${PORT}/api/auth/login`);
});
