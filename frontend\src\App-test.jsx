import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

// 导入组件
import ErrorBoundary from './components/ErrorBoundary/ErrorBoundary';
import { getToken } from './utils/auth';
import Login from './pages/Auth/Login';
import Dashboard from './pages/Dashboard/Dashboard-simple';
import Layout from './components/Layout/Layout';

// 设置dayjs中文
dayjs.locale('zh-cn');

console.log('App-test.jsx 开始加载');

// 私有路由组件
const PrivateRoute = ({ children }) => {
  console.log('PrivateRoute 检查认证状态');
  const token = getToken();
  console.log('Token:', token ? '存在' : '不存在');
  return token ? children : <Navigate to="/login" replace />;
};

// 公共路由组件
const PublicRoute = ({ children }) => {
  console.log('PublicRoute 检查认证状态');
  const token = getToken();
  console.log('Token:', token ? '存在' : '不存在');
  return token ? <Navigate to="/dashboard" replace /> : children;
};

// 测试简单组件
const TestLogin = () => {
  console.log('TestLogin 组件渲染');
  return (
    <div style={{ padding: '50px', textAlign: 'center' }}>
      <h1>登录页面</h1>
      <p>这是一个测试登录页面</p>
      <button onClick={() => {
        localStorage.setItem('token', 'test-token');
        window.location.href = '/dashboard';
      }}>
        模拟登录
      </button>
    </div>
  );
};

const TestDashboard = () => {
  console.log('TestDashboard 组件渲染');
  return (
    <div style={{ padding: '50px', textAlign: 'center' }}>
      <h1>仪表板</h1>
      <p>这是一个测试仪表板页面</p>
      <button onClick={() => {
        localStorage.removeItem('token');
        window.location.href = '/login';
      }}>
        退出登录
      </button>
    </div>
  );
};

function App() {
  console.log('App-test 组件开始渲染');

  try {
    return (
      <ConfigProvider locale={zhCN}>
        <ErrorBoundary>
          <div className="App">
            <Router>
              <Routes>
                {/* 公共路由 */}
                <Route
                  path="/login"
                  element={
                    <PublicRoute>
                      <Login />
                    </PublicRoute>
                  }
                />

                {/* 私有路由 */}
                <Route
                  path="/"
                  element={
                    <PrivateRoute>
                      <Layout />
                    </PrivateRoute>
                  }
                >
                  {/* 默认重定向到仪表板 */}
                  <Route index element={<Navigate to="/dashboard" replace />} />

                  {/* 仪表板 */}
                  <Route path="dashboard" element={<Dashboard />} />
                </Route>

                {/* 404 页面 */}
                <Route path="*" element={<Navigate to="/dashboard" replace />} />
              </Routes>
            </Router>
          </div>
        </ErrorBoundary>
      </ConfigProvider>
    );
  } catch (error) {
    console.error('App-test 渲染错误:', error);
    return (
      <div style={{ padding: '20px', color: 'red' }}>
        <h1>App-test 渲染错误</h1>
        <p>{error.message}</p>
      </div>
    );
  }
}

export default App;
